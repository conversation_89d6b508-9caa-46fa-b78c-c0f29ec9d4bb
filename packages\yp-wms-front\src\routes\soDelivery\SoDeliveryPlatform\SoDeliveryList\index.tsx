/**
 * @Description: 销售发运平台 - 入口页面
 * @Author: <EMAIL>
 * @Date: 2022/2/10 15:08
 */
import React, { useEffect, useMemo, useState } from 'react';
import { Button, DataSet, Dropdown, Menu, Modal, Table } from 'choerodon-ui/pro';
import { Badge, Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import moment from 'moment';
import intl from 'utils/intl';
import { FRPrintButton } from '@components/tarzan-ui';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import request from 'utils/request';
import notification from 'utils/notification';
import { getResponse } from '@utils/utils';
import myInstance from '@utils/myAxios';
import { BASIC , API_HOST } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import ExcelExport from 'components/ExcelExport';
import { headerTableDS, lineTableDS } from '../stories/EntranceDS';
import { headDS, tableDS } from '../stories/MaterialBatchDS';
import { drawerTableDS, LabelTableDS } from '../stories/DrawerTableDS';
import CreateMaterial from './CreateMaterial';
import styles from './index.module.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.soDelivery.soDeliveryPlatform';
const Host = `${BASIC.HMES_BASIC}`

let modalMaterial; // 创建物料批的modal
let modalMaterialDetail; // 物料批明细的modal
let modalLabelDetail; // 标签明细的modal

const InboundOrderQueryList = props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
  } = props;
  const [cancelFlag, setCancelFlag] = useState<boolean>(false); // 控制状态变更显示取消
  const [closeFlag, setCloseFlag] = useState<boolean>(false); // 控制状态变更显示关闭
  const [selectedInstructionDocIds, setSelectedInstructionDocIds] = useState<any[]>([]);
  const [selectTagDelivery, setSelectTagDelivery] = useState<boolean>(false); // 控制状态变更显示关闭
  // 动态列实际columns
  const headDs = useMemo(() => new DataSet(headDS()), []); // 创建物料批-头ds
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 创建物料批-行ds
  const drawerTableDs: DataSet = useMemo(() => new DataSet(drawerTableDS()), []);
  const LabelTableDs: DataSet = useMemo(() => new DataSet(LabelTableDS()), []);
  // 保存上一次创建成功后的物料批
  const [batchListsArr, setBatchListsArr] = useState([]);

  const { run: handleChangeStatus, loading: changeStatusLoading } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-delivery-platform/product/deliver/status/alter/for/ui`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: handleEstablishMaterial, loading: changeEstablishLoading } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-delivery-platform/material-lot-create/ui`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  useEffect(() => {
    if (tableDs) {
      tableDs.addEventListener('batchSelect', handleSelect);
      tableDs.addEventListener('batchUnSelect', handleSelect);
    }
    return () => {
      if (tableDs) {
        tableDs.removeEventListener('batchSelect', handleSelect);
        tableDs.removeEventListener('batchUnSelect', handleSelect);
      }
    };
  }, []);

  const handleSelect = () => {
    const selected: any = tableDs.selected.map(item => {
      return item.get('materialLotId');
    });
    modalMaterial.update({
      title: createModalTitle(selected),
    });
  };

  // 物料批弹窗上的删除按钮，用来删除与指令的关系
  const handleDelete = selected => {
    // @ts-ignore
    const instructionDocLineId = headDs.toData()[0].instructionDocLineId;
    const data = {
      instructionDocLineId,
      materialLotIdList: selected,
    };
    const url = `${
      BASIC.HMES_BASIC
    }/v1/${getCurrentOrganizationId()}/mt-product-delivery-platform/material-lot-remove/ui`;
    return myInstance.post(url, data).then(async res => {
      if (res?.data?.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
        headDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
        const list = await headDs.query();
        if (list?.success) {
          // 新建物料批成功后更新弹框里的头，和行列表
          tableDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
          await tableDs.query();
          headDs.current!.set('productionDate', moment().format('YYYY-MM-DD HH:mm:ss'));
          toggleOkDisabled(instructionDocLineId, batchListsArr);
        } else {
          notification.error({
            message: list?.data?.message,
          });
        }
      } else {
        notification.error({
          message: res?.data?.message,
        });
      }
    });
  };

  const createModalTitle = (selected = []) => {
    return (
      <div>
        <span style={{ fontSize: '14px' }}>
          {intl.get(`${modelPrompt}.create.materialBatch`).d('创建物料批')}
        </span>
        <div
          style={{
            float: 'right',
            display: 'flex',
            flexDirection: 'row-reverse',
            alignItems: 'center',
            marginRight: '0.3rem',
          }}
        >
          <PermissionButton
            style={{
              marginLeft: '0.1rem',
              marginRight: '0.2rem',
            }}
            icon="save"
            type="c7n-pro"
            color={ButtonColor.primary}
            loading={changeEstablishLoading}
            onClick={() => handleEstablish(lineTableDs?.current?.get('instructionDocLineId'))}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.create.establish`).d('创建')}
          </PermissionButton>
          <PermissionButton
            style={{
              marginLeft: '0.1rem',
              marginRight: '0.2rem',
            }}
            icon="delete"
            type="c7n-pro"
            color={ButtonColor.default}
            onClick={() => handleDelete(selected)}
            disabled={selected.length === 0}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.delete').d('删除')}
          </PermissionButton>
          <FRPrintButton
            kid="MATERIAL_LOT_PRINT" // 使用打印组件的功能的唯一标识，业务提供
            queryParams={selected} // 查询数据
            disabled={selected.length === 0}
            printObjectType="MATERIAL_LOT"
          />
        </div>
      </div>
    );
  };

  // 进来不查询，当从详情返回后查询当前页面
  useEffect(() => {
    if (headerTableDs.toData().length > 0) {
      headerTableDs.query(headerTableDs.currentPage);
    }
  }, []);

  // 当保存成功回到页面时自动重查数据
  useEffect(() => {
    // 当在页签上右键刷新时，如果当前表格有勾选数据时，需要随之变按钮禁用状态
    handleDataSetSelectUpdate();
    if (props?.location?.state && props?.location?.state?.queryFlag === true) {
      headerTableDs.query(headerTableDs.currentPage);
    }
  }, []);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'batchSelect', handleDataSetSelectUpdate);
      handler.call(headerTableDs, 'batchUnSelect', handleDataSetSelectUpdate);
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    handleDataSetSelectUpdate();
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable(null, null);
    }
  };

  const headerRowClick = record => {
    queryLineTable(record?.get('instructionDocId'), record?.get('instructionDocType'));
  };

  // 行列表数据查询
  const queryLineTable = (instructionDocId, instructionDocType) => {
    if (instructionDocId) {
      lineTableDs.setQueryParameter('instructionDocId', instructionDocId);
      lineTableDs.setQueryParameter('instructionDocType', instructionDocType);
    } else {
      lineTableDs.setQueryParameter('instructionDocId', 0);
    }
    lineTableDs.query();
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    const _selectedStatus: string[] = [];
    const _instructionDocIds: string[] = [];
    // 勾选的都是下达状态，状态变更可点击，且下拉显示取消
    const cancelList: string[] = ['RELEASED'];
    // 这几种状态时，状态变更可点击，且下拉显示关闭
    const closeList: string[] = [
      'PROCESSING',
      '1_PROCESSING',
      '1_COMPLETED',
      '2_PROCESSING',
      'COMPLETED',
    ];
    // 汇总头表已勾选的单据状态
    headerTableDs.selected.forEach(item => {
      const { instructionDocStatus, instructionDocId } = item.toData();
      _instructionDocIds.push(instructionDocId);
      if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
        _selectedStatus.push(instructionDocStatus);
      }
    });
    setSelectedInstructionDocIds(_instructionDocIds);
    if(headerTableDs.selected.length === 1){
      const _tagDelivery = headerTableDs.selected.every(item => item.toData()?.instructionDocTypeTag === 'DELIVERY' && !item.toData()?.instructionDocStatusDepict);
      // 标记为DELIVERY且描述为空
      setSelectTagDelivery(_tagDelivery);
    }else{
      setSelectTagDelivery(false);
    }

    if (_selectedStatus.length > 0) {
      setCancelFlag(true);
      setCloseFlag(true);
      _selectedStatus.forEach(item => {
        if (cancelList.indexOf(item) === -1) {
          setCancelFlag(false);
        }
        if (closeList.indexOf(item) === -1) {
          setCloseFlag(false);
        }
      });
    } else {
      setCancelFlag(false);
      setCloseFlag(false);
    }
  };

  // 点击状态变更的回调
  const clickMenu = async key => {
    const selectItems: any = [];
    headerTableDs.selected.forEach(item => {
      const _selectItem = {
        instructionDocId: item?.toData()?.instructionDocId,
        instructionDocStatus: item?.toData()?.instructionDocStatus,
        instructionDocType: item?.toData()?.instructionDocType,
      };
      selectItems.push({ ..._selectItem });
    });

    return handleChangeStatus({
      params: {
        instructionDocList: selectItems,
        alterStatus: key,
      },
    }).then(res => {
      if (res && res.success) {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        notification.success({});
        setCancelFlag(false);
        setCloseFlag(false);
        headerTableDs.query(headerTableDs.currentPage);
      }
    });
  };

  const deliveryTableColumns: ColumnProps[] = [
    { name: 'identification', width: 150 },
    { name: 'materialLotStatusDesc', width: 100 },
    { name: 'carType' },
    { name: 'labelCode' },
    { name: 'vinCode' },
    { name: 'seq' },
    { name: 'paoffTime', width: 150 },
    { name: 'identifyCode' },
    { name: 'containerIdentification', width: 150 },
    { name: 'qty', align: ColumnAlign.right },
    { name: 'uomCode' },
    { name: 'lot' },
    { name: 'locatorCode', width: 150 },
    { name: 'prepareDate', width: 150, align: ColumnAlign.center },
    { name: 'prepareRealName' },
    { name: 'deliveryDate', width: 150, align: ColumnAlign.center },
    { name: 'deliveryPerson' },
  ];

  const returnTableColumns: ColumnProps[] = [
    { name: 'identification', width: 150 },
    { name: 'materialLotStatusDesc', width: 100 },
    { name: 'carType' },
    { name: 'labelCode' },
    { name: 'vinCode' },
    { name: 'seq' },
    { name: 'paoffTime', width: 150 },
    { name: 'identifyCode' },
    { name: 'containerIdentification', width: 150 },
    { name: 'qty', align: ColumnAlign.right },
    { name: 'uomCode' },
    { name: 'lot' },
    { name: 'locatorCode', width: 150 },
    { name: 'executeDate', width: 150, align: ColumnAlign.center },
    { name: 'executePerson' },
  ];

  // 点击行上“物料批明细”的回调
  const goLotDetail = record => {
    drawerTableDs.setQueryParameter('instructionDocLineId', record?.get('instructionDocLineId'));
    drawerTableDs.setQueryParameter(
      'instructionDocType',
      headerTableDs?.current?.get('instructionDocType'),
    );
    drawerTableDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_MATERIAL_LOT.QUERY`,
    );
    drawerTableDs.query();
    modalMaterialDetail = Modal.open({
      title: intl.get(`${modelPrompt}.title.materialLotDetail`).d('物料批明细'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: customizeTable(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_MATERIAL_LOT.QUERY`,
        },
        <Table
          dataSet={drawerTableDs}
          columns={
            headerTableDs?.current?.get('instructionDocTypeTag') === 'RETURN'
              ? returnTableColumns
              : deliveryTableColumns
          }
        />,
      ),
      footer: (
        <Button
          style={{ float: 'right' }}
          onClick={() => {
            modalMaterialDetail.close();
          }}
        >
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  const LabelTableColumns: ColumnProps[] = [
    { name: 'carType' },
    { name: 'labelCode' },
    { name: 'vinCode' },
    { name: 'seq' },
    { name: 'paoffTime', width: 150 },
    { name: 'identifyCode' },
  ];

  // 点击行上“标签明细”的回调
  const goLabelDetail = record => {
    LabelTableDs.setQueryParameter('instructionDocLineId', record?.get('instructionDocLineId'));
    LabelTableDs.setQueryParameter(
      'instructionDocType',
      headerTableDs?.current?.get('instructionDocType'),
    );
    LabelTableDs.setQueryParameter(
      'customizeUnitCode',
      `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_MATERIAL_LOT.QUERY`,
    );
    LabelTableDs.query();
    modalLabelDetail = Modal.open({
      title: intl.get(`${modelPrompt}.title.modalLabelDetail`).d('标签明细'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: customizeTable(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_MATERIAL_LOT.QUERY`,
        },
        <Table dataSet={LabelTableDs} columns={LabelTableColumns} />,
      ),
      footer: (
        <Button
          style={{ float: 'right' }}
          onClick={() => {
            modalLabelDetail.close();
          }}
        >
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 创建物料批
  const handleCreateMaterial = async record => {
    const instructionDocLineId = record?.get('instructionDocLineId');
    headDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
    const res = await headDs.query();
    headDs.current!.set('productionDate', moment().format('YYYY-MM-DD HH:mm:ss'));
    if (res.success) {
      modalMaterial = Modal.open({
        title: createModalTitle([]),
        className: 'hmes-style-modal',
        maskClosable: true,
        destroyOnClose: true,
        drawer: true,
        closable: true,
        onClose: () => {
          tableDs.batchUnSelect(tableDs.selected);
        },
        style: {
          width: 720,
        },
        children: (
          <CreateMaterial
            headDs={headDs}
            tableDs={tableDs}
            instructionDocLineId={instructionDocLineId}
            batchList={[]}
            customizeTable={customizeTable}
          />
        ),
        footer: (
          <Button onClick={() => modalMaterial.close()}>
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
        ),
      });
    } else {
      notification.error({
        message: res?.message,
      });
    }
  };

  // 物料批创建
  const handleEstablish = async instructionDocLineId => {
    const validate = await headDs.validate();
    if (validate) {
      const data = {
        ...headDs?.current?.toData(),
      };
      return handleEstablishMaterial({
        params: data,
      }).then(async res => {
        if (res?.success) {
          const batchLists = res?.rows;
          // 保存新生成的物料批，删除物料批的时候会用到
          setBatchListsArr(batchLists);
          headDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
          const list = await headDs.query();
          if (list?.success) {
            notification.success({});
            // 新建物料批成功后更新弹框里的头，和行列表
            tableDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
            await tableDs.query();
            headDs.current!.set('productionDate', moment().format('YYYY-MM-DD HH:mm:ss'));
            toggleOkDisabled(instructionDocLineId, batchLists);
          } else {
            notification.error({
              message: list?.data?.message,
            });
          }
        }
      });
    }
  };

  const getQueryParams=()=>{
    const queryData=headerTableDs.queryDataSet?.current?.toJSONData()
    const selectedData=headerTableDs.selected.map(ele=>ele.toJSONData()).map(ele=>ele.instructionDocId)
    if(selectedData.length>0){
      return {
        instructionDocIdList: selectedData,
      }
    }
    return queryData
  }

  // 更新物料批
  const toggleOkDisabled = (instructionDocLineId, batchLists) => {
    modalMaterial.update({
      children: (
        <CreateMaterial
          headDs={headDs}
          tableDs={tableDs}
          instructionDocLineId={instructionDocLineId}
          batchList={batchLists}
          customizeTable={customizeTable}
        />
      ),
    });
  };

  const headerTableColumns: ColumnProps[] = [
    {
      name: 'instructionDocNum',
      lock: ColumnLock.left,
      width: 180,
      renderer: ({ record, value }) => {
        if (record?.get('instructionDocStatus') === 'RELEASED') {
          return (
            <a
              onClick={() => {
                headerTableDs.batchUnSelect(headerTableDs.selected);
                props.history.push({
                  pathname: `/hwms/so-delivery/so-delivery-platform-new/detail/${record?.get(
                    'instructionDocId',
                  )}`,
                  state: {
                    executionType: '',
                  },
                })
              }}
            >
              {value}
            </a>
          );
        }
        return value;
      },
    },
    { name: 'instructionDocStatusDesc' },
    { name: 'instructionDocTypeDesc' },
    { name: 'sourceSystemDesc' },
    { name: 'siteCode', width: 180 },
    { name: 'customerName', width: 150 },
    { name: 'contactAddress', width: 200 },
    {
      name: 'demandTime',
      width: 150,
      align: ColumnAlign.center,
    },
    {
      name: 'expectedArrivalTime',
      width: 150,
      align: ColumnAlign.center,
    },
    { name: 'remark' },
    { name: 'contactPerson' },
    { name: 'contactTel' },
    { name: 'contactFax' },
    { name: 'logisticsMode' },
    { name: 'logisticsCompanyCode' },
    { name: 'logisticsCompanyDesc' },
    { name: 'paymentType', width: 150 },
    { name: 'sapSoNum' },
    { name: 'createdByName' },
    {
      name: 'creationDate',
      width: 150,
      align: ColumnAlign.center,
    },
  ];

  const lineTableColumns: ColumnProps[] = [
    { name: 'lineNumber', lock: ColumnLock.left, align: ColumnAlign.left },
    {
      name: 'identifyType',
      lock: ColumnLock.left,
      width: 120,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        }
        if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    { name: 'materialCode', lock: ColumnLock.left, width: 180 },
    { name: 'revisionCode', lock: ColumnLock.left },
    { name: 'materialName', width: 150 },
    { name: 'needQty', align: ColumnAlign.right },
    { name: 'uomCode' },
    { name: 'statusDesc' },
    { name: 'soNumber' },
    { name: 'soLineNumber', width: 150 },
    {
      name: 'orderFlag',
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    { name: 'targetSiteCode', width: 150 },
    { name: 'targetLocatorCode', width: 150 },
    { name: 'prepareQty', align: ColumnAlign.right },
    { name: 'deliveryQty', align: ColumnAlign.right },
    { name: 'receivingQty', align: ColumnAlign.right },
    { name: 'remark' },
    {
      name: 'toleranceFlag',
      align: ColumnAlign.center,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    { name: 'toleranceTypeDesc' },
    { name: 'toleranceMaxValue', align: ColumnAlign.right },
    { name: 'toleranceMinValue', align: ColumnAlign.right },
    {
      header: intl.get(`${modelPrompt}.column.operation`).d('操作列'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      width: 220,
      renderer: ({ record }) => {
        return (
          <span>
            {headerTableDs?.current?.get('instructionDocTypeTag') === 'RETURN' ? (
              <a onClick={() => handleCreateMaterial(record)}>
                {intl.get(`${modelPrompt}.create.materialBatch`).d('创建物料批')}
              </a>
            ) : (
              <span>{intl.get(`${modelPrompt}.create.materialBatch`).d('创建物料批')}</span>
            )}
            <a onClick={() => goLotDetail(record)}>
              {intl.get(`${modelPrompt}.materialLotDetail`).d('物料批明细')}
            </a>
            <a onClick={() => goLabelDetail(record)} style={{ marginLeft: '5px' }}>
              {intl.get(`${modelPrompt}.labelDetail`).d('标签明细')}
            </a>
          </span>
        );
      },
    },
  ];

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '100px' }}>
      <Menu.Item disabled={!cancelFlag} key="CANCEL">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CANCEL')}>
          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
        </a>
      </Menu.Item>
      <Menu.Item disabled={!closeFlag} key="CLOSED">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CLOSED')}>
          {intl.get(`${modelPrompt}.button.close`).d('关闭')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const handlePrint = async () => {
    const result = await request(
      `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wmsProductDelivery/print/pdf`,
      {
        method: 'POST',
        responseType: 'blob',
        body: {
          instructionDocId: selectedInstructionDocIds,
        },
      },
    );
    const res = getResponse(result);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader: any = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: intl.get(`${modelPrompt}.notification.print.success`).d('打印成功'),
          });
        } else {
          notification.error({ message: intl.get(`${modelPrompt}.notification.browser.config`).d('当前窗口已被浏览器拦截，请手动设置浏览器！') });
        }
      }
    }
  };

  const handleExecution = async () => {
    props.history.push({
      pathname: `/hwms/so-delivery/so-delivery-platform-new/detail/${headerTableDs.selected[0]?.get('instructionDocId')}`,
      state: {
        executionType: 'execution',
      },
    })
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.soDeliveryPlatform`).d('销售发运平台')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-product-delivery-platform/instruction-doc/export`}
          queryParams={getQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() =>
            props.history.push({
              pathname: `/hwms/so-delivery/so-delivery-platform-new/detail/create`,
              state: {
                executionType: '',
              },
            })
          }
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.create`).d('创建单据')}
        </PermissionButton>
        <Dropdown
          overlay={menu}
          disabled={!(headerTableDs.selected.length > 0 && (closeFlag || cancelFlag))}
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={!(headerTableDs.selected.length > 0 && (closeFlag || cancelFlag))}
            loading={changeStatusLoading}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>

        <Button
          onClick={handlePrint}
          disabled={!selectedInstructionDocIds.length}
          color={ButtonColor.primary}
        >
          {intl.get(`${modelPrompt}.button.print`).d('打印')}
        </Button>
        <Button
          onClick={handleExecution}
          disabled={!selectTagDelivery}
          color={ButtonColor.primary}
        >
          {intl.get(`${modelPrompt}.button.execution`).d('补单执行')}
        </Button>
        {/* <FRPrintButton
          kid="SO_DELIVERT&RETURN"
          queryParams={selectedInstructionDocIds}
          disabled={!selectedInstructionDocIds.length}
          printObjectType="INSTRUCTION_DOC"
        /> */}
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_LIST.HEAD`,
          },
          <Table
            searchCode="xsfypt1"
            customizedCode="xsfypt1"
            dataSet={headerTableDs}
            queryFieldsLimit={13}
            columns={headerTableColumns}
            highLightRow
            showCachedSelection={false}
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            onRow={({ record }) => {
              return {
                onClick: () => {
                  headerRowClick(record);
                },
              };
            }}
            pagination={{
              showPager: true, // 显示数字按钮
              pageSizeOptions: ['10', '20', '50', '100', '200'],
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.line`).d('行信息')}
            key="basicInfo"
            dataSet={lineTableDs}
          >
            {lineTableDs &&
              customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_LIST.LINE`,
                },
                <Table
                  customizedCode="xsfypt2"
                  dataSet={lineTableDs}
                  highLightRow={false}
                  columns={lineTableColumns}
                />,
              )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_MATERIAL_LOT.CREATE`,
      `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_MATERIAL_LOT.QUERY`,
    ],
  }),
  formatterCollections({ code: ['tarzan.soDelivery.soDeliveryPlatform', 'tarzan.common'] }),
)(InboundOrderQueryList);
