/**
 * @Description: 创建送货单抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-12-13 10:31:13
 * @LastEditTime: 2022-05-31 14:40:17
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo, useImperativeHandle, forwardRef } from 'react';
import {
  DataSet,
  Form,
  TextField,
  Table,
  DatePicker,
  NumberField,
  Select,
  Switch,
  Button,
  Tabs,
  Tooltip,
  Icon,
  Spin,
} from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import { shippingDS, shippingListDS } from '../stores/CreateShippingDS';

const { TabPane } = Tabs;

const modelPrompt = 'tarzan.hmes.purchase.order';

const CreateShippingDrawer = (props, ref) => {
  const {
    visible,
    handleSuccess = () => {},
    deliverList = [],
    deliverheaderDetailDrawer = {},
  } = props;

  const shippingDs = useMemo(() => {
    return new DataSet(shippingDS());
  }, []);

  const shippingListDs = useMemo(() => {
    return new DataSet(shippingListDS());
  }, []);

  const getList = useRequest(
    {
      url: `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/mt-instruction-doc/po-create-delivery-validate/ui`,
      method: 'POST',
    },
    {
      manual: true,
    },
  );

  const submitForm = useRequest(
    {
      url: `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/mt-instruction-doc/po-create-delivery/ui`,
      method: 'POST',
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    shippingDs.loadData([
      {
        ...(deliverList[0] || {}),
        ...deliverheaderDetailDrawer,
        instructionDocType: 'DELIVERY_DOC',
      },
    ]);
    shippingListDs.loadData(
      deliverList.map((item, index) => {
        const _max = ((item.quantityOrdered || 0) - (item.processedOrdered || 0)).toFixed(6);
        return {
          ...item,
          urgentFlag: 'N',
          quantityMax: _max > 0 ? _max : 0,
          quantity: 0,
          instructionLineNumber: index * 10 + 10,
        };
      }),
    );
    shippingListDs.forEach(record => {
      record.set('newData', 'new');
    });
    shippingDs.forEach(record => {
      record.set('newData', 'new');
    });
    getListAuth('DELIVERY_DOC', shippingListDs.toData());
  }, [visible]);

  useImperativeHandle(ref, () => ({
    handleSubmit: async () => {
      const validate1 = await shippingListDs.validate();
      const validate2 = await shippingDs.validate();
      if (!validate1 || !validate2) {
        return false;
      }
      const headerData = shippingDs.toData()[0];
      const lines = [];
      shippingListDs.toData().forEach(item => {
        if (item.permissionFlag === 'Y') {
          lines.push(item);
        }
      });
      const {
        expectedArrivalTime,
        remark,
        instructionDocType,
        supplierSiteId,
        supplierId,
      } = headerData;
      const poIds = [];
      lines.forEach((item, index) => {
        if (poIds.indexOf(item?.poHeaderId) === -1) {
          poIds.push(item.poHeaderId);
        }
        lines[index].instructionLineNumber = index * 10 + 10;
      });
      const header = {
        expectedArrivalTime,
        remark,
        instructionDocType,
        supplierSiteId,
        supplierId,
        poIds,
      };
      return submitForm.run({
        params: {
          header,
          lines,
        },
        onSuccess: res => {
          notification.success();
          handleSuccess(res);
        },
      });
    },
  }));

  const getListAuth = (type, list) => {
    const _list = list.map(item => {
      const { instructionLineNumber, poLineId, receiveLocatorId } = item;
      return {
        instructionLineNumber,
        poLineId,
        receiveLocatorId,
      };
    });
    getList.run({
      params: {
        instructionDocType: type,
        lines: _list,
      },
      onSuccess: res => {
        shippingListDs.forEach((item, index) => {
          item.set('permissionFlag', res.lines?.[index]?.permissionFlag || '');
          item.set('toleranceFlag', res.lines?.[index]?.toleranceFlag || '');
          item.set('toleranceType', res.lines?.[index]?.toleranceType || '');
          item.set('toleranceMaxValue', res.lines?.[index]?.toleranceMaxValue);
          item.set('toleranceMinValue', res.lines?.[index]?.toleranceMinValue);
          item.set('businessType', res.lines?.[index]?.businessType);
          // toLocatorRequiredFlag在头部
          item.set('toLocatorRequiredFlag', res.toLocatorRequiredFlag)
          if (res.lines?.[index]?.permissionFlag === 'N') {
            item.set('locatorCode', "")
            item.set('receiveLocatorId', "")
          }
        });
      },
    });
  };

  const onTypeChange = () => {
    const instructionDocType = shippingDs.current.get('instructionDocType');
    if (instructionDocType) {
      getListAuth(instructionDocType, shippingListDs.toData());
    }
  };

  // 新建表格后可删除
  const handleDelete = record => {
    shippingListDs.delete(record, false);
    record.set('instructionLineNumber', undefined);
    setTimeout(() => {
      sortShippingList();
    });
  };

  // 行号重新排序
  const sortShippingList = () => {
    shippingListDs.forEach((record, index) => {
      record.set('instructionLineNumber', index * 10 + 10);
    });
  };

  const clearLerance = (record, keys) => {
    if (keys?.length > 0) {
      keys.forEach(item => {
        record.set(item, undefined);
      });
    }
  };
  const columns = [
    {
      title: <Button icon="add" disabled funcType="flat" shape="circle" size="small" />,
      align: 'center',
      width: 60,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDelete(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button icon="remove" funcType="flat" shape="circle" size="small" />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      name: 'instructionLineNumber',
      width: 60,
    },
    {
      name: 'identifyType',
      width: 120,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        } if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'materialCode',
      width: 180,
    },
    {
      name: 'revisionCode',
      width: 120,
    },
    {
      name: 'materialName',
      width: 180,
    },
    {
      name: 'siteCode',
      width: 160,
    },
    {
      name: 'quantityOrdered',
      width: 85,
    },
    {
      name: 'processedOrdered',
      width: 85,
    },
    {
      name: 'quantity',
      width: 85,
      editor: true,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'locatorLov',
      width: 180,
      editor: true,
    },
    {
      name: 'urgentFlag',
      width: 80,
      align: 'center',
      editor: () => <Switch />,
    },
    {
      name: 'toleranceFlag',
      width: 80,
      align: 'center',
      editor: record => (
        <Switch
          onChange={() => {
            clearLerance(record, ['toleranceType', 'toleranceMinValue', 'toleranceMaxValue']);
          }}
        />
      ),
    },
    {
      name: 'toleranceType',
      width: 140,
      editor: record => (
        <Select
          onChange={() => {
            clearLerance(record, ['toleranceMinValue', 'toleranceMaxValue']);
          }}
        />
      ),
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
      editor: () => <NumberField nonStrictStep precision={6} step={1} />,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
      editor: () => <NumberField nonStrictStep precision={6} step={1} />,
    },
    {
      name: 'poNumber',
      width: 120,
    },
    {
      name: 'lineNum',
      width: 95,
    },
    {
      name: 'soNum',
      width: 120,
    },
    {
      name: 'soLineNum',
      width: 95,
    },
    {
      name: 'demandDate',
      width: 180,
      align: 'center',
    },
  ];

  const tabARender = React.useCallback(() => {
    return (
      <>
        <span>{intl.get(`${modelPrompt}.line.information`).d('行信息')}</span>
        <span>
          <Tooltip
            placement="top"
            title={
              <span>
                {intl
                  .get(`${modelPrompt}.line.explain`)
                  .d(
                    '若您没有勾选的采购订单行上仓库的权限，该行将会为置灰状态，不能创建送货单，您可以前往人员仓库权限分配维护权限。',
                  )}
              </span>
            }
          >
            <Icon
              style={{
                position: 'relative',
                top: '-2px',
              }}
              type="help_outline"
            />
          </Tooltip>
        </span>{' '}
      </>
    );
  }, []);

  return (
    <Spin spinning={getList.loading || submitForm.loading}>
      <Form dataSet={shippingDs} columns={3} labelLayout="horizontal" labelWidth={110}>
        <TextField name="instructionDocCode" />
        <TextField name="supplierName" />
        <TextField name="supplierSiteName" />
        <Select name="instructionDocType" onChange={onTypeChange} />
        <DatePicker name="expectedArrivalTime" mode="dateTime" />
        <TextField name="remark" />
      </Form>
      <Tabs>
        <TabPane tab={tabARender}>
          <Table
            dataSet={shippingListDs}
            columns={columns}
            filter={record => {
              return record.status !== 'delete';
            }}
          />
        </TabPane>
      </Tabs>
    </Spin>
  );
};

export default forwardRef(CreateShippingDrawer);
