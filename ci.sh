#!/bin/bash
set -e

find dist -name '*.js' | xargs sed -i "s BUILD_BASE_PATH ${BUILD_BASE_PATH:-/} g"
find dist -name '*.js' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find dist -name '*.html' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find dist -name '*.css' | xargs sed -i "s /BUILD_PUBLIC_URL/ ${BUILD_PUBLIC_URL:-/} g"
find dist -name '*.js' | xargs sed -i "s BUILD_API_HOST $BUILD_API_HOST g"
find dist -name '*.js' | xargs sed -i "s BUILD_CLIENT_ID $BUILD_CLIENT_ID g"
find dist -name '*.js' | xargs sed -i "s BUILD_WEBSOCKET_HOST $BUILD_WEBSOCKET_HOST g"
find dist -name '*.js' | xargs sed -i "s BUILD_PLATFORM_VERSION $BUILD_PLATFORM_VERSION g"
find dist -name '*.js' | xargs sed -i "s BUILD_IM_ENABLE $BUILD_IM_ENABLE g"
find dist -name '*.js' | xargs sed -i "s BUILD_IM_WEBSOCKET_HOST $BUILD_IM_WEBSOCKET_HOST g"
find dist -name '*.js' | xargs sed -i "s BUILD_TRACE_LOG_ENABLE $BUILD_TRACE_LOG_ENABLE g"
find dist -name '*.js' | xargs sed -i "s BUILD_CUSTOMIZE_ICON_NAME $BUILD_CUSTOMIZE_ICON_NAME g"
	 
find dist -name '*.js' | xargs sed -i "s BUILD_HMES_BASIC /wms g"
find dist -name '*.js' | xargs sed -i "s BUILD_TARZAN_COMMON /tznc g"
find dist -name '*.js' | xargs sed -i "s BUILD_HRPT_COMMON /hrpt g"
find dist -name '*.js' | xargs sed -i "s BUILD_TARZAN_MODEL /tznm g"
find dist -name '*.js' | xargs sed -i "s BUILD_TARZAN_REPORT /tznr-wms g"
find dist -name '*.js' | xargs sed -i "s BUILD_TARZAN_METHOD /tznd g"
find dist -name '*.js' | xargs sed -i "s BUILD_TARZAN_SAMPLING /tznq g"
find dist -name '*.js' | xargs sed -i "s BUILD_TARZAN_HSPC /tzns g"
find dist -name '*.js' | xargs sed -i "s BUILD_APS_METHODTZND /tznd g"
find dist -name '*.js' | xargs sed -i "s BUILD_APS_COMMON /tznc g"
find dist -name '*.js' | xargs sed -i "s BUILD_APS_METHOD /aps g"
find dist -name '*.js' | xargs sed -i "s BUILD_POOL_QUERY query g"
find dist -name '*.js' | xargs sed -i "s BUILD_BASE_SERVERPLAN /aps-mltp g"
find dist -name '*.js' | xargs sed -i "s BUILD_BASE_SERVERPURCHASE /aps-purchase g"
find dist -name '*.js' | xargs sed -i "s BUILD_BASE_SERVER /aps g"
find dist -name '*.js' | xargs sed -i "s BUILD_CUSZ_CODE_BEFORE $BUILD_CUSZ_CODE_BEFORE g"
find dist -name '*.js' | xargs sed -i "s BUILD_LOV_CODE_BEFORE $BUILD_LOV_CODE_BEFORE g"
