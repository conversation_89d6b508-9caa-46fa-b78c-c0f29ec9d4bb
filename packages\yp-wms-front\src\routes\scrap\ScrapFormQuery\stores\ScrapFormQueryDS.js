// 报废单查询
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.ScrapFormQuery';
const tenantId = getCurrentOrganizationId();

// 头表格ds
const headTableDS = () => {
  return {
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: "multiple",
    paging: true,
    autoQuery: false,
    queryFields: [
      {
        name: 'siteObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.siteObj`).d('站点'),
        lovCode: 'MT.MODEL.SITE',
        ignore: 'always',
        lovPara: {
          tenantId,
        },
        required: true,
      },
      {
        name: 'siteId',
        bind: 'siteObj.siteId',
      },
      {
        name: 'instructionDocNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.instructionDocNum`).d('报废入库单号'),
      },
      {
        name: 'instructionDocStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS`,
        textField: 'description',
        valueField: 'statusCode',
        noCache: true,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'materialObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
        lovCode: 'MT.METHOD.MATERIAL',
        ignore: 'always',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'materialId',
        bind: 'materialObj.materialId',
      },
    ],
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
      },
      {
        name: 'instructionDocNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.instructionDocNum`).d('报废单号'),
      },
      {
        name: 'instructionDocStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.instructionDocStatusDesc`).d('单据状态'),
      },
      {
        name: 'costcenterCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.costcenterCode`).d('成本中心'),
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('创建人'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-scrap-docs/head/list/ui`,
          method: 'GET',
          data,
        };
      },
    },
  };
};

// 行表格ds
const lineTableDS = () => {
  return {
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'lineNumber',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      },
      {
        name: 'lineStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineStatusDesc`).d('行状态'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'quantity',
        type: 'string',
        label: intl.get(`${modelPrompt}.quantity`).d('需执行数量'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'sumActualQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.sumActualQty`).d('执行数量'),
      },
      {
        name: 'fromLocatorCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.fromLocatorCode`).d('来源仓库'),
      },
      {
        name: 'toLocatorCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.toLocatorCode`).d('目标仓库'),
      },
      {
        name: 'option',
        type: 'string',
        label: intl.get(`${modelPrompt}.option`).d('操作'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-scrap-docs/line/list/ui`,
          method: 'GET',
          data,
        };
      },
    },
  };
};

// 明细表格ds
const detailTableDS = () => {
  return {
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批标识'),
      },
      {
        name: 'materialLotStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotStatusDesc`).d('物料批状态'),
      },
      {
        name: 'containerCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
      },
      {
        name: 'primaryUomQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'lot',
        type: 'string',
        label: intl.get(`${modelPrompt}.lot`).d('批次'),
      },
      {
        name: 'scrapReason',
        type: 'string',
        label: intl.get(`${modelPrompt}.scrapReason`).d('报废原因'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-scrap-docs/detail/list/ui`,
          method: 'GET',
          data,
        };
      },
    },
  };
};

const createMaterialDS = () => ({
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  paging: false,
  autoQuery: false,
  fields: [
    {
      name: 'siteLov',
      type: 'object',
      lovCode: 'WMS.NOWORTH_SITE',
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
    },
    {
      name: 'materialLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      bind: 'materialLov.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'locatorLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      required: true,
      lovPara: {
        tenantId,
        locatorCategory: ['AREA'],
      },
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorName',
      bind: 'locatorLov.locatorName',
      label: intl.get(`${modelPrompt}.locatorName`).d('仓库描述'),
    },
    {
      
      name: 'weight',
      required: true,
      type: 'number',
      label: intl.get(`${modelPrompt}.weight`).d('重量'),
      validator: (value) => { // 校验器 自定义校验规则对内容进行校验
        if (value <= 0) {
          return intl.get(`${modelPrompt}.info.weight_min_zero`).d('重量需大于0');
        }
        return true;
      },
    },
    {
      name: 'number',
      required: true,
      type: 'number',
      label: intl.get(`${modelPrompt}.number`).d('个数'),
      validator: (value) => { // 校验器 自定义校验规则对内容进行校验
        if (!/^[1-9]\d*$/.test(value)) {
          return intl.get(`${modelPrompt}.info.number_min_zero`).d('个数需为正整数');
        }
        return true;
      },
    },
    {
      name: 'remark',
      type: 'string',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

const materialLotTableDS = () => ({
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: 'multiple',
  paging: false,
  autoQuery: false,
  fields: [
    {
      name: 'materialLotCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.tagCode`).d('标签条码号'),
    },
    {
      name: 'siteCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.noWorth.siteCode`).d('无价值工厂代码'),
    },
    {
      name: 'siteName',
      type: 'string',
      label: intl.get(`${modelPrompt}.factoryName`).d('工厂名称'),
    },
    {
      name: 'locatorCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.locatorCode`).d('仓库代码'),
    },
    {
      name: 'materialCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.wasteNumber`).d('废料SAP虚拟号'),
    },
    {
      name: 'materialName',
      type: 'string',
      label: intl.get(`${modelPrompt}.wasteName`).d('废料名称'),
    },
    {
      name: 'weight',
      type: 'string',
      label: intl.get(`${modelPrompt}.weigh.weight`).d('称重重量'),
    },
    {
      name: 'uomCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'creationDate',
      type: 'string',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'remark',
      type: 'string',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-scrap-docs/detail/list/ui`,
        method: 'GET',
        data,
      };
    },
  },
});

export { headTableDS, lineTableDS, detailTableDS, createMaterialDS, materialLotTableDS };
