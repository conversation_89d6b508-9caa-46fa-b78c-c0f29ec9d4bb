/**
 * @feature 超期报表
 * @date 2022-10-17
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import { TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { API_HOST, BASIC } from '@utils/config';
import { isNil } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import myInstance from 'hcm-components-front/lib/utils/myAxios';
import notification from 'utils/notification';
import { Popconfirm } from 'hzero-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Badge } from 'choerodon-ui';
import { useDataSetEvent } from 'utils/hooks';
import { entranceDS } from './stores/EntranceDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.report.overDue';

const OverDue = props => {

  const {
    dataSet,
    match: { path },
  } = props;

  const columns: ColumnProps[] = [
    { name: 'materialCode', width: 160 },
    { name: 'revisionCode', width: 160 },
    { name: 'materialName', width: 160 },
    { name: 'supplierCode', width: 160 },
    { name: 'supplierName', width: 160 },
    { name: 'lot', width: 160 },
    { name: 'overdueType', width: 160 },
    { name: 'primaryUomQty', align: ColumnAlign.right },
    { name: 'receipeTime', width: 150},
    { name: 'productionDate', width: 150 },
    { name: 'expirationDate', width: 150 },
    { name: 'shelfLife', align: ColumnAlign.right },
    { name: 'earlyWarningLeadTime', align: ColumnAlign.right },
    { name: 'shelfLifeUomCode' },
    { name: 'expirationDayNumber', width: 150 , align: ColumnAlign.right },
    { name: 'siteCode' },
    { name: 'warehouseCode', width: 150 },
    { name: 'locatorCode', width: 150 },
    { name: 'qualityStatusDesc' },
    { name: 'currentContainerIdentification' },
    { name: 'topContainerIdentification' },
    {
      name: 'freezeFlag',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
  ];

  const [currentColumns, setCurrentColumns] = useState([
    { name: 'identification', width: 150 },
  ]);

  // 存储选中数据的key
  const [selectedKey, setSelectedKey] = useState([]);


  // 监听C7N pro列表选中操作与数据加载完后事件（load）
  useEffect(() => {
    if (dataSet) {
      dataSet.addEventListener('select', handleDataSetSelectUpdate);
      dataSet.addEventListener('unSelect', handleDataSetSelectUpdate);
      dataSet.addEventListener('selectAll', handleDataSetSelectUpdate);
      dataSet.addEventListener('unSelectAll', handleDataSetSelectUpdate);
      // 加载时设置freezeFlag为Y时不可选
      // dataSet.addEventListener('load', handleQueryDataSetLoad);
    }
    return () => {
      if (dataSet) {
        dataSet.removeEventListener('select', handleDataSetSelectUpdate);
        dataSet.removeEventListener('unSelect', handleDataSetSelectUpdate);
        dataSet.removeEventListener('selectAll', handleDataSetSelectUpdate);
        dataSet.removeEventListener('unSelectAll', handleDataSetSelectUpdate);
        // dataSet.removeEventListener('load', handleQueryDataSetLoad);
      }
    };
  }, []);

  // 初始化标志Y,没有权限的不可选
  // const handleQueryDataSetLoad = () => {
  //   if (!dataSet.records.length) return;
  //   dataSet.records.forEach(i => {
  //     // @ts-ignore
  //     if (i.data.freezeFlag === 'Y') {
  //       // eslint-disable-next-line
  //       i.selectable = false;
  //     }
  //   });
  // };

  // 处理选中条状态
  const handleDataSetSelectUpdate = () => {
    if (dataSet && dataSet.selected) {
      const selectList = dataSet.selected;
      if (selectList && selectList.length) {
        const arr = [];
        selectList.forEach(i => {
          // @ts-ignore
          arr.push(i.data.materialLotId);
        });
        setSelectedKey(arr);
      } else {
        setSelectedKey([]);
      }
    } else {
      setSelectedKey([]);
    }
  };

  // 冻结选中的数据
  const deleteSelectData = () => {
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-overdue-material-lot-report/freeze/ui`;
    const data = [...selectedKey];
    myInstance.post(url, data).then(res => {
      if (res.data.success) {
        notification.success({});
        // 冻结成功后重查列表
        dataSet.query();
      } else if (res.data.message) {
        notification.error({
          description: res.data.message,
        });
      }
    });
  };


  // 解冻选中的数据
  const thawSelectData = () => {
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-overdue-material-lot-report/unfreeze/ui`;
    const data = [...selectedKey];
    myInstance.post(url, data).then(res => {
      if (res.data.success) {
        notification.success({});
        // 解冻成功后重查列表
        dataSet.query();
      } else if (res.data.message) {
        notification.error({
          description: res.data.message,
        });
      }
    });
  };

  // LOT时不显示物料批
  useDataSetEvent(dataSet, 'beforeLoad', async () => {
    if (!dataSet.queryDataSet || !dataSet.queryDataSet.current) {
      return {};
    }
    const queryParmas = dataSet.queryDataSet?.current?.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    const columns: any = [];
    if (queryParmas.identifyType !== 'LOT') {
      columns.push({ name: 'identification', width: 150 })
    }
    setCurrentColumns(columns);
  });

  // 站点联动
  useDataSetEvent(dataSet.queryDataSet, 'update', ({ name, record }) => {
    if (name === 'siteLov') {
      record.init('materialLov');
      record.init('areaLocatorLov');
      record.init('inventoryLocatorLov');
    }
  });

  // 处理导出按钮使用的查询参数
  const getExportQueryParams = () => {
    if (!dataSet.queryDataSet || !dataSet.queryDataSet.current) {
      return {};
    }
    const queryParmas = dataSet.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return {
      ...queryParmas,
    };
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get('tarzan.hmes.report.overDue.title.overDue')
          .d('超期报表')}
      >
        <Popconfirm
          title={intl
            .get(`${modelPrompt}.confirm.unfreeze`, {
              count: selectedKey.length,
            })
            .d(`总计${selectedKey.length}条数据，是否确认解冻?`)}
          onConfirm={thawSelectData}
        >
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            disabled={!selectedKey.length}
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.unfreeze`).d('解冻')}
          </PermissionButton>
        </Popconfirm>
        <Popconfirm
          title={intl
            .get(`${modelPrompt}.confirm.freeze`, {
              count: selectedKey.length,
            })
            .d(`总计${selectedKey.length}条数据，是否确认冻结?`)}
          onConfirm={deleteSelectData}
        >
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            disabled={!selectedKey.length}
            permissionList={[
              {
                code: `tarzan${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.freeze`).d('冻结')}
          </PermissionButton>
        </Popconfirm>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-overdue-material-lot-report/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          otherButtonProps={{
            disabled: true,
          }}
        />
      </Header>
      <Content>
        <Table
          queryFieldsLimit={12}
          searchCode="cqbb1"
          customizedCode="cqbb1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
          }}
          dataSet={dataSet}
          // @ts-ignore
          columns={[
            ...currentColumns,
            ...columns,
          ]}
          highLightRow
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.hmes.report.overDue', 'tarzan.common'],
})(
  withProps(
    () => {
      // @ts-ignore
      const dataSet = new DataSet({ ...entranceDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(OverDue),
);
