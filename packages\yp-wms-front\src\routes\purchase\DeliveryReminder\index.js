/**
 * 送货单提醒管理-入口文件
 */
import React, { useEffect, useState } from 'react';
import { DataSet, Table, Dropdown } from 'choerodon-ui/pro';
import { Menu } from 'choerodon-ui';
import intl from 'utils/intl';
import { isNil, flow } from 'lodash';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import ExcelExport from 'components/ExcelExport';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC, API_HOST } from '@utils/config';
import { getResponse } from '@utils/utils';
import notification from 'utils/notification';
import { headerTableDS } from './stores/DeliveryMainDS';
import styles from './index.module.less';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hwms.delivery.reminder';


const DeliveryReminder = props => {

  const {
    headerTableDs,
    match: { path },
    customizeTable,
  } = props;

  const [selectedStatus, setSelectedStatus] = useState([]);
  const [selectedItem, setSelectedItem] = useState([]);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag
        ? headerTableDs.addEventListener
        : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'query', handleHeaderTableDsQuery);
      handler.call(headerTableDs, 'reset', handleHeaderTableDsQuery);
      handler.call(headerTableDs, 'select', handleHeaderTableDsSelect);
      handler.call(headerTableDs, 'batchSelect', handleHeaderTableDsSelect);
      handler.call(headerTableDs, 'unSelect', handleHeaderTableDsSelect);
      handler.call(headerTableDs, 'batchUnSelect', handleHeaderTableDsSelect);
    }
  };

  // 头单选按钮选中
  const handleHeaderTableDsSelect = () => {
    const selectedStatusList = headerTableDs.selected.map((item) => item.get('status'));
    const selectedItemList = headerTableDs.selected.map((item) => item.get('deliveryNoticeId'));
    setSelectedStatus(selectedStatusList);
    setSelectedItem(selectedItemList);
  };

  const handleHeaderTableDsQuery = () => {
    setSelectedStatus([]);
    setSelectedItem([]);
  };

  // 根据当前勾选的单据状态返回对应的状态变更Item的状态
  const isAllEqualWithKeyWord = (array, keyWord) => {
    // 下达状态，状态变更可点击，且执行中，取消可点击
    // 执行中时，状态变更可点击，且完成，关闭可点击
    switch (keyWord) {
      case 'RELEASED':
        return ['PROCESSING', 'RELEASED'].includes(array[0]);
      case 'PROCESSING':
        return array[0] === 'PROCESSING';
      case 'COMPLETED':
        return array[0] === 'RELEASED';
      case 'CLOSED':
        return array[0] === 'RELEASED';
      case 'CANCEL':
        return array[0] === 'PROCESSING';
      default:
        return true;
    }
  };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'siteCode',
      width: 140,
      lock: 'left',
    },
    {
      name: 'deliveryNoticenum',
      width: 140,
    },
    {
      name: 'description',
      width: 100,
    },
    {
      name: 'lineNum',
      width: 100,
    },
    {
      name: 'materialCode',
      width: 80,
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'uomCode',
      width: 140,
    },
    {
      name: 'fromLocatorCode',
      width: 140,
      align: 'center',
    },
    {
      name: 'toLocatorCode',
      width: 140,
    },
    {
      name: 'qty',
      width: 100,
    },
    {
      name: 'createdRealName',
      width: 100,
    },
    {
      name: 'creationDate',
      width: 140,
      align: 'center',
    },
    {
      name: 'lastUpdateRealName',
      width: 140,
      align: 'center',
    },
    {
      name: 'lastUpdateDate',
      width: 140,
      align: 'center',
    },
  ];

  const clickMenu = async ({ key }) => {
    const wmsDeliveryNoticeList = headerTableDs?.selected?.map(
      item => {
        return {
          deliveryNoticeId: item?.toData().deliveryNoticeId,
          status: key,
        }
      },
    );
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-delivery-notices/save`, {
      method: 'POST',
      body: [...wmsDeliveryNoticeList],
    }).then(_res => {
      const res = getResponse(_res);
      if (res) {
        notification.success({});
        headerTableDs.clearCachedSelected();
        setSelectedStatus([]);
        setSelectedItem([]);
        headerTableDs.query(props.headerTableDs.currentPage);
      }
    });
  };

  const menu = (
    <Menu onClick={clickMenu} className={styles['split-menu']} style={{ width: '100px' }}>
      <Menu.Item key="RELEASED" disabled={isAllEqualWithKeyWord(selectedStatus, 'RELEASED')}>
        <a target="_blank" rel="noopener noreferrer">
          {intl.get(`${modelPrompt}.button.released`).d('下达')}
        </a>
      </Menu.Item>
      <Menu.Item key="PROCESSING" disabled={isAllEqualWithKeyWord(selectedStatus, 'PROCESSING')}>
        <a target="_blank" rel="noopener noreferrer">
          {intl.get(`${modelPrompt}.button.processing`).d('执行中')}
        </a>
      </Menu.Item>
      <Menu.Item key="COMPLETED" disabled={isAllEqualWithKeyWord(selectedStatus, 'COMPLETED')}>
        <a target="_blank" rel="noopener noreferrer">
          {intl.get(`${modelPrompt}.button.completed`).d('完成')}
        </a>
      </Menu.Item>
      <Menu.Item key="CLOSED" disabled={isAllEqualWithKeyWord(selectedStatus, 'CLOSED')}>
        <a target="_blank" rel="noopener noreferrer">
          {intl.get(`${modelPrompt}.button.close`).d('关闭')}
        </a>
      </Menu.Item>
      <Menu.Item key="CANCEL" disabled={isAllEqualWithKeyWord(selectedStatus, 'CANCEL')}>
        <a target="_blank" rel="noopener noreferrer">
          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
        </a>
      </Menu.Item>
    </Menu>
  );

  // 获取导出入参
  const getExportQueryParams = () => {
    if (selectedItem.length > 0) {
      const queryParams = {
        deliveryNoticeIdList: selectedItem,
      }
      return queryParams;
    }
    if (!headerTableDs.queryDataSet || !headerTableDs.queryDataSet.current) {
      return {};
    }
    const queryParams = headerTableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    })
    return queryParams;

  };


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.deliveryReminder`).d('送货单提醒管理')}>
        <Dropdown
          overlay={menu}
          disabled={!(selectedStatus.length > 0
            && selectedStatus.every((e) =>
              e === selectedStatus[0]
              && !['COMPLETED', 'CLOSED', 'CANCEL'].includes(e)))}
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={!(selectedStatus.length > 0
              && selectedStatus.every((e) =>
                e === selectedStatus[0]
                && !['COMPLETED', 'CLOSED', 'CANCEL'].includes(e)))}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/wms-delivery-notices/export`}
          queryParams={getExportQueryParams}
          buttonText='导出'
        />
      </Header>
      <Content className={styles['deliver-content']}>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_REMINDER_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_REMINDER_LIST.HEAD`,
          },
          <Table
            searchCode="deliveryReminder'"
            customizedCode="deliveryReminder"
            dataSet={headerTableDs}
            columns={headerTableColumns}
            highLightRow
            queryBar="filterBar"
            queryFieldsLimit={5}
            queryBarProps={{
              fuzzyQuery: false,
            }}
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      return {
        headerTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_REMINDER_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_REMINDER_LIST.HEAD`],
  }),
  formatterCollections({ code: ['tarzan.hwms.delivery.reminder', 'tarzan.common'] }),
)(DeliveryReminder);
