import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import { isNil } from 'lodash';
import ExcelExportPro from 'components/ExcelExportPro';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@utils/config';
import { tableDS } from './stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.frozenPerformanceQueryReport';

const frozenPerformanceQueryReport = props => {
  const { tableDs } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'eventTypeDesc',
        width: 150,
      },
      {
        name: 'eventId',
        width: 150,
      },
      {
        name: 'materialLotCode',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'locatorCode',
        width: 150,
      },
      {
        name: 'wareHouseCode',
        width: 150,
      },
      {
        name: 'currentContainerCode',
        width: 150,
      },
      {
        name: 'topContainerCode',
        width: 150,
      },
      {
        name: 'operationDate',
        width: 150,
      },
      {
        name: 'operateByDesc',
        width: 150,
      },
      {
        name: 'operationTypeDesc',
        width: 150,
      },
      {
        name: 'reason',
        width: 150,
      },
      {
        name: 'remark',
        width: 150,
      },
    ];
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get('tarzan.mes.frozenPerformanceQueryReport.title').d('冻结实绩查询报表')}
      >
        <ExcelExportPro
          method="POST"
          allBody
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/freeze-actual/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="frozenPerformanceQueryReport"
          customizedCode="frozenPerformanceQueryReport"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.mes.event.frozenPerformanceQueryReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(frozenPerformanceQueryReport),
);
