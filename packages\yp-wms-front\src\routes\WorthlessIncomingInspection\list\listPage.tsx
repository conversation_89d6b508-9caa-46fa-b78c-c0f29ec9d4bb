import React, { FC, useEffect, useState, } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import ExcelExport from 'components/ExcelExport';
import { isNil } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import listPageFactory from '../stores/listPageDs';

interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.ass.worthlessIncomingInspection';

const ListPageComponent: FC<ListPageProps> = ({ listDs }) => {


  useEffect(() => {
    listDs.query();
  }, []);


  const columns: ColumnProps[] = [
    {
      name: 'materialLotCode',
      width: 180
    },
    {
      name: 'siteCode',
    },
    {
      name: 'materialCode',
      width: 150
    },
    {
      name: 'materialName',
      width: 180
    },
    {
      name: 'tagWeight',
    },
    {
      name: 'sampleInspectionWeight',
    },
    {
      name: 'uomCode',
    },
    {
      name: 'relateDocCode',
    },
    {
      name: 'wareHouseCode',
    },
    {
      name: 'locatorCode',
    },
    {
      name: 'inspectMachine',
    },
    {
      name: 'inspectDate',
      width: 150
    },
    {
      name: 'creationDate',
      width: 150
    },
    {
      name: 'creationByName',
    },
    {
      name: 'createWarehouseCode',
      width: 150
    },
    {
      name: 'inspectEmployeeName',
    },
  ]

  const getExportQueryParams = () => {
    if (!listDs.queryDataSet || !listDs.queryDataSet.current) {
      return {};
    }
    const queryParams = listDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach((i) => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });

    return {
      ...queryParams,
      materialLotIds: listDs.selected.map((item: any) => {
        return item.get('materialLotId');
      }),
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.worthlessIncomingInspection`).d('无价值入库抽检明细报表')}>
        <ExcelExport
          method="GET"
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/wms-valua-into-warehouse/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.title.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="worthlessIncomingInspection"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={4} // 头部显示的查询字段的数量
          searchCode="worthlessIncomingInspection" // 动态筛选条后端接口唯一编码
          customizedCode="worthlessIncomingInspection" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.ass.worthlessIncomingInspection'],
})(ListPage);
