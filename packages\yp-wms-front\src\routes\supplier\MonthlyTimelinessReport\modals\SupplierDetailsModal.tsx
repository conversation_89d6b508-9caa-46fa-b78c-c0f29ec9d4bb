/* eslint-disable no-restricted-syntax */
import React, { useEffect, useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import ExcelExport from 'components/ExcelExport';
import { Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { BASIC } from '@utils/config';
import { detailDS } from '../stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.wms.supplier.monthlyTimelinessReport';

const SupplierDetailsModal = (props: any) => {
  const { detailDs, data, queryData } = props;

  useEffect(() => {
    detailDs.setQueryParameter(
      'times',
      data.map(item => item.toData().receiveDoodsTime),
    );
    for (const key in queryData) {
      if (queryData[key]) {
        detailDs.setQueryParameter(key, queryData[key]);
      }
    }
    detailDs.query();
  }, []);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'receiveDoodsTime',
        width: 150,
      },
      {
        name: 'supplierCode',
        width: 150,
      },
      {
        name: 'supplierName',
        width: 150,
      },
      {
        name: 'planQty',
        width: 150,
      },
      {
        name: 'punctualQty',
        width: 150,
      },
      {
        name: 'noPunctualQty',
        width: 120,
      },
      {
        name: 'punctualRate',
        width: 120,
      },
    ];
  }, []);

  const getExportQueryParams = () => {
    return {
      times: data.map(item => item.toData().receiveDoodsTime),
      detailTimes: detailDs.selected.map((item: any) => item.toData().receiveDoodsTime),
      detailSupplierIds: detailDs.selected.map((item: any) => item.toData().supplierId),
      ...queryData,
    };
  };

  return (
    <div className="hmes-style">
      <div style={{ position: 'absolute', right: 0 }}>
        <ExcelExport
          method="GET"
          // exportAsync
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-month-delivery-timely/detail/list/get/ui/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </div>
      <Content>
        <Table
          dataSet={detailDs}
          columns={columns}
          searchCode="supplierDetailsModal"
          customizedCode="supplierDetailsModal"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.wms.supplier.supplierDetailsModal', 'tarzan.common'],
})(
  withProps(
    () => {
      const detailDs = new DataSet({
        ...detailDS(),
      });
      return {
        detailDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(SupplierDetailsModal),
);
