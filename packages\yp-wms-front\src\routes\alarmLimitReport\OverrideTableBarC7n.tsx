/**
 * @Description: 覆盖C7N表格的搜索表单
 * @Author: <<EMAIL>>
 * @Date: 2021-01-12 17:08:54
 * @LastEditTime: 2023-03-06 14:24:14
 * @LastEditors: <<EMAIL>>
 *
 * @example
 *  import { SearchForm } from '@/components/tarzan-ui'
 *  <Table dataSet={tableDs} columns={columns} queryBar={SearchForm} />
 */

import React, { useMemo, useState } from 'react';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import { Button, Form, Row, Col, DataSet, Lov, Select } from 'choerodon-ui/pro';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';

const buttonColumn = {
    lineHeight: '40px',
    paddingLeft: '16px',
};

const rowStyle = {
    marginBottom: '11px',
};

interface TableQueryBarHookPropsWithDisabled{
    queryDataSet: DataSet;
    query:()=>Promise<void>
    resetQuery:()=>void
    // queryDisabled?: boolean;
}

const RenderTableSearchBar: React.FC<TableQueryBarHookPropsWithDisabled> = observer(({
    queryDataSet,
    query,
    resetQuery,
    // queryDisabled = false,
}) => {
    const [hidden, setVisible] = useState(false);
    //   const firstFlowFidlds = useMemo(() => {
    //     return queryFields.slice(0, 3);
    //   }, [queryFields]);

    const keyDownClick = async e => {
        if (e.keyCode === 13) {
            await query();
        }
    };

    const clickHandler = async () => {
        await query();
    };

    const loadEmptyData = () => {
        resetQuery()
    };

    const firstFlowFidlds = <>
        <Lov name='siteLov' />
        <Lov name='material' />
        <Lov name='warehouse' />
    </>

    const otherFidlds = <>
        <Select name='secureFlag' />
        <Lov name='supplier' />
        <Select name='maxInventoryFlag' />
    </>

    return (
        <Row style={rowStyle}>
            <Col span={18}>
                <Form columns={3} labelWidth={121} onKeyDown={keyDownClick} dataSet={queryDataSet}>
                    {firstFlowFidlds}
                    {!hidden && otherFidlds}
                </Form>
            </Col>
            <Col span={6} style={buttonColumn}>
                <Button
                    onClick={() => {
                        setVisible(prev => !prev);
                    }}
                >
                    {hidden
                        ? intl.get('tarzan.common.button.moreQueries').d('更多查询')
                        : intl.get('tarzan.common.button.lessQueries').d('收起查询')}
                </Button>
                <Button onClick={loadEmptyData}>{intl.get('tarzan.common.button.reset').d('重置')}</Button>
                <Button
                    onClick={clickHandler}
                    color={ButtonColor.primary}
                    type={ButtonType.submit}
                >
                    {intl.get('tarzan.common.button.search').d('查询')}
                </Button>
            </Col>
        </Row>
    );
});

/**
 * 覆盖C7N表格的搜索表单
 *
 * @param {TableQueryBarHookProps} props queryBar的返回参数
 * @return ReactDOM
 * @example
 *  import { SearchForm } from '@/components/tarzan-ui'
 *  <Table dataSet={tableDs} columns={columns} queryBar={SearchForm} />
 */
const SearchForm = (props: TableQueryBarHookPropsWithDisabled) => (
    // @ts-ignore
    <RenderTableSearchBar {...props} />
);

export default SearchForm;
