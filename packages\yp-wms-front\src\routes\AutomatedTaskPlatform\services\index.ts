import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// Get请求
export function GetMethod() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-auto-instructions/cancel`,
    method: 'GET',
  };
}

// Post请求
export function PostMethod() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/save/ui`,
    method: 'POST',
  };
}
