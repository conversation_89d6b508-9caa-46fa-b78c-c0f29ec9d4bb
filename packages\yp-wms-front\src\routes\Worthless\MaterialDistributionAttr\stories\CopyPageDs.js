/**
 * @feature 新物料配送属性维护-复制页面ds
 * @date 2021-3-14
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentLanguage, getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import { targetPointOptionDs } from './EntranceDs';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.product.materialDistributionNew.model.materialDistributionNew';

/**
 * 复制页面目标
 */
const copyTargetDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'materialSelect',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.patternType`).d('物料/物料类别'),
      defaultValue: 'material',
      options: new DataSet({
        data: [
          { value: 'material', key: intl.get(`${modelPrompt}.material`).d('物料') },
          {
            value: 'materialCategory',
            key: intl.get(`${modelPrompt}.materialCategory`).d('物料类别'),
          },
        ],
      }),
      textField: 'key',
      valueField: 'value',
      required: true,
      ignore: 'always',
    },
    {
      name: 'materialCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.targetResource`).d('目标来源'),
      required: true,
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteId:
              dataSet.parent && dataSet.parent.current && dataSet.parent.current.get('siteId'),
          };
        },
        lovCode: ({ record }) => {
          switch (record.get('materialSelect')) {
            case 'material':
              return 'MT.METHOD.MATERIAL';
            case 'materialCategory':
              return 'MT.METHOD.MATERIAL_CATEGORY';
            default:
              return false;
          }
        },
      },
    },
    {
      name: 'goalMaterialCategoryId',
      bind: 'materialCode.materialCategoryId',
    },
    {
      name: 'goalMaterialId',
      bind: 'materialCode.materialId',
    },
    {
      name: 'sourceLocatorCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceLocator`).d('来源库位'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      required: true,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteIds:
              dataSet.parent &&
              dataSet.parent.current &&
              [dataSet.parent.current.get('siteId')].join(','),
          };
        },
      },
      ignore: 'always',
    },
    {
      name: 'goalSourceLocatorId',
      bind: 'sourceLocatorCode.locatorId',
    },
    {
      name: 'goalOrganizationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.patternType`).d('目标点位'),
      required: true,
      defaultValue: 'LOCATOR',
      options: targetPointOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      disabled: true,
    },
    {
      name: 'pfepDisOrganization',
      type: FieldType.object,
      required: true,
      label: intl.get(`${modelPrompt}.pfepDisOrganization`).d('目标点位'),
      dynamicProps: {
        lovPara: ({ record, dataSet }) => {
          if (record.get('goalOrganizationType') === 'WORKCELL') {
            return {
              tenantId,
              siteId:
                dataSet.parent && dataSet.parent.current && dataSet.parent.current.get('siteId'),
            };
          }
          return {
            tenantId,
            siteIds:
                dataSet.parent &&
                dataSet.parent.current &&
                [dataSet.parent.current.get('siteId')].join(','),
          };

        },
        lovCode: ({ record }) => {
          switch (record.get('goalOrganizationType')) {
            case 'LOCATOR':
              return 'MT.MODEL.LOCATOR_BY_ORG';
            case 'WORKCELL':
              return 'MT.MODEL.WORKCELL';
            default:
              return false;
          }
        },
      },
    },
    {
      name: 'targetLocatorCode',
      type: FieldType.object,
      required: true,
      label: intl.get(`${modelPrompt}.targetLocator`).d('存储库位'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('goalOrganizationType') === 'LOCATOR';
        },
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteIds:
              dataSet.parent &&
              dataSet.parent.current &&
              [dataSet.parent.current.get('siteId')].join(','),
          };
        },
        bind: ({ record }) => {
          if (record.get('goalOrganizationType') === 'LOCATOR') {
            return 'pfepDisOrganization.locatorCode';
          }
          return false;

        },
      },
    },
  ],
  transport: {},
});

/**
 * 复制页面来源
 */
const copySourceDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  lang: getCurrentLanguage(),
  fields: [
    {
      name: 'materialSelect',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.patternType`).d('物料/物料类别'),
      defaultValue: 'material',
      options: new DataSet({
        data: [
          { value: 'material', key: intl.get(`${modelPrompt}.material`).d('物料') },
          {
            value: 'materialCategory',
            key: intl.get(`${modelPrompt}.materialCategory`).d('物料类别'),
          },
        ],
      }),
      textField: 'key',
      valueField: 'value',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'materialCode1',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.copyResource`).d('复制来源'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        lovCode: ({ record }) => {
          switch (record.get('materialSelect')) {
            case 'material':
              return 'MT.METHOD.MATERIAL';
            case 'materialCategory':
              return 'MT.METHOD.MATERIAL_CATEGORY';
            default:
              return false;
          }
        },
      },
    },
    {
      name: 'materialCode',
      bind: 'materialCode1.materialCode',
    },
    {
      name: 'materialCategoryCode',
      bind: 'materialCode1.categoryCode',
    },
    {
      name: 'sourceLocatorCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceLocator`).d('来源库位'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')].join(','),
          };
        },
      },
    },
    {
      name: 'organizationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.patternType`).d('目标点位'),
      defaultValue: 'LOCATOR',
      options: targetPointOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'pfepDisOrganization',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.pfepDisOrganization`).d('目标点位'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          if (record.get('organizationType') === 'WORKCELL') {
            return {
              tenantId,
              siteId: record.get('siteId'),
            };
          }
          return {
            tenantId,
            siteIds: [record.get('siteId')].join(','),
          };

        },
        lovCode: ({ record }) => {
          switch (record.get('organizationType')) {
            case 'LOCATOR':
              return 'MT.MODEL.LOCATOR_BY_ORG';
            case 'WORKCELL':
              return 'MT.MODEL.WORKCELL';
            default:
              return false;
          }
        },
      },
    },
    {
      name: 'organizationCode',
      bind: 'pfepDisOrganization.locatorCode',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('organizationType')) {
            case 'LOCATOR':
              return 'pfepDisOrganization.locatorCode';
            case 'WORKCELL':
              return 'pfepDisOrganization.workcellCode';
            default:
              return '';
          }
        },
      },
      ignore: 'always',
    },
    {
      name: 'targetLocatorCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.targetLocator`).d('存储库位'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')].join(','),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId') || record.get('organizationType') === 'LOCATOR';
        },
        bind: ({ record }) => {
          if (record.get('organizationType') === 'LOCATOR') {
            return 'pfepDisOrganization.locatorCode';
          }
          return false;

        },
      },
    },
  ],
  transport: {},
});

export { copyTargetDS, copySourceDS };
