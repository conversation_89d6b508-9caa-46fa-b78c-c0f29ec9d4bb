/**
 * @Description: 供应商零件生产日计划报表
 * @Author: <<EMAIL>>
 * @Date: 2023-11-21
 * @LastEditTime: 2023-11-21
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import {DataSetSelection, FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

// const BASIC = {
//   TARZAN_REPORT: '/yp-report-wms-44212',
// }

const modelPrompt = 'tarzan.wms.dailyPlanReport.model';
const tenantId = getCurrentOrganizationId();

const TableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-produce-plan/query-header`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'supplierDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierDate`).d('日期'),
    },
    {
      name: 'planQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planQty`).d("计划数"),
    },
    {
      name: 'actualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualQty`).d('实际数'),
    },
    {
      name: 'reachRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reachRate`).d('达成率'),
    },
  ],
  queryFields: [
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierDesc`).d('供应商描述'),
      lovCode: 'MT.MODEL.SUPPLIER',
      multiple: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierCodes',
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      multiple: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialCodes',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'startDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startDate`).d('日期从'),
      max: 'endDate',
    },
    {
      name: 'endDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endDate`).d('日期至'),
      min: 'startDate',
    },
  ],
});

export { TableDS };
