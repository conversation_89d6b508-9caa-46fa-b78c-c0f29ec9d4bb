import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { isNil } from 'lodash';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import ExcelExport from 'components/ExcelExport';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useDataSetEvent } from 'utils/hooks';
import { tableDS, lineTableDS } from './stores';


const modelPrompt = 'freeStorageDetailReport';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

const freeStorageDetailReport = props => {
  const {
    lineDs,
    headDs,
  } = props;

  useDataSetEvent(headDs, 'load', ({ dataSet }) => {
    if (!dataSet.length) {
      // 查询出来没有数据
      lineDs.loadData([]);
      return;
    }
    headerRowClick(dataSet.current);
  });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'locatorCode',
      },
      {
        name: 'locatorName',
      },
      {
        name: 'materialCode',
      },
      {
        name: 'materialName',
      },
      {
        name: 'supplierCode',
      },
      {
        name: 'supplierName',
      },
      {
        name: 'sumQty',
      },
      {
        name: 'primaryUomCode',
      },
      {
        name: 'lot',
      },
    ];
  }, []);

  const lineColumns = [
    {
      name: 'materialLotCode',
      width: 150,
    },
    {
      name: 'primaryUomQty',
    },
    {
      name: 'primaryUomCode',
    },
    {
      name: 'containerCode',
    },
    {
      name: 'creationDate',
    },
    {
      name: 'createdName',
    },
    {
      name: 'inLocatorTime',
    },
    {
      name: 'lastUpdateDate',
    },
    {
      name: 'lastUpdateName',
    },
  ];

  const getExportQueryParams = () => {
    // if (headDs.selected.length) {
    //   const data = headDs.selected.map(ele => ele.toData()) || [];
    //   return data;
    // }
    if (!headDs.queryDataSet || !headDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = headDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    queryParmas.locatorCodes = (queryParmas.locatorCodes || []).join(',');
    queryParmas.materialCodes = (queryParmas.materialCodes || []).join(',');
    queryParmas.supplierCodes = (queryParmas.supplierCodes || []).join(',');
    queryParmas.lot = (queryParmas.lot || []).join(',');
    return queryParmas;
  };

  const headerRowClick = record => {
    lineDs.setQueryParameter('locatorCode', record.get('locatorCode'));
    lineDs.setQueryParameter('materialCode', record.get('materialCode'));
    lineDs.setQueryParameter('supplierCode', record.get('supplierCode'));
    lineDs.setQueryParameter('primaryUomCode', record.get('primaryUomCode'));
    lineDs.setQueryParameter('lot', record.get('lot'));
    lineDs.query();
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('免费入库明细报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-free-inventory-dtl-report/export`}
          queryParams={getExportQueryParams}
        />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
          dataSet={headDs}
          columns={columns}
          searchCode="freeStorageDetailReport"
          customizedCode="freeStorageDetailReport"
        />
        <Collapse bordered={false} defaultActiveKey={['lineTable']}>
          <Panel key="lineTable" header={intl.get(`${modelPrompt}.title.lineTable`).d('行表格')}>
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              customizedCode="freeStorageDetailReportLine"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet({
        ...tableDS(),
      });
      const lineDs = new DataSet({
        ...lineTableDS(),
      });
      return {
        lineDs,
        headDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(freeStorageDetailReport),
);
