import React from 'react';
import { Modal } from 'choerodon-ui/pro';
import '../index.module.less';

const historyKey = Modal.key();
/**
 * 头行结构的表单示例
 */
export const AutoModal = props => {
  const { title, width = 1080, children = <></>, onOk, onCancel, footer, ...others } = props;

  const _modal = Modal.open({
    key: historyKey,
    destroyOnClose: true,
    title,
    drawer: true,
    closable: true,
    style: {
      width,
    },
    className: 'hmes-style-modal hmes-auto-model',
    children,
    onOk,
    onCancel,
    footer,
    ...others,
  });

  return _modal;
};
