import React, { useEffect } from 'react';
import { Table, Button, DataSet} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { observer } from 'mobx-react';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import notification from 'utils/notification';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import ExcelExport from 'components/ExcelExport';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { tableDS, lineTableDS } from './store/ListDS';



const titlePromptCode = 'hwms.outFactoryApply.view.title';
const modelPrompt = 'hwms.outFactoryApply.field';
const tenantId = getCurrentOrganizationId();

const { Panel } = Collapse;


const TablePage = observer(props => {
  const { lineTableDs, tableDs, customizeTable } = props;

  useEffect(() => {
    if(tableDs) {
      tableDs.query();
      tableDs.addEventListener('load', handleResetHeaderQuery);
    }
    return () => {
      if(tableDs) {
        tableDs.removeEventListener('load', handleResetHeaderQuery);
      }
    }
  }, []);

  const handleResetHeaderQuery = ({ dataSet }) => {
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      lineTableDs.loadData([]);
    }
  };

  /** @type {ColumnProps} 头列 */
  const headerTableColumns = [
    { name: 'siteCode' },
    { name: 'instructionDocNum' },
    { name: 'statusMeaning' },
    { name: 'applicationTypeMeaning' },
    { name: 'attribute1' },
    { name: 'supplierName' },
    { name: 'driver' },
    { name: 'licenseNum' },
    { name: 'factoryDate' },
    { name: 'applyUserName' },
    { name: 'applyDate' },
  ];

  const lineTableColumns = [
    { name: 'lineNum' },
    { name: 'factoryTypeMeaning' },
    { name: 'factoryCode' },
    { name: 'factoryDescription' },
    { name: 'factoryInstructionDocNum' },
    { name: 'factoryQty' },
    { name: 'factoryUomCode' },
    { name: 'attribute2Meaning' },
    { name: 'remark' },
  ];

  const handlePrint = () => {
    if(tableDs.selected.length > 0) {
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-factory-application-manage/print/pdf`, {
        method: 'POST',
        body: tableDs.selected.map(e => e.get('instructionDocNum')),
        responseType: 'blob',
      }).then(res => {
        if (res.failed) {
          notification.error({ message: res.exception });
        } else {
          const file = new Blob([res], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(file);
          const newwindow = window.open(fileURL, 'newwindow');
          if (newwindow) {
            newwindow.print();
          } else {
            notification.error({ message: intl.get(`${modelPrompt}.notification.browser.config`).d('当前窗口已被浏览器拦截，请手动设置浏览器！') });
          }
        }
      })
    }
  };

  const headerRowClick = (record) => {
    lineTableDs.setQueryParameter('instructionDocNum', record.get('instructionDocNum'));
    lineTableDs.query();
  };

  const getQueryParams = () => {
    const queryData=tableDs.queryDataSet?.current?.toJSONData()
    const selectedData=tableDs.selected.map(ele=>ele.get('instructionDocNum'))
    if(selectedData.length>0){
      return {
        instructionDocNums:selectedData
      }
    }
    return queryData
  };

  return (
    <PageHeaderWrapper
      header={
        <>
          <Button onClick={() => handlePrint()} color="primary" icon="print">
            {intl.get('hzero.common.button.print').d('打印')}
          </Button>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-factory-application-manage/export`}
            queryParams={getQueryParams}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
        </>
      }
      title={intl.get(`${titlePromptCode}.outFactoryApply`).d('出厂申请管理')}
    >
      {customizeTable(
        {
          filterCode: `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.QUERY`,
          code: `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.HEAD`,
        },
        <Table
          searchCode="ltlgzt1"
          customizedCode="ltlgzt1"
          dataSet={tableDs}
          columns={headerTableColumns}
          highLightRow
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
        />,
      )}
      <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
          key="basicInfo"
          dataSet={lineTableDs}
        >
          {lineTableDs &&
            customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.LINE`,
              },
              <Table
                customizedCode="ltlgzt2"
                dataSet={lineTableDs}
                highLightRow={false}
                columns={lineTableColumns}
              />,
            )}
        </Panel>
      </Collapse>
    </PageHeaderWrapper>
  );
});
export default flow(
  withProps(
    () => {
      const tableDs = new DataSet({ ...tableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        tableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.LINE`,
    ],
  }),
  formatterCollections({ code: ['hwms.outFactoryApply','hzero.common'] }),
)(TablePage);
