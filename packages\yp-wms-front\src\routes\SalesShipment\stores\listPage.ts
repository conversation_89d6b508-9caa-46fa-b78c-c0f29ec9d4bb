import { DataSet } from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.aps.salesShipment';

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'id',
    selection: DataSetSelection.multiple,
    autoQuery: false,
    pageSize: 10,
    dataKey: 'content',
    totalKey: 'totalElements',
    queryDataSet: new DataSet({
     fields: [
        {
          name: 'saleOrderNum',
          label: '发运单号',
          type: FieldType.string,
        },
        {
          name: 'saleOrderTypeLov',
          lovCode: 'SALE_ORDER_TYPE',
          label: '销售订单类型',
          ignore: FieldIgnore.always,
          type: FieldType.object,
          lovPara: {
            tenantId: getCurrentOrganizationId(),
          },
        },
        {
          name:'saleOrderType',
          type: FieldType.string,
          bind:'saleOrderTypeLov.saleOrderType'
        },
        {
          name: 'status',
          label: '状态',
          lookupCode:'WMS.SO_CREATE_STATUS',
          type: FieldType.string,
        },
        {
          name: 'materialLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
          lovCode: 'MT.METHOD.MATERIAL',
          multiple:true,
          ignore: FieldIgnore.always,
          lovPara: {
            tenantId: getCurrentOrganizationId(),
          },
        },
        // {
        //   name: 'materialIds',
        //   type: FieldType.number,
        //   bind: 'materialLov.materialId',
        // },
        {
          name: 'materialCodes',
          type: FieldType.string,
          bind: 'materialLov.materialCode',
        },
        {
          name: 'siteLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.siteCode`).d('工厂编码'),
          lovCode: 'MT.MODEL.SITE',
          lovPara: {
            tenantId: getCurrentOrganizationId(),
            noworthFlag: 'Y',
          },
          ignore: FieldIgnore.always,
        },
        {
          name: 'plantCode',
          type: FieldType.string,
          bind: 'siteLov.siteCode',
        },
        {
          name: 'wareHouseLov',
          type: FieldType.object,
          ignore: FieldIgnore.always,
          label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
          lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
          multiple: true,
          lovPara: {
            tenantId: getCurrentOrganizationId(),
            locatorCategory: ['AREA'],
          },
        },
        {
          name: 'locatorIds',
          type: FieldType.string,
          bind: 'wareHouseLov.locatorId',
        },
        {
          name:'locatorCodes',
          type: FieldType.string,
          bind: 'wareHouseLov.locatorCode',
        },
        {
          name: 'lot',
          label: '批次',
          type: FieldType.string,
        },
        {
          name: 'createDateFrom',
          label: '创建时间从',
          type: FieldType.dateTime,
        },
        {
          name: 'createDateTo',
          label: '创建时间至',
          type: FieldType.dateTime,
        },
        {
          name: 'lastUpdateFrom',
          label: '最后更新时间从',
          type: FieldType.dateTime,
        },
        {
          name: 'lastUpdateTo',
          label: '最后更新时间至',
          type: FieldType.dateTime,
        },
      ],
    }),
    fields: [
      {
        name: 'saleOrderNum',
        label: '发运单号',
        type: FieldType.string,
      },
      {
        name: 'saleOrderType',
        lookupCode: 'WMS_SO_CREATE_IFACE.SALE_ORDER_TYPE',
        label: '销售订单类型',
        type: FieldType.string,
      },
      {
        name: 'status',
        lookupCode:'WMS.SO_CREATE_STATUS',
        label: '状态',
        type: FieldType.string,
      },
      {
        name: 'message',
        label: '返回消息',
        type: FieldType.string,
      },
      {
        name: 'customCode',
        type: FieldType.string,
        label: '客户编码',
      },
      {
        name: 'saleOrg',
        label: '销售组织',
        type: FieldType.string,
      },
      {
        name: 'saleRouter',
        label: '分销渠道',
        type: FieldType.string,
      },
      {
        name: 'productGroup',
        label: '产品组',
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${
            BASIC.HMES_BASIC
          }/v1/${getCurrentOrganizationId()}/wms-so-create/list`,
          // url:`/yp-wms-33139/v1/${getCurrentOrganizationId()}/wms-so-create/list`,
          // transformResponse: value => {
          //   let listData: any = {};
          //   try {
          //     listData = JSON.parse(value);
          //   } catch (err) {
          //     listData = {
          //       message: err,
          //     };
          //   }
          //   if (!listData.success) {
          //     return {
          //       ...listData,
          //       failed: true,
          //     };
          //   }
          //   return listData;
          // },
        };
      },
    },
  });

export default listPageFactory;
