import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'freeStorageDetailReport';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  // selection: DataSetSelection.multiple,
  // primaryKey: 'id',
  queryFields: [
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouseName`).d('仓库'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      multiple: ',',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        locatorCategory: ['AREA'],
      },
    },
    {
      name: 'locatorCodes',
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      multiple: ',',
    },
    {
      name: 'materialCodes',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'supplier',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      multiple: ',',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierCodes',
      type: FieldType.string,
      bind: 'supplier.supplierCode',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'lastUpdateDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间从'),
      max: 'lastUpdateDateTo',
    },
    {
      name: 'lastUpdateDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间至'),
      min: 'lastUpdateDateFrom',
    },
    {
      name: 'lots',
      type: FieldType.string,
      multiple: true,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
  ],
  fields: [
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('仓库'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('仓库描述'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'sumQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumQty`).d('数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-free-inventory-dtl-report/query-header`,
        method: 'GET',
      };
    },
  },
});

const lineTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  primaryKey: 'id',
  fields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('免费入库物料批条码'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('单位'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('顶层容器'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdName`).d('创建人'),
    },
    {
      name: 'inLocatorTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.inLocatorTime`).d('入库时间'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdateName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateName`).d('最后更新人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-free-inventory-dtl-report/query-line`,
        method: 'GET',
      };
    },
  },
});

export { tableDS, lineTableDS };
