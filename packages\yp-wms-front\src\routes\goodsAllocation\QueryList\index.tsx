/**
 * @Description: 库存明细三级报表（货位） - 列表页
 * @Author: <EMAIL>
 * @Date: 2022/7/6 15:40
 * @LastEditTime: 2022-11-21 13:37:41
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { isNil } from 'lodash';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import { TableQueryBarType, TableMode } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC, API_HOST } from '@utils/config';
import { entranceDS } from '../stores/EntranceDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.wms.AutomaticShelvingConfiguration';
const Query = props => {
  const {
    dataSet,
  } = props;

  useEffect(() => {
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    if (Object.keys(props?.location?.query).length === 0 || props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      // dataSet.query(dataSet.currentPage);
      return;
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const {
      siteId,
      siteCode,
      materialId,
      materialCode,
      revisionCode,
      lotCode,
      qualityStatus,
      ownerType,
      ownerId,
      ownerCode,
      locatorId,
      locatorCode,
    } = props?.location?.query || {};
    const queryParams = {
      siteLov: siteId ? { siteId, siteCode } : undefined, // 回显站点
      materialLov: materialId ? { materialId, materialCode } : undefined,
      revisionCodes: revisionCode && revisionCode.length ? [revisionCode] : undefined,
      lotCodes: lotCode ? [lotCode] : undefined,
      qualityStatus,
      ownerType,
      ownerLov: ownerId
        ? {
          soLineId: ownerId,
          customerId: ownerId,
          soNumContent: ownerCode,
          supplierCode: ownerCode,
          customerCode: ownerCode,
        } : undefined,
      locatorLov: locatorId ? { locatorId, locatorCode } : null,
    };
    // setExportFlag(siteId || null);
    setTimeout(() => {
      dataSet.queryDataSet.loadData([queryParams]);
      dataSet.query();
    }, 200);
  }, [props?.location?.query, props?.location?.state]);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (dataSet) {
      const handerQuery = flag
        ? dataSet.queryDataSet.addEventListener
        : dataSet.queryDataSet.removeEventListener;
      // 查询条件更新时操作
      handerQuery.call(dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ name, record }) => {
    // console.log('11', record.get('siteLov'));
    // if (record.get('siteLov')) {
    //   setExportFlag(record.get('siteLov').siteId)

    // }else{
    //   setExportFlag(null)

    // }
    if (name === 'ownerType') {
      record.set('ownerLov', null);
    }
    if (name === 'siteLov') {
      record.set('locatorLov', null);
      record.set('materialLov', null);
    }
  };

  const handleJumpToMaterialLotTrace = (record, type) => {
    const {
      siteId,
      siteCode,
      materialId,
      materialCode,
      locatorId,
      locatorCode,
      inLocatorTime,
      lot,
      supplierId,
      supplierCode,
      supplierName,
      supplierLot,
      specifiedLevel,
    } = record.toData();
    let otherParams = {};
    if (type === 'okQty') {
      otherParams = {
        qualityStatus: 'OK',
        freezeFlag: 'N',
      };
    } else if (type === 'freezeQty') {
      otherParams = {
        qualityStatus: 'OK',
        freezeFlag: 'Y',
      };
    } else if (type === 'pendingQty') {
      otherParams = {
        qualityStatus: 'PENDING',
      };
    } else {
      otherParams = {
        qualityStatus: 'NG',
      };
    }
    const queryParams = {
      siteId,
      siteCode,
      materialId,
      materialCode,
      loactorsInfo: JSON.stringify({
        locatorId,
        locatorCode,
      }),
      inLocatorTime,
      lotCode: lot,
      supplierId,
      supplierCode,
      supplierName,
      supplierLot,
      specifiedLevel,
      ...otherParams,
    };
    props.history.push({
      pathname: `/hwms/product/material-lot-traceability/list`,
      query: queryParams,
    });
  };

  const columns: ColumnProps[] = [
    { name: 'siteCode', width: 160 },
    { name: 'warehouseCode', width: 160 },
    { name: 'reservoirAreaCode', width: 160 },
    { name: 'reservoirAreaType', width: 160 },
    { name: 'locatorCode', width: 160 },
    { name: 'materialCode', width: 160 },
    { name: 'materialName', width: 160 },
    { name: 'minPackageQty', width: 160 },
    { name: 'packageQty', width: 160 },
    { 
      name: 'okQty',
      width: 160,
      renderer: ({ record, value }) => (
        <a onClick={() => handleJumpToMaterialLotTrace(record, 'okQty')}>{value}</a>
      ),
    },
    { 
      name: 'freezeQty',
      width: 160,
      renderer: ({ record, value }) => (
        <a onClick={() => handleJumpToMaterialLotTrace(record, 'freezeQty')}>{value}</a>
      ),
    },
    {
      name: 'pendingQty',
      width: 160,
      renderer: ({ record, value }) => (
        <a onClick={() => handleJumpToMaterialLotTrace(record, 'pendingQty')}>{value}</a>
      ),
    },
    {
      name: 'ngQty',
      width: 160,
      renderer: ({ record, value }) => (
        <a onClick={() => handleJumpToMaterialLotTrace(record, 'ngQty')}>{value}</a>
      ),
    },
    { name: 'lot', width: 160 },
    { name: 'stockAge', width: 160 },
    { name: 'expirationDate', width: 150 },
    { name: 'supplierCode', width: 160 },
    { name: 'supplierName', width: 160 },
    { name: 'supplierLot', width: 160 },
    { name: 'specifiedLevel' },
  ];

  const getExportQueryParams = () => {
    if (!dataSet.queryDataSet || !dataSet.queryDataSet.current) {
      return {};
    }
    const queryParams = {
      siteId: dataSet.queryDataSet.current.toData().siteId,
      materialId: dataSet.queryDataSet.current.toData().materialIds.join(','),
      warehouseId: dataSet.queryDataSet.current.toData().warehouseId.join(','),
      supplierId: dataSet.queryDataSet.current.toData().supplierId.join(','),
      reservoirAreaId: dataSet.queryDataSet.current.toData().reservoirAreaIds.join(','),
      reservoirAreaType: dataSet.queryDataSet.current.toData().reservoirAreaTypes.join(','),
      locatorId: dataSet.queryDataSet.current.toData().locatorIds.join(',')
    }
    Object.keys(queryParams).forEach((i) => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    console.log(queryParams)
    return {
      ...queryParams,
    };


  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.inventory.query.view.title.level').d('库存明细三级报表（货位）')}>
        {/* {
          exportFlag!== null && */}
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-inventory-details/locator/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        {/* // } */}

      </Header>
      <Content>
        <Table
          searchCode="kccx1"
          customizedCode="kccx1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: true,
          }}
          mode={TableMode.tree}
          // treeAsync
          dataSet={dataSet}
          columns={columns}
          highLightRow
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.inventory.query', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...entranceDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Query),
);
