import React, { useMemo } from 'react';
import { observer } from 'mobx-react';
import { DataSet,  Table, Button } from 'choerodon-ui/pro';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { useRequest } from 'hcm-components-front/lib/components/tarzan-hooks';
import {  tableDS } from './stores';
import { SaveRequest } from './services';

const modelPrompt = 'tarzan.hwms.safetyStock';


const SafetyStock = () => {
  const saveRequest = useRequest(SaveRequest(), {
    manual: true,
    needPromise: true,
  });

  const tableDs = useMemo(()=>{
    return new DataSet(tableDS())
  }, [])

  const handleCancel = () => {
    tableDs.setState('editIndex', undefined)
    tableDs.query(tableDs.currentPage)
  }
  const handleSave = async record => {
    const validateResult = await record.validate()
    if (!validateResult) {
      return false
    }
    const res = await saveRequest.run({
      params:{
        ...record.toData(),
      },
    })
    if (res?.success) {
      tableDs.setState('editIndex', undefined)
      tableDs.query(tableDs.currentPage)
    }
  }
  const handleEdit = record => {
    tableDs.setState('editIndex', record.index)
  }


  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      { 
        name: 'siteLov',
        editor: (record) => {
          return record.dataSet.getState('editIndex') === record.index;
        },
      },
      {
        name: 'materialLov',
        editor: (record) => {
          return record.dataSet.getState('editIndex') === record.index;
        },
      },
      {
        name: 'supplierLov',
        editor: (record) => {
          return record.dataSet.getState('editIndex') === record.index;
        },
      },
      {
        name: 'locator',
        editor: (record) => {
          return record.dataSet.getState('editIndex') === record.index;
        },
      },
      {
        name: 'safeStockValue',
        editor: (record) => {
          return record.dataSet.getState('editIndex') === record.index;
        },
      },
      {
        name: 'dailyConsum',
        editor: (record) => {
          return record.dataSet.getState('editIndex') === record.index;
        },
      },
      {
        name: 'safeStockDay',
        editor: (record) => {
          return record.dataSet.getState('editIndex') === record.index;
        },
      },
      {
        name: 'enableFlag',
        editor: (record) => {
          return record.dataSet.getState('editIndex') === record.index;
        },
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        lock: ColumnLock.right,
        width: 150,
        renderer: ({ record, dataSet }) => {
          return (
            <>
              {
                dataSet?.getState('editIndex') === record?.index && (
                  <Button
                    funcType={FuncType.flat}
                    onClick={handleCancel}
                  >
                    {intl.get(`${modelPrompt}.cancel`).d('取消')}
                  </Button>
                )
              }
              {
                dataSet?.getState('editIndex') === record?.index && (
                  <Button
                    loading={saveRequest.loading}
                    funcType={FuncType.flat}
                    onClick={() => {
                      handleSave(record)
                    }}
                  >
                    {intl.get(`${modelPrompt}.save`).d('保存')}
                  </Button>
                )
              }
              {
                dataSet?.getState('editIndex') !== record?.index && (
                  <Button
                    disabled={
                      dataSet?.getState('editIndex') || dataSet?.getState('editIndex') === 0
                    }
                    funcType={FuncType.flat}
                    onClick={() => {
                      handleEdit(record)
                    }}
                  >
                    {intl.get(`${modelPrompt}.edit`).d('编辑')}
                  </Button>
                )
              }
            </>
          )
        },
      },
    ];
  }, []);

  const handleAdd = () => {
    tableDs.create({}, 0);
    tableDs.setState('editIndex', 0);
  };

  const AddButton = observer(({ds}) => {
    return (<Button
      color={ButtonColor.primary}
      disabled={ds?.getState('editIndex') || ds?.getState('editIndex') === 0}
      onClick={handleAdd}
    >
      {intl.get(`${modelPrompt}.add`).d('新建')}
    </Button>)
  });

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('供应商物料安全库存数据维护')}>
        <AddButton ds={tableDs} />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={headColumns}
          searchCode="safetyStockTable1"
          customizedCode="safetyStockTable1"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(SafetyStock);
