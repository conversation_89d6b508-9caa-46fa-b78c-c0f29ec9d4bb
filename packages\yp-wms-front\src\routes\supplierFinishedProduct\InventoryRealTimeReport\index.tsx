/**
 * @Description: 供应商成品库存实时报表
 * @Author: <<EMAIL>>
 * @Date: 2023-11-21
 * @LastEditTime: 2023-11-21
 * @LastEditors: <<EMAIL>>
 */
import React, { FC } from 'react';
import { RouteComponentProps } from 'react-router';
import { isNil } from 'lodash';
import intl from 'utils/intl';
import {Table, DataSet, Button} from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import {ButtonColor} from "choerodon-ui/pro/lib/button/enum";
import request from "utils/request";
import {getResponse} from "@utils/utils";
import { TableDS } from './stores/index';

// const BASIC = {
//   TARZAN_REPORT: '/yp-report-wms-40066',
// }

const modelPrompt = 'tarzan.qms.inventoryRealTimeReport.model';
const tenantId = getCurrentOrganizationId();

interface InventoryRealTimeReportProps extends RouteComponentProps {
  tableDS: any;
}
const InventoryRealTimeReport: FC<InventoryRealTimeReportProps> = props => {
  const { tableDS } = props;

  const columns: ColumnProps[] = [
    {
      name: 'supplierCode',
      width: 150,
    },
    {
      name: 'supplierName',
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
    },
    {
      name: 'locatorDesc',
    },
    {
      name: 'okQty',
      width: 120,
    },
    {
      name: 'ngQty',
      width: 120,
    },
    {
      name: 'supplierDate',
    },
  ];

  const getExportQueryParams = async () => {
    let queryParams = {};
    const ifaceIds = tableDS.selected.map(item => item?.toData()?.ifaceId);
    if (tableDS.selected.length > 0) {
      queryParams = {
        ifaceIds: ifaceIds.join(','),
      };
    } else {
      queryParams = tableDS.queryDataSet.current.toData();
      Object.keys(queryParams).forEach(i => {
        if (isNil(queryParams[i])) {
          delete queryParams[i];
        }
      })
    }
    // 请求后台
    const result = await request(`${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-inventory-realTime/list/export/ui`, {
      method: 'GET',
      responseType: 'blob',
      query: queryParams,
    });
    const res = getResponse(result);
    if (res) {
      const file = new Blob([res], { type: 'application/vnd.ms-excel' });
      const fileURL = URL.createObjectURL(file);
      const fileName = '供应商成品库存实时报表.xls';
      const elink = document.createElement('a');
      elink.download = fileName;
      elink.style.display = 'none';
      elink.href = fileURL;
      document.body.appendChild(elink);
      elink.click();
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink);
    }
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/WMS_SUPPLIER_INVENTORY',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('供应商成品库存实时报表')}>
        <Button color={ButtonColor.primary} icon="download" onClick={() => getExportQueryParams()}>
          {intl.get(`${modelPrompt}.export`).d('导出')}
        </Button>
        <Button icon="file_upload" onClick={goImport}>
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={tableDS}
          columns={columns}
          searchCode="inventoryRealTimeReport"
          queryBar={TableQueryBarType.filterBar}
          queryFieldsLimit={3}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          customizable
          customizedCode="inventoryRealTimeReport"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.wms.inventoryRealTimeReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDS = new DataSet(TableDS());
      return {
        tableDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(InventoryRealTimeReport as any),
);
