import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

// const Host = `/yp-wms-33139`;
const Host = `${BASIC.HMES_BASIC}`

const modelPrompt = 'tarzan.wms.AutomaticShelvingConfiguration';
const tenantId = getCurrentOrganizationId();

const headerTableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: 'multiple',
  transport: {
    read: ({ data }) => {
      return {
        url: `${Host}/v1/${tenantId}/wms-auto-shelfs/list`,
        method: 'GET',
        data: {
          ...data,
        },
      };
    },
  },
  queryFields: [
    {
      name: 'siteLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
      noCache: true,
    },
    {
      name: 'siteId',
      type: 'number',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: 'string',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
    },
    {
      name: 'materialId',
      type: 'number',
      bind: 'materialLov.materialId',
    },
    {
      name: 'fromWarehouseLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.fromWarehouseLov`).d('仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorCategoryAreaFlag: 'Y',
            siteId: record?.get('siteId') || null,
            enableFlag: 'Y',
          };
        },
      },
    },
    {
      name: 'warehouseId',
      type: 'number',
      bind: 'fromWarehouseLov.locatorId',
    },
  ],
  fields: [
    {
      name: 'siteLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: 'always',
      noCache: true,
    },
    {
      name: 'siteId',
      type: 'number',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: 'string',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
      required: true,
    },
    {
      name: 'materialId',
      type: 'number',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: 'string',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: 'string',
      bind: 'materialLov.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'warehouseLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.fromWarehouseLov`).d('仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: 'always',
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorCategoryAreaFlag: 'Y',
            siteId: record?.get('siteId') || null,
            enableFlag: 'Y',
          };
        },
      },
    },
    {
      name: 'warehouseId',
      type: 'number',
      bind: 'warehouseLov.locatorId',
    },
    {
      name: 'warehouseCode',
      type: 'string',
      bind: 'warehouseLov.locatorCode',
    },
    {
      name: 'warehouseName',
      type: 'string',
      bind: 'warehouseLov.locatorName',
      label: intl.get(`${modelPrompt}.locatorName`).d('仓库描述'),
    },
    // {
    //   name: 'enableFlag',
    //   type: 'boolean',
    //   label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    //   trueValue: 'Y',
    //   falseValue: 'N',
    // },
    {
      name: 'realName',
      type: 'string',
      label: intl.get(`${modelPrompt}.lastUpdatedBy`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: 'string',
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
});

export { headerTableDS };
