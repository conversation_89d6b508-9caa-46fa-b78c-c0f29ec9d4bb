/**
 * @Description: 事务报表平台-主表格-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-25 18:09:12
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.mes.alarmLimitReport.list';
const tenantId = getCurrentOrganizationId();

export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: false,
  selection: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.string,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      noCache: true,
      ignore: FieldIgnore.always,
      multiple:true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialIds',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'material.materialCode',
    },
    {
      name: 'warehouse',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      noCache: true,
      multiple:true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'warehouseIds',
      type: FieldType.string,
      bind: 'warehouse.locatorId',
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      bind: 'warehouse.locatorCode',
    },
    {
      name: 'secureFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secureFlag`).d('是否低于安全库存'),
      lovPara: { tenantId },
      lookupCode: 'WMS.IMPORT_ENABLEFLAG',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'supplier',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商编码'),
      lovCode: 'MT.MODEL.SUPPLIER',
      noCache: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierIds',
      type: FieldType.string,
      bind: 'supplier.supplierId',
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      bind: 'supplier.supplierCode',
    },
    {
      name: 'maxInventoryFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maxInventoryFlag`).d('是否超最大库存值'),
      lovPara: { tenantId },
      lookupCode: 'WMS.IMPORT_ENABLEFLAG',
      textField: 'meaning',
      valueField: 'value',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'minQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.minQty`).d('安全库存'),
    },
    {
      name: 'maxQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maxQty`).d('最大库存'),
    },
    {
      name: 'onhandQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.onhandQty`).d('当前可用库存'),
    },
    {
      name: 'secureFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secureFlag`).d('是否低于安全库存'),
    },
    {
      name: 'maxInventoryFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maxInventoryFlag`).d('是否超最大库存'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('低于安全库存数量'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-minmax-onhands/list/get/ui`,
        method: 'GET',
      };
    },
  },
});
