/**
 * 销售订单管理-入口文件
 * @date 2023-7-5
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState, useRef } from 'react';
import { DataSet, Table, Modal, Button } from 'choerodon-ui/pro';
import { Badge, Collapse } from 'choerodon-ui';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import { flow, isNil } from 'lodash';
import { BASIC } from '@utils/config';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import { headerTableDS, lineTableDS } from './stores/OrderListDS';
import CreateShippingDrawer from './CreateShippingDrawer';
import AssemblyDrawer from './AssemblyDrawer';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.purchase.sellOrder';

let modalCreateShipping;
let modalAssembly;
const tenantId = getCurrentOrganizationId();

const Order = props => {

  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
  } = props;

  const [deliverheaderDetail, setDeliverheaderDetail] = useState({});
  const [deliverheaderDetailDrawer, setDeliverheaderDetailDrawer] = useState({});
  const [deliverListSupplierId, setDeliverListSupplierId] = useState(undefined);
  const [deliverListSiteId, setDeliverListSiteId] = useState(undefined);
  const [soTypeTag, setsoTypeTag] = useState(undefined);
  const [deliverListSupplierSiteIds, setDeliverListSupplierSiteIds] = useState([]);
  // 判断头搜索条件切换
  const [soId, setSoId] = useState();
  // 选中头详情(单选)
  const [ headerDetail, setHeaderDetail] = useState({});
  // 选中行数量
  const [selectedLength, setSelectedLength] = useState(0);
  // 送货单ref
  const shippingDrawerRef = useRef();
  // 页面权限
  // eslint-disable-next-line no-unused-vars
  // const [createInstructionDocLineId, setCreateInstructionDocLineId] = useState(); // TODO 可能有bug
  // 行选中的数据，用来控制创建物料批新建相关
  const [ selectedItems, setSelectedItems] = useState([]);

  useEffect(() => {
    if (lineTableDs) {
      lineTableDs.addEventListener('batchSelect', handleSelectLineTable);
      lineTableDs.addEventListener('batchUnSelect', handleSelectLineTable);
    }
    return () => {
      if (lineTableDs) {
        lineTableDs.removeEventListener('batchSelect', handleSelectLineTable);
        lineTableDs.removeEventListener('batchUnSelect', handleSelectLineTable);
      }
    };
  }, []);

  // 行的选中事件，用来获取选中的instructionDocLineId
  const handleSelectLineTable = () => {
    let noMaterialLot = false;
    const selected = lineTableDs.selected.map(item => {
      // 非实物不可操作
      if (item.get('identifyType') !== 'MATERIAL_LOT') {
        noMaterialLot = true;
      }
      return item.get('poLineId');
    });
    if (noMaterialLot) {
      setSelectedItems([]);
    } else {
      setSelectedItems(selected);
    }
  };

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  // DS事件监听
  useEffect(() => {
    if (props?.location?.query?.poNumber && props?.location?.query?.poNumber !== '') {
      const poNumber = props?.location?.query?.poNumber;
      headerTableDs.queryDataSet.loadData([
        {
          poNumber,
        },
      ]);
      headerTableDs.query();
    } else if (props?.history?.action === 'PUSH') {
      headerTableDs.query();
    }
  }, [props?.location?.query?.poNumber]);

  // 返回页面时恢复选中项和当前项状态
  useEffect(() => {
    if (headerTableDs?.selected?.length > 0) {
      setHeaderDetail(headerTableDs?.selected[0]?.toData() || {});
    }
    if (lineTableDs?.selected?.length > 0) {
      setSelectedLength(lineTableDs?.selected?.length || 0);
    }
  }, []);

  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag
        ? headerTableDs.queryDataSet.addEventListener
        : headerTableDs.queryDataSet.removeEventListener;
      handler.call(headerTableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'select', handleHeaderTableDsSelect);
      handler.call(headerTableDs, 'unSelect', handleHeaderTableDsUnSelect);
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
    }
    // 行监听
    if (lineTableDs) {
      const handler = flag ? lineTableDs.addEventListener : lineTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(lineTableDs, 'load', resetLineDetail);
      handler.call(lineTableDs, 'select', handleLineTableChange);
      handler.call(lineTableDs, 'unSelect', handleLineTableChange);
      handler.call(lineTableDs, 'batchSelect', handleLineTableChange);
      handler.call(lineTableDs, 'batchUnSelect', handleLineTableChange);
    }
  };

  // 头搜索条件切换清空供应商地点
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (data.soId !== soId) {
      setSoId(data.soId);
      record.set('supplierSite', {});
    }
  };

  // 头单选按钮选中
  const handleHeaderTableDsSelect = ({ record }) => {
    const soTypeTag = record.data.soTypeTag
    setsoTypeTag(soTypeTag)
    setDeliverheaderDetail(record?.toData());
    headerTableDs.current = record;
    queryLineTable(record?.toData()?.soId);
    setHeaderDetail(record?.toData() || {});
  };
  // 头单选按钮撤销
  const handleHeaderTableDsUnSelect = () => {
    setHeaderDetail({});
    setDeliverheaderDetail({});
    queryLineTable();
  };
  // 头列表加载
  // const resetHeaderDetail = ({ dataSet }) => {
  const resetHeaderDetail = () => {
    // debugger
    // 列表刷新清除头单选状态
    setHeaderDetail({});
    // 数据正常时用第一条数据查询行数据否则空查
    // if (dataSet?.current?.toData()) {
    //   headerRowClick(dataSet?.current);
    // } else {
    //   queryLineTable();
    // }
  };

  // 行选中事件, 更新选中行数量
  const resetLineDetail = ({ dataSet }) => {
    dataSet.forEach(lineRecored => {
      if (deliverListSupplierId === undefined) {
        // eslint-disable-next-line
        lineRecored.selectable =
          lineRecored?.data?.poHeaderApprovedFlag !== 'N' &&
          lineRecored?.data?.poHeaderDeleteFlag !== 'Y' &&
          lineRecored?.data?.poHeaderClosedFlag !== 'Y' &&
          lineRecored?.data?.returnFlag !== 'Y' &&
          lineRecored?.data?.deleteFlag !== 'Y' &&
          lineRecored?.data?.completeFlag !== 'Y';
      } else {
        // eslint-disable-next-line
        lineRecored.selectable =
          deliverListSupplierId === lineRecored?.data?.soId &&
          deliverListSiteId === lineRecored?.data?.siteId &&
          lineRecored?.data?.poHeaderApprovedFlag !== 'N' &&
          lineRecored?.data?.poHeaderDeleteFlag !== 'Y' &&
          lineRecored?.data?.poHeaderClosedFlag !== 'Y' &&
          lineRecored?.data?.returnFlag !== 'Y' &&
          lineRecored?.data?.deleteFlag !== 'Y' &&
          lineRecored?.data?.completeFlag !== 'Y';
      }
    });
  };
  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    if (dataSet?.selected?.length > 0) {
      const firstSupplierId = dataSet?.selected[0]?.data?.soId;
      const firstSiteId = dataSet?.selected[0]?.data?.siteId;
      setDeliverheaderDetailDrawer(
        firstSupplierId === deliverheaderDetail.soId ? deliverheaderDetail : {},
      );
      setSelectedLength(dataSet?.selected?.length || 0);
      setDeliverListSupplierId(firstSupplierId);
      setDeliverListSiteId(firstSiteId);
      setDeliverListSupplierSiteIds([
        ...deliverListSupplierSiteIds,
        deliverheaderDetail.supplierSiteId,
      ]);
      dataSet.forEach(lineRecored => {
        // eslint-disable-next-line
        lineRecored.selectable =
          firstSupplierId === lineRecored?.data?.soId &&
          firstSiteId === lineRecored?.data?.siteId &&
          lineRecored?.data?.poHeaderApprovedFlag !== 'N' &&
          lineRecored?.data?.poHeaderDeleteFlag !== 'Y' &&
          lineRecored?.data?.poHeaderClosedFlag !== 'Y' &&
          lineRecored?.data?.returnFlag !== 'Y' &&
          lineRecored?.data?.deleteFlag !== 'Y';
      });
    } else {
      setDeliverListSupplierId(undefined);
      setDeliverListSiteId(undefined);
      setDeliverListSupplierSiteIds([]);
      setSelectedLength(0);
      setDeliverheaderDetailDrawer({});
      dataSet.forEach(lineRecored => {
        // eslint-disable-next-line
        lineRecored.selectable =
          lineRecored?.data?.poHeaderApprovedFlag !== 'N' &&
          lineRecored?.data?.poHeaderDeleteFlag !== 'Y' &&
          lineRecored?.data?.poHeaderClosedFlag !== 'Y' &&
          lineRecored?.data?.returnFlag !== 'Y' &&
          lineRecored?.data?.deleteFlag !== 'Y';
      });
    }
  };

  // 行列表数据查询
  const queryLineTable = soId => {
    lineTableDs.setQueryParameter('soId', soId);
    lineTableDs.setState('soId', soId)
    lineTableDs.query();
  };

  const shippingHandleSubmit = () => {
    return shippingDrawerRef.current.handleSubmit();
  };

  // 创建送货单
  const handleCreateCreateShipping = async () => {
    let deliverList;
    const supplierSiteId = [];
    const supplierSiteCode = [];
    if (lineTableDs?.selected?.length > 0) {
      deliverList = lineTableDs.selected.map(item => {
        item.data.customerId = headerTableDs?.selected[0]?.toData()?.customerId;
        item.data.customerName = headerTableDs?.selected[0]?.toData()?.customerName;
        item.data.customerSiteId = headerTableDs?.selected[0]?.toData()?.customerSiteId;
        item.data.description = headerTableDs?.selected[0]?.toData()?.description;
        item.data.description = headerTableDs?.selected[0]?.toData()?.description;
        item.data.targetLocatorId = item.get('shipLocatorId');
        item.data.locatorCode = item.get('shipLocatorCode')
        if (supplierSiteId.indexOf(item.data.supplierSiteId) === -1) {
          supplierSiteId.push(item.data.supplierSiteId);
          supplierSiteCode.push(item.data.supplierSiteCode);
        }
        return item.data;
      });
    }

    modalCreateShipping = Modal.open({
      title: intl.get(`${modelPrompt}.createDeliverys`).d('创建发/退货单'),
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <CreateShippingDrawer
          ref={shippingDrawerRef}
          deliverList={deliverList}
          deliverheaderDetailDrawer={{
            ...deliverheaderDetailDrawer,
            supplierSiteId: supplierSiteId.length === 1 ? supplierSiteId[0] : undefined,
            supplierSiteCode: supplierSiteCode.length === 1 ? supplierSiteCode[0] : undefined,
          }}
          handleSuccess={handleSuccess}
          customizeTable={customizeTable}
          soTypeTag={soTypeTag}
        />
      ),
      footer: (
        <>
          <Button onClick={() => modalCreateShipping.close()}>
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
          <PermissionButton
            key="create"
            type="c7n-pro"
            color={ButtonColor.primary}
            onClick={shippingHandleSubmit}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.createDeliverys`).d('创建发/退货单')}
          </PermissionButton>
        </>
      ),
    });
  };

  // 组件信息
  const handleCreateAssembly = async record => {
    const assemblyDetail = record?.toData() || {};
    modalAssembly = Modal.open({
      title: intl.get(`${modelPrompt}.planInformation`).d('计划行'),
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      // children: <AssemblyDrawer record={assemblyDetail} customizeTable={customizeTable} />,
      children: <AssemblyDrawer record={assemblyDetail} />,
      footer: (
        <Button onClick={() => modalAssembly.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 操作列渲染
  const optionRender = record => (
    <span className="action-link">
      <a
        disabled={!(record.data.poComponentExists !== 'N')}
        onClick={() => {
          handleCreateAssembly(record);
        }}
      >
        {intl.get(`${modelPrompt}.planInformation`).d('计划行')}
      </a>
    </span>
  );

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'soNumber',
      width: 180,
      // fixed: 'left',
      lock: 'left',
    },
    {
      name: 'soStatusDesc',
      width: 140,
    },
    {
      name: 'siteCode',
      width: 180,
    },
    {
      name: 'customerName',
      width: 150,
    },
    {
      name: 'description',
      width: 130,
    },
    {
      name: 'shipToName',
      width: 130,
    },
    {
      name: 'shipToSiteDesc',
      width: 130,
    },
    {
      name: 'salesPerson',
      width: 130,
    },
    {
      name: 'soType',
      width: 130,
    },
    {
      name: 'freezeFlag',
      width: 120,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === '是' ? 'success' : 'error'}
          text={value}
        />
      ),
    },
    {
      name: 'currencyCode',
      width: 130,
    },
    {
      name: 'soCategoryDesc',
      width: 130,
    },
    {
      name: 'salesChannel',
      width: 130,
    },

    {
      name: 'commercialTerm',
      width: 120,
    },
    {
      name: 'paymentTerm',
      width: 120,
    },
    {
      name: 'remark',
      width: 130,
    },
    {
      name: 'creationDate',
      width: 160,
      align: 'center',
    },
  ];

  // 行信息表配置
  let lineTableColumns = [];
  const lineTableColumnsFront = [
    {
      name: 'revisionCode',
      dataIndex: 'revisionCode',
      width: 80,
      title: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
    },
    {
      name: 'materialName',
      dataIndex: 'materialName',
      width: 150,
      title: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'orderedQuantity',
      dataIndex: 'orderedQuantity',
      align: 'right',
      width: 70,
      title: intl.get(`${modelPrompt}.orderedQuantity`).d('数量'),
    },
    {
      name: 'uomCode',
      dataIndex: 'uomCode',
      width: 60,
      title: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'processedOrdered',
      dataIndex: 'processedOrdered',
      align: 'right',
      width: 100,
      title: intl.get(`${modelPrompt}.processedOrdered`).d('已制单数量'),
    },
  ];

  // const lineTableColumnsMiddle = [
  //   {
  //     name: 'actualQuantity',
  //     dataIndex: 'actualQuantity',
  //     width: 140,
  //     align: 'right',
  //     title: intl.get(`${modelPrompt}.actualQuantity`).d('已创建物料批数量'),
  //   },
  // ];
  const lineTableColumnsAfter = [
    // {
    //   name: 'demandDate',
    //   dataIndex: 'demandDate',
    //   align: 'center',
    //   width: 140,
    //   title: intl.get(`${modelPrompt}.demandDate`).d('需要日期'),
    // },
    {
      name: 'shipFromSiteCode',
      dataIndex: 'shipFromSiteCode',
      width: 160,
      title: intl.get(`${modelPrompt}.shipFromSiteCode`).d('站点'),
    },
    {
      name: 'shipLocatorCode',
      dataIndex: 'shipLocatorCode',
      width: 100,
      title: intl.get(`${modelPrompt}.shipLocatorCode`).d('发运库位'),
    },
    {
      name: 'shipMethod',
      dataIndex: 'shipMethod',
      width: 100,
      title: intl.get(`${modelPrompt}.shipMethod`).d('发运方式'),
    },
    {
      name: 'quantityReceived',
      dataIndex: 'quantityReceived',
      width: 100,
      title: intl.get(`${modelPrompt}.quantityReceived`).d('已备货数量'),
    },
    {
      name: 'quantityDelivered',
      dataIndex: 'quantityDelivered',
      width: 130,
      title: intl.get(`${modelPrompt}.quantityDelivered`).d('已发运数量'),
    },
    {
      name: 'reservedQuantity',
      dataIndex: 'reservedQuantity',
      width: 100,
      title: intl.get(`${modelPrompt}.reservedQuantity`).d('预留数量'),
    },
    {
      name: 'cancelledQuantity',
      dataIndex: 'cancelledQuantity',
      width: 130,
      title: intl.get(`${modelPrompt}.cancelledQuantity`).d('取消数量'),
    },
    {
      name: 'scheduleShipDate',
      dataIndex: 'scheduleShipDate',
      width: 150,
      title: intl.get(`${modelPrompt}.scheduleShipDate`).d('计划发运日期'),
    },
    {
      name: 'scheduleArrivalDate',
      dataIndex: 'scheduleArrivalDate',
      width: 150,
      title: intl.get(`${modelPrompt}.scheduleArrivalDate`).d('计划到达日期'),
    },
    // {
    //   name: 'completeFlag',
    //   dataIndex: 'completeFlag',
    //   width: 80,
    //   title: intl.get(`${modelPrompt}.completeFlag`).d('交货完成标识'),
    //   align: 'center',
    //   renderer: ({ value }) => (
    //     <Badge
    //       status={value === 'Y' ? 'success' : 'error'}
    //       text={
    //         value === 'Y'
    //           ? intl.get(`tarzan.common.label.yes`).d('是')
    //           : intl.get(`tarzan.common.label.no`).d('否')
    //       }
    //     />
    //   ),
    // },
    // {
    //   name: 'returnFlag',
    //   dataIndex: 'returnFlag',
    //   width: 80,
    //   title: intl.get(`${modelPrompt}.returnFlag`).d('退货行标识'),
    //   align: 'center',
    //   renderer: ({ value }) => (
    //     <Badge
    //       status={value === 'Y' ? 'success' : 'error'}
    //       text={
    //         value === 'Y'
    //           ? intl.get(`tarzan.common.label.yes`).d('是')
    //           : intl.get(`tarzan.common.label.no`).d('否')
    //       }
    //     />
    //   ),
    // },
    // {
    //   name: 'deleteFlag',
    //   dataIndex: 'deleteFlag',
    //   width: 80,
    //   title: intl.get(`${modelPrompt}.deleteFlag`).d('删除标识'),
    //   align: 'center',
    //   renderer: ({ value }) => (
    //     <Badge
    //       status={value === 'Y' ? 'success' : 'error'}
    //       text={
    //         value === 'Y'
    //           ? intl.get(`tarzan.common.label.yes`).d('是')
    //           : intl.get(`tarzan.common.label.no`).d('否')
    //       }
    //     />
    //   ),
    // },
    // {
    //   name: 'consignedFlag',
    //   dataIndex: 'consignedFlag',
    //   width: 80,
    //   title: intl.get(`${modelPrompt}.consignedFlag`).d('寄售标识'),
    //   align: 'center',
    //   renderer: ({ value }) => (
    //     <Badge
    //       status={value === 'Y' ? 'success' : 'error'}
    //       text={
    //         value === 'Y'
    //           ? intl.get(`tarzan.common.label.yes`).d('是')
    //           : intl.get(`tarzan.common.label.no`).d('否')
    //       }
    //     />
    //   ),
    // },
    // {
    //   name: 'releaseNum',
    //   dataIndex: 'releaseNum',
    //   width: 100,
    //   title: intl.get(`${modelPrompt}.releaseNum`).d('一揽子发放号'),
    // },
    {
      name: 'packingInstructions',
      dataIndex: 'packingInstructions',
      width: 120,
      title: intl.get(`${modelPrompt}.packingInstructions`).d('包装需求'),
    },
    {
      name: 'itemCategory',
      dataIndex: 'itemCategory',
      width: 120,
      title: intl.get(`${modelPrompt}.itemCategory`).d('项目类别'),
    },
    {
      name: 'freezeFlag',
      dataIndex: 'freezeFlag',
      align: 'center',
      width: 140,
      title: intl.get(`${modelPrompt}.freezeFlag`).d('信用冻结标识'),
      renderer: ({ value }) => (
        <Badge
          status={value === '是' ? 'success' : 'error'}
          text={value}
        />
      ),
    },
    {
      name: 'contractNum',
      dataIndex: 'contractNum',
      width: 130,
      title: intl.get(`${modelPrompt}.contractNum`).d('合同编码'),
    },
    {
      name: 'customerPoNum',
      dataIndex: 'customerPoNum',
      width: 130,
      title: intl.get(`${modelPrompt}.customerPoNum`).d('客户采购订单编码'),
    },
    {
      name: 'customerPoLineNum',
      dataIndex: 'customerPoLineNum',
      width: 130,
      title: intl.get(`${modelPrompt}.customerPoLineNum`).d('客户采购订单行号'),
    },
    {
      name: 'parentLineNum',
      dataIndex: 'parentLineNum',
      width: 130,
      title: intl.get(`${modelPrompt}.parentLineNum`).d('母件订单行号'),
    },
    {
      name: 'reason',
      dataIndex: 'reason',
      width: 130,
      title: intl.get(`${modelPrompt}.reason`).d('原因'),
    },
    {
      name: 'remark',
      dataIndex: 'remark',
      width: 130,
      title: intl.get(`${modelPrompt}.remark`).d('行备注'),
    },
  ];

  if (
    props.permissionDetail?.approve === false &&
    props.permissionDetail?.controllerType === 'hidden'
  ) {
    lineTableColumns = [...lineTableColumnsFront, ...lineTableColumnsAfter];
  } else {
    lineTableColumns = [
      ...lineTableColumnsFront,
      ...lineTableColumnsAfter,
    ];
  }

  // 固定不动列
  const otherColumn = [
    {
      name: 'soLineNum',
      dataIndex: 'soLineNum',
      width: 80,
      title: intl.get(`${modelPrompt}.lineNum`).d('行号'),
      fixed: 'left',
      lock: 'left',
    },
    {
      name: 'manageMode',
      dataIndex: 'manageMode',
      width: 120,
      title: intl.get(`${modelPrompt}.manageMode`).d('管理模式'),
      fixed: 'left',
      lock: 'left',
    },
    {
      name: 'lineType',
      dataIndex: 'lineType',
      width: 110,
      title: intl.get(`${modelPrompt}.lineType`).d('行类型'),
      // fixed: 'left',
      // lock: 'left',
    },
    {
      name: 'lineStatus',
      dataIndex: 'lineStatus',
      width: 110,
      title: intl.get(`${modelPrompt}.lineStatus`).d('行状态'),
      // fixed: 'left',
      // lock: 'left',
    },
    {
      name: 'materialCode',
      dataIndex: 'materialCode',
      width: 130,
      title: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      fixed: 'left',
      lock: 'left',
    },
  ];

  // 航信息操作列配置
  const optionColumn = {
    name: 'poLineId',
    fixed: 'right',
    lock: 'right',
    width: 200,
    align: 'center',
    title: intl.get(`${modelPrompt}.option`).d('操作'),
    renderer: ({ record }) => optionRender(record),
  };

  lineTableColumns = [...otherColumn, ...lineTableColumns, optionColumn];

  const headerRowClick = record => {
    console.log(record)
    const soTypeTag = record.data.soTypeTag
    console.log(soTypeTag)
    setsoTypeTag(soTypeTag)
    setSelectedItems([]);
    headerTableDs.select(record);
    queryLineTable(record?.toData()?.soId);
  };

  // 创建发货单成功回调
  const handleSuccess = id => {
    lineTableDs.batchUnSelect(lineTableDs.selected);
    lineTableDs.clearCachedSelected();
    setDeliverheaderDetailDrawer({});
    setDeliverListSupplierId(undefined);
    setDeliverListSiteId(undefined);
    setDeliverListSupplierSiteIds([]);
    setSelectedLength(0);
    queryLineTable(headerTableDs?.current?.data?.soId);
    modalCreateShipping.close();
    // 成功后跳转到销售发运平台
    props.history.push({
      pathname: `/hwms/so-delivery/so-delivery-platform-new/detail/${id}`,
      state: {
        executionType: '',
      },
    });
  };

  const getExportQueryParams = () => {
    if (!headerTableDs.queryDataSet || !headerTableDs.queryDataSet.current) {
      return {};
    }
    const queryParams = headerTableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return {
      ...queryParams,
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.sellOrderManagement`).d('销售订单管理')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreateCreateShipping}
          disabled={selectedLength === 0}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.createDeliverys`).d('创建发/退货单')}
        </PermissionButton>
        {/* <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => props.history.push(`/hmes/so-delivery/sell-order-manage/detail/create`)}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.createSaleDelivery`).d('创建销售订单')}
        </PermissionButton> */}
        {/* </PermissionButton> */}
        <ExcelExport
          method="GET"
          // exportAsync
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/sale-orders/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.button.export`).d('导出')}
        />
      </Header>
      <Content className={styles['order-content']}>
        <Table
          searchCode="cgddgl1"
          customizedCode="cgddgl1"
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headerTableDs}
          columns={headerTableColumns}
          highLightRow={false}
          queryFieldsLimit={7}
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
        />
        <Collapse bordered={false} collapsible="icon" defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            // extra={extraButton}
            dataSet={lineTableDs}
          >
            {lineTableDs && (
              <Table
                customizedCode="cgddgl2"
                className={styles['expand-table']}
                queryBar="bar"
                dataSet={lineTableDs}
                highLightRow={false}
                columns={lineTableColumns}
                pagination={{
                  showPager: true, // 显示数字按钮
                  pageSizeOptions: ['10', '20', '50', '100', '200'],
                }}
              />
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  formatterCollections({ code: ['tarzan.hmes.purchase.sellOrder', 'tarzan.common', 'hzero.c7nProUI'] }),
  // withCustomize({
  //   unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.LIST`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST.LINE`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_BUTTON.HEAD`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_BUTTON.LINE`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_MATERIAL_LOT.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_COMPONENT.QUERY`],
  // }),
)(Order);
