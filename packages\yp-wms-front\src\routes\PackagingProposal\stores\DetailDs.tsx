/*
 * @Author: 20379 <EMAIL>
 * @Date: 2023-12-19 15:04:55
 * @LastEditors: 20379 <EMAIL>
 * @LastEditTime: 2023-12-22 11:44:34
 * @FilePath: /yp-wms-front/packages/yp-wms-front/src/routes/packageProposal/Management/stores/Detail.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'hwms.packageProposalManagement';
const tenantId = getCurrentOrganizationId();

const formDS = (): DataSetProps => ({
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  autoLocateFirst: true,
  fields: [
    {
      name: 'battery',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.orderSeq`).d('电池型号'),
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('零件号'),
      lovCode: 'MT.METHOD.MATERIAL',
      required: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      bind: 'materialLov.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('零件名称'),
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      lovCode: 'MT.MODEL.SUPPLIER',
      required: true,
      textField: 'supplierCode',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierCode',
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'supplierName',
      bind: 'supplierLov.supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'materialOwnership',
      type: FieldType.string,
      lookupCode: 'WMS.MATERIAL_OWNERSHIP',
      required: true,
      label: intl.get(`${modelPrompt}.materialName`).d('物料归属'),
    },
    {
      name: 'department',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.department`).d('部门'),
    },
    {
      name: 'startDate',
      type: FieldType.dateTime,
      required: true,
      label: intl.get(`${modelPrompt}.startDate`).d('日期'),
    },
    {
      name: 'tel',
      required: true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tel`).d('TEL'),
    },
    {
      name: 'eml',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.eml`).d('EML'),
    },
    {
      name: 'minister',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.minister`).d('部长'),
    },
    {
      name: 'management',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.management`).d('高级经理'),
    },
    {
      name: 'engineer',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.engineer`).d('工程师'),
    },
    {
      name: 'picture1',
      type: FieldType.string,
      // required: true,
      label: intl.get(`${modelPrompt}.picture1Uuid`).d('图一、零件图片'),
    },
    {
      name: 'explain1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.explain1`).d('文字说明1'),
    },
    {
      name: 'picture2',
      type: FieldType.string,
      // required: true,
      label: intl.get(`${modelPrompt}.picture2Uuid`).d('图二、零件在周转箱/料架摆放图片 (俯视图）'),
    },
    {
      name: 'explain2',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.explain2`).d('文字说明2'),
    },
    {
      name: 'picture3',
      type: FieldType.string,
      // required: true,
      label: intl.get(`${modelPrompt}.picture3Uuid`).d('图三、周转箱或料架侧视图'),
    },
    {
      name: 'explain3',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.explain3`).d('文字说明3'),
    },
    {
      name: 'picture4',
      type: FieldType.string,
      // required: true,
      label: intl.get(`${modelPrompt}.picture4Uuid`).d('图四、周转箱+托盘组托图片'),
    },
    {
      name: 'explain4',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.explain4`).d('文字说明4'),
    },
    {
      name: 'proposalStatus',
      type: FieldType.string,
      lookupCode: 'WMS.PACKAGE_PROPOSAL_STATUS',
      label: intl.get(`${modelPrompt}.proposalStatus`).d('提案状态'),
    },
  ],
});

const packagingTypeDS = (): DataSetProps => ({
  paging: false,
  selection: false,
  fields: [
    {
      name: 'packageType',
      type: FieldType.string,
      required: true,
      lookupCode: 'WMS.PACKAGE_TYPE',
      label: intl.get(`${modelPrompt}.packageType`).d('包装种类'),
    },
    {
      name: 'boxL',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.boxL`).d('L'),
    },
    {
      name: 'boxW',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.boxW`).d('W'),
    },
    {
      name: 'boxH',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.boxH`).d('H'),
    },
    {
      name: 'capacity',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.capacity`).d('收容数'),
    },
    {
      name: 'materialWeight',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.materialWeight`).d('零件重'),
    },
    {
      name: 'boxWeight',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.boxWeight`).d('箱/台车重'),
    },
    {
      name: 'sumWeight',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.sumWeight`).d('总重量'),
    },
    {
      name: 'labelPosition',
      type: FieldType.string,
      required: true,
      lookupCode: 'WMS.LABEL_POSITION',
      label: intl.get(`${modelPrompt}.labelPosition`).d('标签位置'),
    },
    {
      name: 'boxQty',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.boxQty`).d('组托箱数'),
    },
    {
      name: 'conL',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.conL`).d('L'),
    },
    {
      name: 'conW',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.conW`).d('W'),
    },
    {
      name: 'conH',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.conH`).d('H'),
    },
    {
      name: 'eachPackage',
      type: FieldType.boolean,
      required: true,
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
      label: intl.get(`${modelPrompt}.eachPackage`).d('逐个包装'),
    },
    {
      name: 'dustCover',
      type: FieldType.boolean,
      required: true,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      label: intl.get(`${modelPrompt}.dustCover`).d('防尘罩'),
    },
    {
      name: 'internalItem',
      type: FieldType.boolean,
      required: true,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      label: intl.get(`${modelPrompt}.internalItem`).d('内材'),
    },
  ],
});

const truckInfoDS = (): DataSetProps => ({
  paging: false,
  selection: false,
  fields: [
    {
      name: 'truckCode',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.truckCode`).d('货车代码'),
    },
    {
      name: 'axlesQty',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.axlesQty`).d('轴数'),
    },
    {
      name: 'ton',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.ton`).d('吨位'),
    },
    {
      name: 'truckL',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.truckL`).d('车厢外长[mm]'),
    },
    {
      name: 'truckW',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.truckW`).d('车厢外宽[mm]'),
    },
    {
      name: 'truckH',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.truckH`).d('车厢外高[mm]'),
    },
    {
      name: 'mantissa',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.mantissa`).d('尾数'),
    },
    {
      name: 'palletQtyL',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.palletQtyL`).d('L'),
    },
    {
      name: 'palletQtyW',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.palletQtyW`).d('W'),
    },
    {
      name: 'palletQtyH',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.palletQtyH`).d('H'),
    },
    {
      label: intl.get(`${modelPrompt}.loadQty`).d('托盘满载时货车内放置数量'),
      type: FieldType.number,
      required: true,
      name: 'loadQty',
    },
  ],
});

const supplierPackageListDS = (): DataSetProps => ({
  paging: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  autoQuery: false,
  fields: [
    {
      name: 'packageName',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.packageName`).d('包装名称'),
    },
    {
      name: 'picture',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.pictureUuid`).d('图片展示'),
    },
    {
      name: 'emptyFlag',
      type: FieldType.boolean,
      required: true,
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      label: intl.get(`${modelPrompt}.ton`).d('是否返空'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-package-proposals/query/line/ui`,
        method: 'GET',
      };
    },
  },
});

export { formDS, packagingTypeDS, truckInfoDS, supplierPackageListDS };
 