/**
 * @Description: 销售发运平台 - 入口页面
 * @Author: <EMAIL>
 * @Date: 2022/2/10 15:08
 */
import React, { useEffect, useState } from 'react';
import { DataSet, Table, Modal, Output, Form } from 'choerodon-ui/pro';
import { Collapse, } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import notification from 'utils/notification';
import { useDataSetEvent } from 'utils/hooks';
import withProps from 'utils/withProps';
import { flow, isNil } from 'lodash';
import { Content, Header } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { tableDS, lineTableDS, detailTableDS } from './stores/ListDS';
import axios from 'axios';

const modelPrompt = 'hwms.PurchaseReceiptReport';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

let modal

const PurchaseReceiptReport = props => {
  const {
    tableDs,
    lineTableDs,
    detailTableDs,
  } = props;

  const [_, setLineId] = useState()

  useEffect(() => {
    handleFetchDefaultSite();
  }, []);

  useEffect(() => {
    tableDs.addEventListener('load', handleFetchHeadList);
    return () => {
      tableDs.removeEventListener('load', handleFetchHeadList);
    };
  }, []);

  const handleFetchHeadList = () => {
    if (tableDs?.current?.toData()) {
      headerRowClick(tableDs?.current);
    } else {
      queryLineTable(null);
    }
  };

  const handleFetchDefaultSite = () => {
    request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-user-organization/user/default/site/ui`, {
      method: 'GET',
    }).then(res => {
      if (res) {
        tableDs.queryDataSet.current.set('siteIds', [res.rows.siteId]);
        tableDs.queryDataSet.current.set('siteLov', [res.rows]);
        tableDs.query();
      }
    })
  };

  const headerRowClick = record => {

    queryLineTable(record?.get('instructionDocId'), record?.get('instructionDocType'));
  };

  // 行列表数据查询
  const queryLineTable = (instructionDocId) => {
    if (instructionDocId) {
      lineTableDs.setQueryParameter('instructionDocId', instructionDocId);
      lineTableDs.query();
    } else {
      lineTableDs.loadData([]);
    }
  };

  const columns: ColumnProps[] = [
    { name: 'instructionDocNum' },
    { name: 'poNumber' },
    { name: 'instructionDocStatusDesc' },
    { name: 'instructionDocTypeDesc' },
    { name: 'sourceSystemDesc', width: 150 },
    { name: 'supplierCode', width: 150 },
    { name: 'supplierName', align: ColumnAlign.right },
    { name: 'realName' },
    { name: 'creationDate', width: 150 },
    { name: 'receiveDoodsTime', width: 150 },
    { name: 'expectedArrivalTime', width: 150 },

  ];

  const lineColumns: ColumnProps[] = [
    { name: 'materialCode' },
    { name: 'materialName' },
    { name: 'quantityOrdered' },
    { name: 'quantity' },
    { name: 'actualQty' },
    { name: 'storageActualQty' },
    { name: 'uomCode' },
    { name: 'locatorCode' },
    { name: 'creationDate' },
    { name: 'lineRealName' },
    {
      header: intl.get(`${modelPrompt}.column.operation`).d('操作列'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      width: 260,
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => {
                setLineId(record?.get('instructionDocLineId'))
                handleOpenDetail(record)
              }}
            >
              {intl.get(`${modelPrompt}.create.detail`).d('明细')}
            </a>
          </span>
        );
      },
    },
  ];

  const detailColumns: ColumnProps[] = [
    { name: 'materialIdentification' ,width: 150 },
    { name: 'materialLotStatusDesc' },
    { name: 'materialCode', width: 150 },
    { name: 'materialName', width: 150 },
    { name: 'containerCode', width: 150 },
    { name: 'sumActualQty' },
    { name: 'uomCode' },
    { name: 'lot',width: 150 },
    { name: 'receiveBy' },
    { name: 'receiveLocatorCode', width: 150 },
    { name: 'receiveData', width: 180 },
    { name: 'putOnBy' },
    { name: 'putOnLocatorCode', width: 150 },
    { name: 'putOnData', width: 180 },
  ];

  const createModalTitle = (instructionDocLineId) => {
    return (
      <div>
        <span style={{ fontSize: '14px' }}>
          {intl.get(`${modelPrompt}.title.detail`).d('明细')}
        </span>
        <div
          style={{
            float: 'right',
            display: 'flex',
            flexDirection: 'row-reverse',
            alignItems: 'center',
            marginRight: '0.3rem',
          }}
        >
          <ExcelExport
            method="GET"
            requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-delivery-doc/material-lot/list/export/ui`}
            queryParams={{ instructionDocLineId }}
          />
        </div>
      </div>
    );
  };

  useDataSetEvent(detailTableDs, 'load', () => {
    setLineId(pre => {
      if (pre) {
        openModal(pre, 'query')
      }
      return pre
    })
  });

  const handleOpenDetail = async (record) => {
    detailTableDs.setQueryParameter('instructionDocLineId', record.get('instructionDocLineId'));
    detailTableDs.query();
    openModal(record.get('instructionDocLineId'))
  };

  const openModal = async (instructionDocLineId, query?) => {
    const { lotList } = detailTableDs.queryDataSet.toData()[0]
    const url = `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-delivery-doc/lot/control/ui`
    const params = {
      lotList,
      instructionDocLineId: instructionDocLineId
    }
    const res: any = await axios.post(url, params)
    let list: Array<any> = []
    let qty = 0
    if (res && res.success) {
      const { rows } = res
      rows.map(item => {
        if (item.qty) {
          qty += item.qty
        }
      })
      list = rows
    } else {
      notification.error({
        message: res.message
      })
    }
    detailTableDs.queryDataSet.current.set('qty', qty)
    if (query) {
      modal.update({
        key: 'receipt',
        onCancel: () => {
          detailTableDs.loadData([])
          detailTableDs.queryDataSet.loadData([
            { lotList: [] }
          ])
        },
        children: <>
          <Table dataSet={detailTableDs}
            queryBar={TableQueryBarType.professionalBar}
            summaryFieldsLimit={1}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            columns={detailColumns} />
          {(list||[]).length>0&&<Form columns={5}>
            {list.map(item => {
              return <span key={item.lot}>
                <label>
                  {item.lot}:
                  <Output
                    readOnly
                    defaultValue={item.qty}
                  />
                </label>
              </span>
            })}
          </Form>}
        </>,
      });
    } else {
      modal=   Modal.open({
        key: 'receipt',
        title: createModalTitle(instructionDocLineId),
        destroyOnClose: true,
        drawer: true,
        closable: true,
        keyboardClosable: true,
        style: {
          width: 1200,
        },
        onCancel: () => {
          detailTableDs.loadData([])
          detailTableDs.queryDataSet.loadData([
            { lotList: [] }
          ])
        },
        className: 'hmes-style-modal',
        children: <>
          <Table dataSet={detailTableDs}
            queryBar={TableQueryBarType.professionalBar}
            summaryFieldsLimit={1}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            columns={detailColumns} />
          <Form columns={5}>
            {list.map((item) => {
              return <span key={item.lot}>
                <label>
                  {item.lot}:
                  <Output
                    readOnly
                    defaultValue={item.qty}
                  />
                </label>
              </span>
            })}
          </Form>
        </>,
      });
    }
  }

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryData = tableDs.queryDataSet.current.toData();
    const queryParams = {
      instructionDocNum: queryData.instructionDocNum,
      poHeaderId: queryData.poHeaderId,
      supplierIdList: queryData.supplierIds.toString(),
      materialIdList: queryData.materialIds.toString(),
      siteIdList: queryData.siteIds.toString(),
      receiveLocatorIdList: queryData.receiveLocatorIds.toString(),
      expectedArrivalTimeFrom: queryData.expectedArrivalTimeFrom,
      expectedArrivalTimeTo: queryData.expectedArrivalTimeTo,
      instructionDocIds: tableDs.selected.map(e => e.get('instructionDocId')).toString(),
    }
    Object.keys(queryParams).forEach((i) => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return queryParams;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.PurchaseReceiptReport`).d('采购收货明细报表')}>
        <ExcelExport
          method="GET"
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-delivery-doc/export/ui`}
          queryParams={getExportQueryParams}
        />
      </Header>
      <Content>
        <Table
          searchCode="xsfypt1"
          customizedCode="xsfypt1"
          queryFieldsLimit={10}
          dataSet={tableDs}
          columns={columns}
          highLightRow
          showCachedSelection={false}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
          }}
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
        />
        <Collapse
          bordered={false}
          defaultActiveKey={[
            'lineInfo',
          ]}
        >
          <Panel
            header={intl.get(`${modelPrompt}.lineInfo`).d('行信息')}
            key="lineInfo"
          >
            <Table border columns={lineColumns} dataSet={lineTableDs} />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['hwms.PurchaseReceiptReport', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet(tableDS());
      const lineTableDs = new DataSet(lineTableDS());
      const detailTableDs = new DataSet(detailTableDS());

      return {
        tableDs,
        lineTableDs,
        detailTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
)(PurchaseReceiptReport);
