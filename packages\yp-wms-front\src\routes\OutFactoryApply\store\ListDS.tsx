import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';

import { BASIC } from '@utils/config';
import intl from 'utils/intl';

// const RTHost = '/rt-mes-24510';


const tenantId = getCurrentOrganizationId();
const modelPrompt = 'hwms.outFactoryApply.fields';

const tableDS = () => ({
  autoQuery: false,
  selection: 'multiple',
  fields: [
    {
      name: 'siteCode',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      type: FieldType.string,
    },
    {
      name: 'instructionDocNum',
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('出厂单编码'),
      type: FieldType.string,
    },
    {
      name: 'statusMeaning',
      label: intl.get(`${modelPrompt}.statusMeaning`).d('出厂单状态'),
      type: FieldType.string,
    },
    {
      name: 'applicationTypeMeaning',
      label: intl.get(`${modelPrompt}.applicationTypeMeaning`).d('物资类别'),
      type: FieldType.string,
    },
    {
      name: 'driver',
      label: intl.get(`${modelPrompt}.driver`).d('司机'),
      type: FieldType.string,
    },
    {
      name: 'licenseNum',
      label: intl.get(`${modelPrompt}.licenseNum`).d('车牌号'),
      type: FieldType.string,
    },
    {
      name: 'factoryDate',
      label: intl.get(`${modelPrompt}.factoryDate`).d('出厂日期'),
      type: FieldType.string,
    },
    {
      name: 'applyUserName',
      label: intl.get(`${modelPrompt}.applyUserName`).d('申请人'),
      type: FieldType.string,
    },
    {
      name: 'applyDate',
      label: intl.get(`${modelPrompt}.applyDate`).d('申请时间'),
      type: FieldType.string,
    },
    {
      name: 'attribute1',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerLov`).d('供应商/承运商单位'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
  ],
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      noCache: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'instructionDocNum',
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('出厂单编码'),
      type: FieldType.string,
    },
    {
      name: 'status',
      label: intl.get(`${modelPrompt}.status`).d('出厂单状态'),
      lookupCode: 'WMS.FACTORY_APPLICATION_STATUS',
      type: FieldType.string,
    },
    {
      name: 'applicationType',
      label: intl.get(`${modelPrompt}.applicationType`).d('物资类别'),
      lookupCode: 'WMS.FACTORY_APPLICATION_TYPE',
      type: FieldType.string,
    },
    {
      name: 'driver',
      label: intl.get(`${modelPrompt}.driver`).d('司机'),
      type: FieldType.string,
    },
    {
      name: 'licenseNum',
      label: intl.get(`${modelPrompt}.licenseNum`).d('车牌号'),
      type: FieldType.string,
    },
    {
      name: 'factoryDateFrom',
      label: intl.get(`${modelPrompt}.factoryDateFrom`).d('出厂日期从'),
      type: FieldType.dateTime,
      max: 'factoryDateTo',
    },
    {
      name: 'factoryDateTo',
      label: intl.get(`${modelPrompt}.factoryDateTo`).d('出厂日期至'),
      type: FieldType.dateTime,
      min: 'factoryDateFrom',
    },
    {
      name: 'applyUserName',
      label: intl.get(`${modelPrompt}.applyUserName`).d('申请人'),
      type: FieldType.string,
    },
    {
      name: 'applyDateFrom',
      label: intl.get(`${modelPrompt}.applyDateFrom`).d('申请日期从'),
      type: FieldType.dateTime,
      max: 'applyDateTo',
    },
    {
      name: 'applyDateTo',
      label: intl.get(`${modelPrompt}.applyDateTo`).d('申请日期至'),
      type: FieldType.dateTime,
      min: 'applyDateFrom',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierLov`).d('供应商/承运商单位'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      noCache: true,
    },
    {
      name: 'attribute1',
      type: FieldType.string,
      bind: 'supplierLov.supplierCode',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-factory-application-manage/heads`,
        method: 'GET',
      };
    },
  },
});

const lineTableDS = () => ({
  autoQuery: false,
  selection: false,
  fields: [
    {
      name: 'lineNum',
      label: intl.get(`${modelPrompt}.lineNum`).d('行号'),
      type: FieldType.string,
    },
    {
      name: 'factoryTypeMeaning',
      label: intl.get(`${modelPrompt}.factoryTypeMeaning`).d('出场类型'),
      type: FieldType.string,
    },
    {
      name: 'factoryCode',
      label: intl.get(`${modelPrompt}.factoryCode`).d('出厂编码'),
      type: FieldType.string,
    },
    {
      name: 'factoryDescription',
      label: intl.get(`${modelPrompt}.factoryDescription`).d('出厂描述'),
      type: FieldType.string,
    },
    {
      name: 'factoryInstructionDocNum',
      label: intl.get(`${modelPrompt}.factoryInstructionDocNum`).d('出厂单据'),
      type: FieldType.string,
    },
    {
      name: 'factoryQty',
      label: intl.get(`${modelPrompt}.factoryQty`).d('出场数量'),
      type: FieldType.string,
    },
    {
      name: 'factoryUomCode',
      label: intl.get(`${modelPrompt}.factoryUomCode`).d('单位'),
      type: FieldType.string,
    },
    {
      name: 'attribute2Meaning',
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
      type: FieldType.string,
    },
    {
      name: 'remark',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-factory-application-manage/lines`,
        method: 'GET',
      };
    },
  },
});


export { tableDS, lineTableDS };
