/**
 * @Description: 事务报表平台-行表-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 18:25:02
 * @LastEditTime: 2023-03-21 14:56:34
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.mes.event.materialTransIface';
const tenantId = getCurrentOrganizationId();

export const lineTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  fields: [
    {
      name: 'siteCode',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'minQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.minQty`).d('安全库存'),
    },
    {
      name: 'maxQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maxQty`).d('最大库存'),
    },
    {
      name: 'onhandQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.onhandQty`).d('当前可用库存'),
    },
    {
      name: 'secureFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secureFlag`).d('是否低于安全库存'),
    },
    {
      name: 'maxInventoryFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maxInventoryFlag`).d('是否超最大库存'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('低于安全库存数量'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('事件时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-minmax-onhands/list/get/ui/history`,
        method: 'GET',
      };
    },
  },
});
