/**
 * @Description: 销售发运平台 - 入口页面DS
 * @Author: <EMAIL>
 * @Date: 2022/2/9 18:02
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { DataSet } from 'choerodon-ui/pro';
import { filterNullValueObject, getCurrentOrganizationId } from 'utils/utils';
import { searchCopy } from '@utils/utils';

const modelPrompt = 'hwms.PurchaseReceiptReport';
const tenantId = getCurrentOrganizationId();

const tableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  autoLocateFirst: true,
  queryDataSet: new DataSet({
    events: {
      update({ record, name, value }) {
        searchCopy(
          [
            'materialLotCodes',
          ],
          name,
          record,
          value,
        );
      }
    },
    fields: [
      {
        name: 'instructionDocNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.instructionDocNum`).d('送货单号'),
      },
      {
        name: 'poLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.poNum`).d('采购订单号'),
        lovCode: `${BASIC.LOV_CODE_BEFORE}.PO`,
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'poHeaderId',
        bind: 'poLov.poHeaderId',
      },
      {
        name: 'supplierLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.supplierCode`).d('供应商'),
        lovCode: 'MT.MODEL.SUPPLIER',
        ignore: FieldIgnore.always,
        multiple: true,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'supplierIds',
        bind: 'supplierLov.supplierId',
      },
      {
        name: 'materialLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        lovCode: 'MT.METHOD.MATERIAL',
        ignore: FieldIgnore.always,
        multiple: true,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'materialIds',
        bind: 'materialLov.materialId',
      },
      {
        name: 'siteLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
        lovCode: 'MT.MODEL.SITE',
        ignore: FieldIgnore.always,
        multiple: true,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'siteIds',
        bind: 'siteLov.siteId',
      },
      {
        name: 'materialLotCodes',
        multiple: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotCodes`).d('物料批'),
      },
      {
        name: 'locatorLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialCode`).d('接收仓库'),
        lovCode: 'MT.MODEL.LOCATOR',
        ignore: FieldIgnore.always,
        multiple: true,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'receiveLocatorIds',
        bind: 'locatorLov.locatorId',
      },
      {
        name: 'expectedArrivalTimeFrom',
        type: FieldType.dateTime,
        max: 'expectedArrivalTimeTo',
        label: intl.get(`${modelPrompt}.expectedArrivalTimeFrom`).d('收货时间从'),
      },
      {
        name: 'expectedArrivalTimeTo',
        type: FieldType.dateTime,
        min: 'expectedArrivalTimeFrom',
        label: intl.get(`${modelPrompt}.expectedArrivalTimeTo`).d('收货时间至'),
      },
    ],
  }),
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('送货单号'),
    },
    {
      name: 'poNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poNumber`).d('采购订单号'),
    },
    {
      name: 'instructionDocStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatusDesc`).d('送货单状态'),
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocTypeDesc`).d('送货单类型'),
    },
    {
      name: 'sourceSystemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSystemDesc`).d('来源系统'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'realName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.realName`).d('采购员'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'receiveDoodsTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveDoodsTime`).d('收货时间'),
    },
    {
      name: 'expectedArrivalTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.expectedArrivalTime`).d('预计到货时间'),
    },
  ],
  transport: {
    read: ({ data }) => {
      const { supplierIds, materialIds, siteIds, receiveLocatorIds, materialLotCodes, ...otherData } = data;


      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-delivery-doc/list/ui`,
        method: 'GET',
        data: filterNullValueObject({
          ...otherData,
          materialLotCodes: materialLotCodes.join(),
          supplierIdList: supplierIds ? supplierIds.toString() : null,
          materialIdList: materialIds ? materialIds.toString() : null,
          siteIdList: siteIds ? siteIds.toString() : null,
          receiveLocatorIdList: receiveLocatorIds ? receiveLocatorIds.toString() : null,
        }),
      };
    },
  },
});

const lineTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  autoLocateFirst: true,
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'quantityOrdered',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantityOrdered`).d('采购订单数量'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('送货单制单数量'),
    },
    {
      name: 'actualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualQty`).d('已接收数量'),
    },
    {
      name: 'storageActualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.storageActualQty`).d('已上架数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('接收仓库'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('收货时间'),
    },
    {
      name: 'lineRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineRealName`).d('收货人'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-delivery-doc/line/list/ui?instructionDocId=${data.instructionDocId}`,
        method: 'POST',
      };
    },
  },
});

const detailTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  autoLocateFirst: true,
  queryDataSet: new DataSet({
    fields: [
      {
        name: 'lotList',
        type: FieldType.string,
        multiple: true,
        label: intl.get(`${modelPrompt}.lotList`).d('批次'),
      },
      {
        name: 'qty',
        type: FieldType.number,
        ignore: FieldIgnore.always,
        disabled: true,
        readOnly: true,
        label: intl.get(`${modelPrompt}.qty`).d('汇总'),
      },
    ]
  }),
  fields: [
    {
      name: 'materialIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentification`).d('物料批标识'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatusDesc`).d('物料批状态'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('所在容器'),
    },
    {
      name: 'sumActualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumActualQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'receiveBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveBy`).d('接收人'),
    },
    {
      name: 'receiveLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveLocatorCode`).d('接收货位'),
    },
    {
      name: 'receiveData',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveData`).d('接收时间'),
    },
    {
      name: 'putOnBy',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.putOnBy`).d('上架人'),
    },
    {
      name: 'putOnLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.putOnLocatorCode`).d('上架货位'),
    },
    {
      name: 'putOnData',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.putOnData`).d('上架时间'),
    },

  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-delivery-doc/material-lot/list/ui?instructionDocLineId=${data.instructionDocLineId}`,
        method: 'POST',
      };
    },
  },
});

export { tableDS, lineTableDS, detailTableDS };
