/**
 * 杂项工作台-详情文件
 * @date 2023-1-3
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState } from 'react';
import {
  DataSet,
  Table,
  TextField,
  Form,
  NumberField,
  Lov,
  Select,
  Button,
  Switch,
  Spin,
  DateTimePicker
} from 'choerodon-ui/pro';
import { Popconfirm, Badge, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import notification from 'utils/notification';
import { useRequest } from '@components/tarzan-hooks';
import { headerFormDS, lineTableDS } from '../stores/DetailDS';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.receive.miscellaneous';

// 行查询接口
export function AvailableQuantity() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/mt-inv-onhand-quantity/sum-available-qty/ui`,
    method: 'POST',
  };
}

const Order = props => {
  const {
    headerFormDs,
    lineTableDs,
    match: {
      path,
      params: { id },
    },
    customizeForm,
    customizeTable,
  } = props;

  const [hasPermissionFlag, setHasPermissionFlag] = useState(false);
  const [changeAll, setChangeAll] = useState(false);
  const [canEdit, setCanEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [headerSiteId, setHeaderSiteId] = useState(undefined);
  const [toleranceGroup, setToleranceGroup] = useState({});
  const [instructionDocStatus, setInstructionDocStatus] = useState('');

  const availableQuantity = useRequest(AvailableQuantity(), {
    manual: true,
  });

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      headerFormDs.loadData([
        {
          instructionDocStatus: 'RELEASED',
        },
      ]);
      lineTableDs.loadData([]);
      setInstructionDocStatus('RELEASED');
    } else {
      setCanEdit(false);
      setInstructionDocStatus();
      headerFormDs.loadData([]);
      lineTableDs.loadData([]);
      queryDetail();
    }
  }, [id]);

  const queryDetail = async () => {
    setLoading(true);
    headerFormDs.setQueryParameter('instructionDocId', id);
    const res = await headerFormDs.query()
    setLoading(false);
    if (res?.rows?.instructionDoc) {
      const { instructionDoc } = res?.rows;
      const {
        siteId,
        // accountType,
        instructionDocType,
        businessTypes,
        queryType,
      } = instructionDoc;
      setHeaderSiteId(siteId);
      headerFormDs.current.set('internalOrder', {
        internalOrderId: res.rows.instructionDoc.internalOrderId,
        internalOrderCode: res.rows.instructionDoc.internalOrderCode,
      });
      // headerFormDs.loadData([instructionDoc])
      // console.log(headerFormDs.toData())
      // if (accountType === 'INTERNAL_ORDER') {
      //   headerFormDs.current.init('COST_CENTER', null);
      // } else {
      //   headerFormDs.current.init('internalOrder', null);
      // }
      const result = await getToleranceGroup({
        instructionDocType,
      });
      setInstructionDocStatus(instructionDoc.instructionDocStatus);
      if (res?.rows?.instructionList) {
        const { instructionList } = res?.rows;
        // console.log('res.rows.instructionDoc', res.rows.instructionDoc)
        const permissionFlagList = [];
        lineTableDs.loadData(
          instructionList.map(item => {
            if (item.permissionFlag !== 'Y') {
              permissionFlagList.push(item);
            }
            return {
              ...item,
              siteId,
              businessTypes,
              queryType,
              sourceSumOnhandQty: item.locatorQty,
              // instructionDocType: res.rows.instructionDoc.instructionDocType,
              locatorRequiredFlag: queryType === 'TARGET' ? result.rows.toLocatorRequiredFlag : result.rows.fromLocatorRequiredFlag,
              // locatorRequiredFlag: res.rows.instructionDoc.instructionDocType === '202_COST_CENTER_RETURN_DOC' || res.rows.instructionDoc.instructionDocType === '202_INTERNAL_ORDER_RETURN_DOC' ? result.rows.toLocatorRequiredFlag : result.rows.fromLocatorRequiredFlag,
            };
          }),
        );
        setHasPermissionFlag(permissionFlagList.length === 0);
      }
    } else {
      notification.error({
        message: res.message,
      });
    }
  };

  const addLine = () => {
    let maxLineNumber = 0;
    lineTableDs.toData().forEach(item => {
      const { lineNumber } = item;
      if (lineNumber >= maxLineNumber) {
        maxLineNumber = lineNumber;
      }
    });
    const { toleranceFlag, toleranceType, toleranceMinValue, toleranceMaxValue } = toleranceGroup;
    console.log('toleranceGroup', toleranceGroup)
    const headerData = headerFormDs.current.toData();
    lineTableDs.create(
      {
        lineNumber: Math.floor(maxLineNumber / 10) * 10 + 10,
        site: headerData.site,
        siteId: headerData.siteId,
        businessTypes: headerData.businessTypes,
        queryType: headerData.queryType,
        warehouse: headerData.warehouse,
        locator: headerData.locator,
        instructionStatus: 'RELEASED',
        toleranceFlag: toleranceFlag || 'N',
        toleranceType,
        toleranceMinValue,
        toleranceMaxValue,
        // instructionDocType: toleranceGroup.instructionDocType,
        locatorRequiredFlag: headerData.queryType === 'TARGET' ? toleranceGroup.toLocatorRequiredFlag : toleranceGroup.fromLocatorRequiredFlag,
        // locatorRequiredFlag: toleranceGroup.instructionDocType === '202_COST_CENTER_RETURN_DOC' || toleranceGroup.instructionDocType === '202_INTERNAL_ORDER_RETURN_DOC' ? toleranceGroup.toLocatorRequiredFlag : toleranceGroup.fromLocatorRequiredFlag,
      },
      0,
    );
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push(`/hwms/in-library/miscellaneous-new/list`);
    } else {
      setCanEdit(false);
      queryDetail();
    }
  };

  const headerLovChange = async (type, value) => {
    if (type === 'instructionDocType') {
      headerFormDs.current.init('accountType', null);
      headerFormDs.current.init('costcenter', null);
      headerFormDs.current.init('internalOrder', null);
      headerFormDs.current.init('warehouse', null);
      headerFormDs.current.init('accountTypeDesc', null);
      headerFormDs.current.init('accountType', null);
      headerFormDs.current.init('internalOrderCategoryDesc', null);
      headerFormDs.current.init('costcenterCategoryDesc', null);
      if (value !== null) {
        // 建单时根据指令执行规则自动带出允差标识、允差类型、上允差、下允差 新建行时代到行上
        getToleranceGroup(value);
      }
      lineTableDs.loadData([]);
    }
    if (type === 'site') {
      setHeaderSiteId(value?.siteId);
      headerFormDs.current.init('costcenter', null);
      headerFormDs.current.init('internalOrder', null);
      lineTableDs.loadData([]);
    }
    if (type === 'accountType') {
      headerFormDs.current.init('costcenter', null);
      headerFormDs.current.init('internalOrder', null);
    }
    if (type === 'warehouse') {
      headerFormDs.current.init('locator', null);
    }
  };

  const getToleranceGroup = async value => {
    const { instructionDocType } = value;
    if (instructionDocType) {
      const res = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/miscellaneous/tolerance/get/ui`, {
        method: 'get',
        query: {
          instructionDocType,
        },
      })
      if (res?.success) {
        if (res?.rows) {
          setToleranceGroup(res?.rows);
          headerFormDs.current.init('accountTypeDesc', res.rows.accountTypeDesc);
          headerFormDs.current.init('accountType', res.rows.accountType);
          headerFormDs.current.init('accountCategory', res.rows.accountCategory);

          // 修改成本中心类型与内部订单类型的数据
          if (res.rows.accountType === 'INTERNAL_ORDER') {
            headerFormDs.current.init('internalOrderCategoryDesc', res.rows.accountCategoryDesc);
          } else if (res.rows.accountType === 'COST_CENTER') {
            headerFormDs.current.init('costcenterCategoryDesc', res.rows.accountCategoryDesc);
          }
          headerFormDs.current.init('accountTypeList', res.rows.accountTypeList);
          headerFormDs.current.init('accountCategoryList', res.rows.accountCategoryList);
          if (res.rows.accountTypeList && res.rows.accountTypeList.length === 1) {
            const accountTypeListKeys = Object.keys(res.rows.accountTypeList[0]);
            headerFormDs.current.init('accountType', accountTypeListKeys[0]);
          }
          return res;
        }
      } else {
        notification.error({
          message: res && res.message,
        });
      }
    } else {
      setToleranceGroup({});
      headerFormDs.current.init('accountTypeList', []);
      headerFormDs.current.init('accountCategoryList', []);
    }
  };

  const getSourceSumOnhandQty = record => {
    record.set('sourceSumOnhandQty', null);
    const value = record.toData();
    if (value.materialId && value.siteId && value.warehouseId) {
      availableQuantity.run({
        params: {
          materialId: value.materialId,
          siteId: value.siteId,
          revisionCode: value.revisionCode || '',
          locatorId: value.locatorId || value.warehouseId,
          ownerType: value.sourceOrderId ? 'OI' : '',
          ownerId: value.sourceOrderId ? value.sourceOrderId : '',
        },
        onSuccess: res => {
          record.set('sourceSumOnhandQty', res);
        },
        onFailed: () => {
          record.set('sourceSumOnhandQty', null);
        },
      });
    } else {
      record.set('sourceSumOnhandQty', null);
    }
  };

  const getRevision = record => {
    const value = record.toData();
    if (value.revisionFlag === 'Y' && !value.revisionCode) {
      request(`${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`, {
        method: 'get',
        query: {
          tenantId,
          siteIds: value.siteId,
          materialId: value.materialId,
          kid: value.kid,
        },
      }).then(res => {
        if (res?.success) {
          if (res?.rows && res.rows.length > 0) {
            record.set('revisionCode', res.rows[0]);
          }
        } else {
          notification.error({
            message: res && res.message,
          });
        }
      });
    }
  };

  const handleSave = async () => {
    if (headerFormDs) {
      headerFormDs.forEach(item => {
        item.set({ nowDate: new Date().getTime() });
      });
    }
    if (lineTableDs) {
      lineTableDs.forEach(item => {
        item.set({ nowDate: new Date().getTime() });
      });
    }

    const validateHeader = await headerFormDs.validate();
    const validateLine = await lineTableDs.validate();
    const poIds = [];
    if (validateHeader && validateLine) {
      const headerData = headerFormDs.current.toData();
      const {
        site,
        instructionDocTypeObj,
        warehouse,
        locator,
        internalOrder,
        costcenter,
        ...instructionDoc
      } = headerData;
      const lines = lineTableDs.toData();
      lines.forEach(item => {
        if (poIds.indexOf(item?.poHeaderId) === -1) {
          poIds.push(item.poHeaderId);
        }
      });
      const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/miscellaneous/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_DETAIL.LINE`;

      return request(url, {
        method: 'POST',
        body: {
          instructionDoc,
          instructionList: lines,
        },
      }).then(res => {
        if (res && res.success) {
          setCanEdit(false);
          setLoading(false);
          setInstructionDocStatus('');
          if (id === 'create') {
            props.history.push(`/hwms/in-library/miscellaneous-new/detail/${res.rows}`);
          } else {
            queryDetail();
          }
        } else {
          notification.error({
            message: res && res.message,
          });
        }
      });
    }
    return false;

  };

  const fromWarehouseFooter = (okBtn, cancelBtn) => {
    const allBtn = (
      <Button
        onClick={() => {
          setChangeAll(true);
          setTimeout(() => {
            okBtn.props.onClick();
          });
        }}
      >
        {intl.get(`${modelPrompt}.all.rows`).d('应用至所有行')}
      </Button>
    );
    return (
      <div>
        {allBtn}
        {cancelBtn}
        {okBtn}
      </div>
    );
  };

  const wareHouseChange = (record, type, value) => {
    if (type === 'warehouse' && value === null) {
      record.set('locator', null);
    }
    if (changeAll) {
      lineTableDs.forEach(item => {
        item.set(type, value);
        if (type === 'warehouse') {
          item.set('locator', null);
        }
        if (type === 'locator') {
          item.set('warehouse', record.get('warehouse'));
        }
        item.set('sourceSumOnhandQty', null);

        const data = item.toData();
        if (data.materialId && data.siteId && data.warehouseId) {
          request(
            `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/mt-inv-onhand-quantity/sum-available-qty/ui`,
            {
              method: 'POST',
              body: {
                lineNumber: data.lineNumber,
                materialId: data.materialId,
                siteId: data.siteId,
                revisionCode: data.revisionCode || '',
                locatorId: data.locatorId || data.warehouseId,
                ownerType: data.sourceOrderId ? 'OI' : '',
                ownerId: data.sourceOrderId ? data.sourceOrderId : '',
              },
            },
          )
            .then(res => {
              if (res?.success) {
                item.set('sourceSumOnhandQty', res.rows);
              }
            })
            .catch(() => {
              item.set('sourceSumOnhandQty', null);
            });
        } else {
          record.set('sourceSumOnhandQty', null);
        }
      });
      setChangeAll(false);
    } else {
      getSourceSumOnhandQty(record);
    }
  };

  const toWareHouseChange = (record, type, value) => {
    if (type === 'toWarehouse' && value === null) {
      record.set('toLocator', null);
    }
    if (changeAll) {
      lineTableDs.forEach(item => {
        item.set(type, value);
        if (type === 'toWarehouse') {
          item.set('toLocator', null);
        }
        if (type === 'toLocator') {
          item.set('toWarehouse', record.get('toWarehouse'));
        }
      });
      setChangeAll(false);
    } else {
      getSourceSumOnhandQty(record);
    }
  };

  // 新建表格后可删除
  const handleDelete = record => {
    lineTableDs.delete(record, false);
  };

  // 新建行信息表配置
  const lineTableColumns = [
    {
      title: (
        <Button
          icon="add"
          disabled={!headerSiteId || !canEdit}
          onClick={addLine}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      name: 'config',
      align: 'center',
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => handleDelete(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <Button
            disabled={!canEdit || record.get('instructionDocLineId')}
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
      lock: 'left',
    },
    {
      name: 'lineNumber',
      width: 60,
      lock: 'left',
      editor: record =>
        canEdit &&
        !record.get('instructionDocLineId') &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <NumberField nonStrictStep precision={0} step={1} />
        ),
    },
    {
      name: 'identifyType',
      width: 120,
      lock: 'left',
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        } if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'material',
      width: 140,
      editor: record =>
        canEdit &&
        !record.get('instructionDocLineId') &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            onChange={() => {
              record.set('revisionCode', null);
              getSourceSumOnhandQty(record);
              getRevision(record);
            }}
          />
        ),
    },
    {
      name: 'revisionCode',
      width: 80,
      editor: record =>
        canEdit &&
        record.get('revisionFlag') === 'Y' &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Select />
        ),
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'quantity',
      width: 120,
      editor: record =>
        canEdit &&
        !record.get('instructionDocLineId') &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <NumberField nonStrictStep precision={6} step={1} />
        ),
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'instructionStatus',
      width: 80,
    },
    // {
    //   name: 'soNumberObj',
    //   width: 140,
    //   editor: record =>
    //     canEdit &&
    //     (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
    //       <Lov
    //         onChange={() => {
    //           getSourceSumOnhandQty(record);
    //         }}
    //       />
    //     ),
    //   renderer: ({ record }) => record.get('soNumber'),
    // },
    // {
    //   name: 'soLineNum',
    //   width: 100,
    // },
    {
      name: 'warehouse',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'warehouse', value);
            }}
          />
        ),
    },
    // {
    //   name: 'locator',
    //   width: 140,
    //   editor: record =>
    //     canEdit &&
    //     (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
    //       <Lov
    //         tableProps={{
    //           highLightRow: true,
    //         }}
    //         modalProps={{
    //           footer: (okBtn, cancelBtn, modal) => {
    //             return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
    //           },
    //         }}
    //         onChange={value => {
    //           wareHouseChange(record, 'locator', value);
    //         }}
    //       />
    //     ),
    // },
    {
      name: 'toWarehouse',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              toWareHouseChange(record, 'toWarehouse', value);
            }}
          />
        ),
    },
    {
      name: 'toLocator',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              toWareHouseChange(record, 'loctoLocatorator', value);
            }}
          />
        ),
    },
    {
      name: 'sourceSumOnhandQty',
      width: 100,
    },
    {
      name: 'toleranceFlag',
      width: 80,
      align: 'center',
      editor: record =>
        id === 'create' &&
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Switch
            onChange={() => {
              record.set('toleranceType', null);
              record.set('toleranceMinValue', null);
              record.set('toleranceMaxValue', null);
            }}
          />
        ),
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      width: 100,
      editor: record =>
        id === 'create' &&
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <Select
            onChange={() => {
              record.set('toleranceMinValue', null);
              record.set('toleranceMaxValue', null);
            }}
          />
        ),
    },
    {
      name: 'toleranceMaxValue',
      width: 120,
      editor: record =>
        id === 'create' &&
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <NumberField nonStrictStep precision={6} step={1} />
        ),
    },
    {
      name: 'toleranceMinValue',
      width: 120,
      editor: record =>
        id === 'create' &&
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <NumberField nonStrictStep precision={6} step={1} />
        ),
    },
    {
      name: 'remark',
      width: 140,
      editor: record =>
        canEdit &&
        (record.get('permissionFlag') === 'Y' || hasPermissionFlag || id === 'create') && (
          <TextField />
        ),
    },
  ];

  return (
    <div className="hmes-style">
      <Header
        title='成本中心领退平台'
        backPath="/hwms/in-library/miscellaneous-new/list"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
              disabled={instructionDocStatus !== 'RELEASED'}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <PermissionButton type="c7n-pro" icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </PermissionButton>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            disabled={instructionDocStatus !== 'RELEASED' || !hasPermissionFlag}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
          <Panel
            header={intl.get(`${modelPrompt}.header.information`).d('头信息')}
            key="basicInfo"
            dataSet={headerFormDs}
          >
            <Spin dataSet={headerFormDs}>
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_DETAIL.HEAD`,
                },
                <Form
                  disabled={!canEdit}
                  dataSet={headerFormDs}
                  columns={3}
                  labelLayout="horizontal"
                  labelWidth={110}
                >
                  <TextField name="instructionDocNum" />
                  <Lov
                    name="instructionDocTypeObj"
                    onChange={value => {
                      headerLovChange('instructionDocType', value);
                    }}
                    disabled={id !== 'create' || !canEdit}
                  />
                  <Select name="instructionDocStatus" />
                  <Lov
                    name="site"
                    onChange={value => {
                      headerLovChange('site', value);
                    }}
                    disabled={id !== 'create' || !canEdit}
                  />
                  <TextField
                    name="accountTypeDesc"
                  />
                  <TextField name="costcenterCategoryDesc" />
                  <Lov name="costcenter" disabled={id !== 'create' || !canEdit} />
                  {/* <TextField name="internalOrderCategoryDesc" /> */}
                  <Lov name="internalOrder" disabled={id !== 'create' || !canEdit} />
                  <TextField name="remark" />
                  <TextField name="projectCode" />
                  <DateTimePicker name="demandTime"/>
                  <Select name="evaluationType" />
                </Form>,
              )}
            </Spin>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="location"
            dataSet={lineTableDs}
          >
            <Spin spinning={loading}>
              {customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_DETAIL.LINE`,
                },
                <Table dataSet={lineTableDs} highLightRow={false} columns={lineTableColumns} />,
              )}
            </Spin>
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['tarzan.receive.miscellaneous', 'tarzan.common'] }),
  withProps(
    () => {
      const headerFormDs = new DataSet({ ...headerFormDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerFormDs,
        lineTableDs,
      };
    },
    {},
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_DETAIL.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_DETAIL.LINE`,
    ],
  }),
)(Order);
