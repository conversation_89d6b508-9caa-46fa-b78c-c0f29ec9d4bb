/**
 * @Description: 采购退货平台-详情页
 * @Author: <<EMAIL>>
 * @Date: 2023-06-28 15:02:35
 * @LastEditTime: 2023-06-30 17:49:06
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { observer } from 'mobx-react';
import {DataSet, Button, Form, Lov, TextField, Select, DateTimePicker, Attachment} from 'choerodon-ui/pro';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { Collapse } from 'choerodon-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import request from 'utils/request';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { BASIC } from '@utils/config';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import {LabelLayout, ShowValidation} from "choerodon-ui/pro/es/form/enum";
import DetailPageTable from './DetailPageTable';
import { detailDS, lineTableDS } from '../stores/DetailDS';

const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;
const modelPrompt = 'tarzan.wms.purchase.materialApproval';

const PurchaseDetailWms = observer(props => {
  const {
    history,
    match: { path, params },
  } = props;
  const kid = params.id;
  const [canEdit, setCanEdit] = useState(false);
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const lineTableDs = useMemo(() => new DataSet(lineTableDS()), []);
  const [saveLoading, setSaveLoading] = useState(false);

  useEffect(() => {
    if (kid === 'create') {
      setCanEdit(true);
      handleDepartment();
      return;
    }
    // 编辑时
    detailDs.setQueryParameter('processDocId', kid);
    detailQuery();
  }, [kid]);

  const detailQuery = () => {
    detailDs.query().then(res => {
      if (res && res.success) {
        lineTableDs.loadData(res.rows.lineList || []);
      } else {
        notification.error({
          message: res.message || intl.get('hzero.common.notification.error').d('操作失败'),
        });
      }
    });
  };

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  // 查询主岗部门
  const handleDepartment = () => {
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-synthesise-demands/applyUnit/default/ui`, {
      method: 'GET',
    }).then(res => {
      const response = getResponse(res)
      if(response){
        const { unitId, unitName } = response;
        detailDs?.current?.set('unitLov', {
          unitId,
          unitName,
        })
      }
    })
  };

  const handleCancel = useCallback(() => {
    if (kid === 'create') {
      history.push('/hwms/purchase/material-approval');
    } else {
      setCanEdit(false);
      detailQuery();
    }
  }, [kid, detailDs, lineTableDs]);

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    const tableValidateFlag = await lineTableDs.validate();
    if (!validateFlag || !tableValidateFlag) {
      return false;
    }
    if (!lineTableDs.length) {
      notification.error({
        message: intl.get(`${modelPrompt}.noLine`).d('未创建单据行，请检查！'),
      });
      return;
    }
    // if (detailDs.current.toData().demanTypeDescription === 'Y') {
    // }
    const arr = lineTableDs.map(item => {
      return {
        ...item.toData(),
        price: item.toData().qty * item.toData().unitPrice,
      };
    });
    const saveParams = {
      ...detailDs!.current!.toData(),
      status: 'NEW',
      lineList: arr,
    };

    setSaveLoading(true);
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-synthesise-demands/save`, {
      method: 'post',
      body: saveParams,
    }).then(res => {
      if (res.success) {
        if (kid === 'create') {
          history.push(`/hwms/purchase/material-approval/detail/${res.rows}`);
        } else {
          detailDs.setQueryParameter('processDocId', res.rows);
          detailQuery();
        }
        notification.success({});
        setCanEdit(false);
      } else {
        notification.error({ message: res.message });
      }
      setSaveLoading(false);
    });
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/WMS_SYNTHESISE_DEMAND',
      title: '资材审批平台导出',
      search: queryString.stringify({
        title: '资材审批平台导出',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
      }),
    });
  };

  const handleChange = () => {
    // eslint-disable-next-line array-callback-return
    lineTableDs.map(item => {
      item.set('flag', detailDs?.current?.toData()!.demandTypeObj.tag);
    });
  };

  const enclosureProps: any = {
    name: 'attribute1',
    bucketName: 'wms-package',
    bucketDirectory: 'material-approval',
    accept: ['.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const titleName = canEdit?intl.get(`${modelPrompt}.title.create`).d('资材提需平台'):intl.get(`${modelPrompt}.title.detail`).d('资材审批平台');

  return (
    <div className="hmes-style">
      <TarzanSpin dataSet={detailDs} spinning={saveLoading}>
        <Header
          title={titleName}
          backPath="/hwms/purchase/material-approval"
        >
          <Button icon="file_upload" onClick={goImport}>
            {intl.get(`tarzan.common.button.import`).d('导入')}
          </Button>
          {canEdit ? (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['headInfo', 'lineInfo']}>
            <Panel key="headInfo" header={intl.get(`${modelPrompt}.title.headInfo`).d('头信息')}>
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name="processDocNum" />
                <TextField name="applyPersonName" />
                <Lov name="unitLov" />
                <DateTimePicker name="applyTime" />
                <Lov name="createdByLov" />
                <Lov name="siteLov" />
                <Lov name="wareHouseLov" />
                <Lov name="costcenter" />
                <Select name="centralizedUnit" />
                <Select name="demandTypeObj" onChange={handleChange} />
                <TextField name="applyReason" />
                <TextField name="totalPrice" disabled/>
                <Attachment {...enclosureProps} readOnly={!(detailDs.current!.get('siteId') && canEdit)} />
              </Form>
            </Panel>
            <Panel key="lineInfo" header={intl.get(`${modelPrompt}.title.lineInfo`).d('行信息')}>
              <DetailPageTable
                canEdit={detailDs.current!.get('siteId') && canEdit}
                ds={lineTableDs}
                detailDs={detailDs}
                kid={kid}
              />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(PurchaseDetailWms);
