/**
 * @Description: 供应商零件生产日计划报表
 * @Author: <<EMAIL>>
 * @Date: 2023-11-21
 * @LastEditTime: 2023-11-21
 * @LastEditors: <<EMAIL>>
 */
import React, { FC } from 'react';
import { RouteComponentProps } from 'react-router';
import { isNil } from 'lodash';
import intl from 'utils/intl';
import {Table, DataSet, Button} from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import request from 'utils/request';
import { getResponse } from '@utils/utils';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import {ButtonColor} from "choerodon-ui/pro/lib/button/enum";
import { TableDS } from './stores/index';

// const BASIC = {
//   TARZAN_REPORT: '/yp-report-wms-44212',
// }

const modelPrompt = 'tarzan.qms.dailyPlanReport.model';
const tenantId = getCurrentOrganizationId();

interface DailyPlanReportProps extends RouteComponentProps {
  tableDS: any;
}
const DailyPlanReport: FC<DailyPlanReportProps> = props => {
  const { tableDS } = props;

  const columns: ColumnProps[] = [
    {
      name: 'supplierCode',
      width: 150,
    },
    {
      name: 'supplierName',
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
    },
    {
      name: 'supplierDate',
    },
    {
      name: 'planQty',
      width: 120,
    },
    {
      name: 'actualQty',
      width: 120,
    },
    {
      name: 'reachRate',
      width: 120,
    },
  ];

  const getExportQueryParams = async () => {
    let queryParams = {};
    const idList = tableDS.selected.map(item => item?.toData()?.id);
    if (tableDS.selected.length > 0) {
      queryParams = {
        headerIds: idList,
      };
    } else {
      queryParams = tableDS.queryDataSet.current.toData();
      Object.keys(queryParams).forEach(i => {
        if (isNil(queryParams[i])) {
          delete queryParams[i];
        }
      })
    }
    // 请求后台
    const result = await request(`${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-produce-plan/export`, {
      method: 'GET',
      responseType: 'blob',
      query: queryParams,
    });
    const res = getResponse(result);
    if (res) {
      const file = new Blob([res], { type: 'application/vnd.ms-excel' });
      const fileURL = URL.createObjectURL(file);
      const fileName = '供应商零件生产日计划报表.xls';
      const elink = document.createElement('a');
      elink.download = fileName;
      elink.style.display = 'none';
      elink.href = fileURL;
      document.body.appendChild(elink);
      elink.click();
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink);
    }
  };


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('供应商零件生产日计划报表')}>
        <Button color={ButtonColor.primary} icon="download" onClick={() => getExportQueryParams()}>
          导出
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={tableDS}
          columns={columns}
          searchCode="dailyPlanReport"
          queryBar={TableQueryBarType.filterBar}
          queryFieldsLimit={4}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          customizable
          customizedCode="dailyPlanReport"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.wms.dailyPlanReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDS = new DataSet(TableDS());
      return {
        tableDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(DailyPlanReport as any),
);
