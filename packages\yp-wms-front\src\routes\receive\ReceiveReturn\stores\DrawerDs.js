import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';


const modelPrompt = 'tarzan.receive.receiveReturn';
const tenantId = getCurrentOrganizationId();
// ${BASIC.HMES_BASIC}
const endUrl = '';
const HMES_BASIC = BASIC.HMES_BASIC;

const drawerTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: 'multiple',
  cacheSelection: true,
  autoLocateFirst: true,
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据号'),
    },
    {
      name: 'actualQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualQuantity`).d('制单数量'),
    },
    {
      name: 'instructionStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      noCache: true,
    },
    {
      name: 'receivedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receivedQty`).d('已领取数量'),
    },
    {
      name: 'signedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.signedQty`).d('已签收数量'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('已下发数量'),
    },
    {
      name: 'fromLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromLocatorCode`).d('来源仓库'),
    },
    {
      name: 'toLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toLocatorCode`).d('目标仓库'),
    },
    {
      name: 'sourceSubLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSubLocatorCode`).d('来源库位'),
    },
    {
      name: 'specifiedLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specifiedLot`).d('指定批次'),
    },
    {
      name: 'specifiedLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specifiedLevel`).d('指定等级'),
    },
    {
      name: 'autoAllQtySelf',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.prodLine`).d('下达数量'),
      disabled: true,
      dynamicProps: {
        max: ({ record }) => {
          if (!record.get('haveRole')) {
            return record.get('autoAllQty');
          }
          return undefined
        },
        disabled: ({ record }) => {
          return !!record.get('haveRole') && record.get('containers').length > 0
        },
      },
    },
    {
      name: 'containers',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site1`).d('指定托盘'),
      lovCode: 'WMS.REQUISITION_CONTAINER_SPEC',
      noCache: true,
      multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            instructionDocLineId: record.get('instructionDocLineId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('haveRole')
        },
      },
    },
    {
      name: 'haveRole',
    },
    // {
    //   name: 'siteId',
    //   type: FieldType.string,
    //   bind: 'site.siteId',
    // },
  ],
});

export { drawerTableDS };
