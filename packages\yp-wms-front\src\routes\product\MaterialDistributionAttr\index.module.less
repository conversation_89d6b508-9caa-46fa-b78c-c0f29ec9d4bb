/**
 * @feature 物料配送属性维护
 * @date 2021-3-8
 * <AUTHOR> <<EMAIL>>
 */

.head-line-example {
  font-size: 14px;
}

.customer-drawer {
  :global {
    .c7n-pro-modal-footer-drawer {
      text-align: right !important;
    }
  }
}

.customer-form {
  margin-right: 40px;

  :global {
    .c7n-pro-calendar-picker-suffix .icon-date_range:before {
      right: 6px !important;
    }

    .c7n-card.ued-detail-card > .c7n-card-body {
      padding: 16px 40px 24px 16px;
    }

    .icon-language {
      font-size: 14px;
      line-height: 20px;
    }

    .c7n-pro-switch {
      height: 24px !important;
      width: 42px !important;
    }
  }
}

.selectBox {
  :global {
    .c7n-pro-select-box {
      width: 100%;

      label {
        width: 50%;
        text-align: center;
      }

      & > .c7n-pro-radio-button:first-of-type > .c7n-pro-radio-inner {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }

      & > .c7n-pro-radio-button:last-of-type > .c7n-pro-radio-inner {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }

    .c7n-pro-radio-disabled {
      cursor: no-drop;

      .c7n-pro-radio-inner {
        border-color: #d9d9d9 !important;

        & + span {
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }

    .c7n-pro-radio-disabled:hover .c7n-pro-radio-inner + span {
      color: rgba(0, 0, 0, 0.25);
    }

    .c7n-pro-radio-disabled .c7n-pro-radio:checked + .c7n-pro-radio-inner {
      border-color: rgba(0, 0, 0, 0.2) !important;

      & + span {
        color: #333;
      }
    }
  }
}

:global {
  .copy-drawer-modal {
    right: 0;
    height: 100%;
    .c7n-pro-modal-body {
      padding: 0.24rem 0.24rem 0.24rem 0;
    }
  }
}
.load {
  color: #1890ff;
  border-color: #91d5ff;
}

.unload {
  color: #fa8c16;
  border-color: #ffd591;
}

.order {
  background-color: sandybrown !important;
}
