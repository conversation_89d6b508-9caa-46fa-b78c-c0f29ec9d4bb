/**
 * 创建送货单抽屉
 * @date 2023-7-5
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useMemo, useState, useImperativeHandle, forwardRef, useRef } from 'react';
import {
  DataSet,
  Form,
  TextField,
  Table,
  DatePicker,
  NumberField,
  Select,
  Switch,
  Button,
  Tabs,
  Tooltip,
  Icon,
  Spin,
} from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import moment from 'moment';
import { shippingDS, shippingListDS } from '../stores/CreateShippingDS';

const { TabPane } = Tabs;

const modelPrompt = 'tarzan.hmes.purchase.sellOrder';

const CreateShippingDrawer = (props, ref) => {
  const {
    visible,
    handleSuccess = () => { },
    deliverList = [],
    deliverheaderDetailDrawer = {},
    soTypeTag,
  } = props;

  const shippingDs = useMemo(() => {
    return new DataSet(shippingDS());
  }, []);

  const shippingListDs = useMemo(() => {
    return new DataSet(shippingListDS());
  }, []);

  const columns = useMemo(() => {
    return [
      {
        title: <Button icon="add" disabled funcType="flat" shape="circle" size="small" />,
        align: 'center',
        width: 60,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDelete(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <Button icon="remove" funcType="flat" shape="circle" size="small" />
          </Popconfirm>
        ),
        lock: 'left',
      },
      {
        name: 'lineNumber',
        width: 60,
        renderer: ({ record }) => {
          return <span>{(record.index + 1) * 10}</span>;
        },
      },
      {
        name: 'manageMode',
        width: 120,
        // renderer: ({ value }) => {
        //   if (value === 'LOT' || value === 'MAT') {
        //     return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        //   } if (value === 'MATERIAL_LOT' || value === '') {
        //     return intl.get('tarzan.common.physicalManage').d('实物管理');
        //   }
        // },
      },
      {
        name: 'materialCode',
        width: 180,
      },
      {
        name: 'revisionCode',
        width: 120,
      },
      {
        name: 'materialName',
        width: 180,
      },
      {
        name: 'targetSiteCode',
        width: 160,
      },
      {
        name: 'needQty',
        width: 100,
      },
      {
        name: 'processedOrdered',
        width: 120,
      },
      {
        name: 'quantity',
        width: 120,
        editor: () => true && <NumberField nonStrictStep step={parseFloat((10 ** -decimalNumberRef.current).toFixed(decimalNumberRef.current))} precision={decimalNumberRef.current} min={0} />,
      },
      {
        name: 'uomCode',
        width: 80,
      },
      {
        name: 'locatorLov',
        width: 180,
        editor: true,
      },
      {
        name: 'orderFlag',
        width: 100,
        align: 'center',
        editor: () => <Switch />,
      },
      {
        name: 'soNumber',
        width: 120,
      },
      {
        name: 'soLineNum',
        width: 95,
      },
      {
        name: 'toleranceFlag',
        width: 100,
        align: 'center',
        editor: record => (
          <Switch
            onChange={() => {
              clearLerance(record, ['toleranceType', 'toleranceMinValue', 'toleranceMaxValue']);
            }}
          />
        ),
      },
      {
        name: 'toleranceType',
        width: 140,
        editor: record => (
          <Select
            onChange={() => {
              clearLerance(record, ['toleranceMinValue', 'toleranceMaxValue']);
            }}
          />
        ),
      },
      {
        name: 'toleranceMaxValue',
        width: 140,
        editor: () => <NumberField nonStrictStep precision={6} step={1} />,
      },
      {
        name: 'toleranceMinValue',
        width: 140,
        editor: () => <NumberField nonStrictStep precision={6} step={1} />,
      },
      {
        name: 'remark',
        width: 180,
        editor: () => <TextField />,

        // align: 'center',
      },
    ];
  }, [decimalNumber]);

  const { run: getToleranceInfo } = useRequest(
    {
      url: `${BASIC.HMES_BASIC
        }/v1/${getCurrentOrganizationId()}/mt-product-delivery-platform/instruction/tolerance/get/for/ui`,
      method: 'GET',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: getQtyValidate } = useRequest(
    {
      url: `/tznd/v1/${getCurrentOrganizationId()}/mt-material/site/limit/lov/ui`,
      method: 'GET',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  const [decimalNumber, setDecimalNumber] = useState(0);
  const decimalNumberRef = useRef();

  // const getList = useRequest(
  //   {
  //     url: `${
  //       BASIC.HMES_BASIC
  //     }/v1/${getCurrentOrganizationId()}/sale-orders/select/shipDoc`,
  //     method: 'POST',
  //   },
  //   {
  //     manual: true,
  //   },
  // );

  const submitForm = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/sale-orders/create/shipDoc`,
      method: 'POST',
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    onQtyValidate();
    shippingDs.loadData([
      {
        ...(deliverList[0] || {}),
        ...deliverheaderDetailDrawer,
        // instructionDocType: 'DELIVERY_DOC',
        expectedArrivalTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        customerId: deliverList[0]?.customerId,
        customerName: deliverList[0]?.customerName,
        customerSiteId: deliverList[0]?.customerSiteId,
        customerSiteCode: deliverList[0]?.description,
        remark: '',
        soTypeTag: soTypeTag,
      },
    ]);
    shippingListDs.loadData(
      deliverList.map((item, index) => {
        const _max = ((item.quantityOrdered || 0) - (item.processedOrdered || 0)).toFixed(6);
        return {
          ...item,
          urgentFlag: 'N',
          quantityMax: _max > 0 ? _max : 0,
          quantity: item.orderedQuantity - item.processedOrdered,
          lineNumber: index * 10 + 10,
          targetSiteId: deliverList[0]?.shipFromSiteId,
          targetSiteCode: deliverList[0]?.shipFromSiteCode,
          needQty: item.orderedQuantity,
          remark: '',
        };
      }),
    );
    shippingListDs.forEach(record => {
      record.set('newData', 'new');
    });
    shippingDs.forEach(record => {
      record.set('newData', 'new');
    });
    // getListAuth('DELIVERY_DOC', shippingListDs.toData());
  }, [visible]);

  useEffect(() => {
    decimalNumberRef.current = decimalNumber;
  }, [decimalNumber]);

  useImperativeHandle(ref, () => ({
    handleSubmit: async () => {
      const validate1 = await shippingListDs.validate();
      const validate2 = await shippingDs.validate();
      if (!validate1 || !validate2) {
        return false;
      }
      const lines = [];

      shippingListDs.toData().forEach(item => {
        // if (item.permissionFlag === 'Y') {
        lines.push(item);
        // }
      });
      const poIds = [];
      lines.forEach((item, index) => {
        if (poIds.indexOf(item?.poHeaderId) === -1) {
          poIds.push(item.poHeaderId);
        }
        lines[index].lineNumber = index * 10 + 10;
      });
      const headerData = shippingDs.toData()[0];
      return submitForm.run({
        params: {
          ...headerData,
          demandTime: shippingDs.toData()[0].instructionDocTypeTag === "DELIVERY" ? shippingDs.toData()[0].expectedArrivalTime : null,
          expectedArrivalTime: shippingDs.toData()[0].instructionDocTypeTag === "RETURN" ? shippingDs.toData()[0].expectedArrivalTime : null,
          instructionDocLineList: lines,
        },
        onSuccess: res => {
          notification.success();
          handleSuccess(res);
        },
      });
    },
  }));

  // const getListAuth = (type, list) => {
  //   const _list = list.map(item => {
  //     const { lineNumber, poLineId, receiveLocatorId } = item;
  //     return {
  //       lineNumber,
  //       poLineId,
  //       receiveLocatorId,
  //     };
  //   });
  //   // getList.run({
  //   //   params: {
  //   //     instructionDocType: type,
  //   //     lines: _list,
  //   //   },
  //   //   onSuccess: res => {
  //   //     shippingListDs.forEach((item, index) => {
  //   //       item.set('permissionFlag', res.lines?.[index]?.permissionFlag || '');
  //   //       item.set('toleranceFlag', res.lines?.[index]?.toleranceFlag || '');
  //   //       item.set('toleranceType', res.lines?.[index]?.toleranceType || '');
  //   //       item.set('toleranceMaxValue', res.lines?.[index]?.toleranceMaxValue);
  //   //       item.set('toleranceMinValue', res.lines?.[index]?.toleranceMinValue);
  //   //       item.set('businessType', res.lines?.[index]?.businessType);
  //   //       // toLocatorRequiredFlag在头部
  //   //       item.set('toLocatorRequiredFlag', res.toLocatorRequiredFlag)
  //   //       if (res.lines?.[index]?.permissionFlag === 'N') {
  //   //         item.set('locatorCode', "")
  //   //         item.set('receiveLocatorId', "")
  //   //       }
  //   //     });
  //   //   },
  //   // });
  // };

  const onTypeChange = val => {
    console.log('val', val);
    // const instructionDocType = shippingDs.current.get('instructionDocType');
    // if (instructionDocType) {
    //   getListAuth(instructionDocType, shippingListDs.toData());
    // }

    if (val && val !== '') {
      return getToleranceInfo({
        params: {
          instructionDocType: val,
          lovCode: 'MT.SO_DELIVERY_RETURN_DOC_TYPE',
        },
      }).then(res => {
        if (res && res.success) {
          console.log('res', res);
          // getListAuth(val, shippingListDs.toData());
          // setToleranceInfo({
          //   ...res.rows,
          //   businessType: res.rows.minBusinessType,
          // });
          shippingDs.current?.set('businessType', res.rows.minBusinessType);
          shippingDs.current?.set('instructionDocTypeTag', res.rows.instructionDocTypeTag);
          shippingDs.current?.set('fromLocatorRequiredFlag', res.rows.fromLocatorRequiredFlag);
          shippingDs.current?.set('toLocatorRequiredFlag', res.rows.toLocatorRequiredFlag);

          shippingListDs.setState('toLocatorRequiredFlag', res.rows.toLocatorRequiredFlag);
          shippingListDs.setState('fromLocatorRequiredFlag', res.rows.fromLocatorRequiredFlag);
          shippingListDs.setState('instructionDocTypeTag', res.rows.instructionDocTypeTag);
          // 新增界面--切换单据类型时需要更新行上的允差信息
          const lineList = shippingListDs.toData();
          if (lineList.length > 0) {
            const newLine = [];
            lineList.forEach((item) => {
              const _item = {
                ...item,
                // 变更单据类型时需要清空行上的执行仓库
                // targetLocator: null,
                // targetLocatorCode: null,
                // targetLocatorId: null,
                // prepareQty: val === 'SO_DELIVERY_DOC' ? 0 : null,
                // deliveryQty: val === 'SO_DELIVERY_DOC' ? 0 : null,
                // receivingQty: val === 'SO_RETURN_DOC' ? 0 : null,
                toleranceFlag: res.rows.toleranceFlag,
                toleranceType: res.rows.toleranceType,
                toleranceTypeDesc: res.rows.toleranceTypeDesc,
                toleranceMaxValue: res.rows.toleranceMaxValue,
                toleranceMinValue: res.rows.toleranceMinValue,
              };
              newLine.push(_item);
            });
            shippingListDs.loadData(newLine);
          }
        } else {
          // notification.error({message: res.message});
          shippingDs.current?.set('businessType', null);
          shippingDs.current?.set('instructionDocTypeTag', null);
          shippingDs.current?.set('fromLocatorRequiredFlag', null);
          shippingDs.current?.set('toLocatorRequiredFlag', null);

          shippingListDs.setState('toLocatorRequiredFlag', null);
          shippingListDs.setState('fromLocatorRequiredFlag', null);
          shippingListDs.setState('instructionDocTypeTag', null);
          const lineList = shippingListDs.toData();
          if (lineList.length > 0) {
            const newLine = [];
            lineList.forEach((item) => {
              const _item = {
                ...item,
                toleranceFlag: 'N',
                toleranceType: null,
                toleranceTypeDesc: null,
                toleranceMaxValue: null,
                toleranceMinValue: null,
              };
              newLine.push(_item);
            });
            shippingListDs.loadData(newLine);
          }
        }
      });
    }
  };

  // 查询数量位数限制
  const onQtyValidate = () => {
    return getQtyValidate({
      params: {
        siteIds: deliverList[0].shipFromSiteId,
        materialCode: deliverList[0].materialCode,
      },
    }).then(res => {
      if (res && res.content) {
        console.log('decimalNumber', res.content[0].decimalNumber)
        // shippingListDs.current.set('decimalNumber', res.content[0].decimalNumber);
        shippingListDs.setState('decimalNumber', res.content[0].decimalNumber);
        setDecimalNumber(res.content[0].decimalNumber)
      }
    });
  };

  // 新建表格后可删除
  const handleDelete = record => {
    shippingListDs.delete(record, false);
    record.set('lineNumber', undefined);
    setTimeout(() => {
      sortShippingList();
    });
  };

  // 行号重新排序
  const sortShippingList = () => {
    shippingListDs.forEach((record, index) => {
      record.set('lineNumber', index * 10 + 10);
    });
  };

  const clearLerance = (record, keys) => {
    if (keys?.length > 0) {
      keys.forEach(item => {
        record.set(item, undefined);
      });
    }
  };


  const tabARender = React.useCallback(() => {
    return (
      <>
        <span>{intl.get(`${modelPrompt}.line.information`).d('行信息')}</span>
        <span>
          <Tooltip
            placement="top"
            title={
              <span>
                {intl
                  .get(`${modelPrompt}.line.explain`)
                  .d(
                    '若您没有勾选的销售订单行上仓库的权限，该行将会为置灰状态，不能创建发货单，您可以前往人员仓库权限分配维护权限。',
                  )}
              </span>
            }
          >
            <Icon
              style={{
                position: 'relative',
                top: '-2px',
              }}
              type="help_outline"
            />
          </Tooltip>
        </span>{' '}
      </>
    );
  }, []);

  return (
    <Spin spinning={submitForm.loading}>
      <Form dataSet={shippingDs} columns={3} labelLayout="horizontal" labelWidth={110}>
        <TextField name="instructionDocNum" />
        <Select name="instructionDocType" onChange={onTypeChange} />
        <DatePicker name="expectedArrivalTime" mode="dateTime" />
        <TextField name="customerName" />
        <TextField name="customerSiteCode" />
        <TextField name="contactAddress" />
        <TextField name="contactPerson" />
        <TextField name="contactTel" />
        <TextField name="contactFax" />
        <TextField name="logisticsMode" />
        <TextField name="logisticsCompanyCode" />
        <TextField name="logisticsCompanyDesc" />
        <TextField name="paymentType" />
        <TextField name="remark" colSpan={2} />
      </Form>
      <Tabs>
        <TabPane tab={tabARender}>
          <Table
            dataSet={shippingListDs}
            columns={columns}
            filter={record => {
              return record.status !== 'delete';
            }}
          />
        </TabPane>
      </Tabs>
    </Spin>
  );
};

export default forwardRef(CreateShippingDrawer);
