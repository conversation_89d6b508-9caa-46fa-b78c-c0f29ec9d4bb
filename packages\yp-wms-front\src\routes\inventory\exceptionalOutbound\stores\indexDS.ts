/*
 * @Author: NJQ <<EMAIL>>
 * @Date: 2023-08-28 15:04:33
 * @LastEditors: NJQ <<EMAIL>>
 * @LastEditTime: 2023-09-01 18:45:12
 * @FilePath: \yp-wms-front\packages\yp-wms-front\src\routes\inventory\exceptionalOutbound\stores\indexDS.ts
 * @Description:
 *
 * Copyright (c) 2023 by 用户/公司名, All Rights Reserved.
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.inventory.exceptionalOutbound';
const tenantId = getCurrentOrganizationId();

const entranceDS = (): DataSetProps => ({
  autoQuery: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'containerId',
  autoLocateFirst: true,
  queryFields: [
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorLov`).d('仓库'),
      lovCode: `MT.MODEL.LOCATOR_CATEGORY`,
      required: true,
      lovPara: {
        tenantId,
        locatorCategory: 'AREA',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'areaLocatorId',
      type: FieldType.number,
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'outLocationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.outLocationLov`).d('出库位置'),
      lovCode: 'MT.MODEL.LOCATOR01',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            areaLocatorId: record.get('areaLocatorId'),
            locatorType: ['LOCATOR_OUT', 'LOCATOR_BOTH'],
          };
        },
        required: ({ record }) => {
          return record.get('areaLocatorId');
        },
        disabled: ({ record }) => {
          return !record.get('areaLocatorId');
        },
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'outLocationId',
      type: FieldType.number,
      bind: 'outLocationLov.locatorId',
    },
    {
      name: 'instructionDocLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionDocLov`).d('送货单'),
      lovCode: 'YP_WMS.MES.INSTRUCTION_DOC',
      lovPara: {
        tenantId,
        instructionDocType: 'DELIVERY_DOC',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'instructionDocId',
      type: FieldType.number,
      bind: 'instructionDocLov.instructionDocId',
    },
    {
      name: 'autoCodes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specificStorageLocation`).d('具体存储货位'),
    },
    {
      name: 'goodsAllocationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.goodsAllocation`).d('货位'),
      lovCode: 'MT.MODEL.LOCATOR',
      multiple: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'locatorIdLists',
      bind: 'goodsAllocationLov.locatorId',
    },
    {
      name: 'loadDetailFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadDetailFlag`).d('是否空托'),
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'containerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerLov`).d('托盘号'),
      lovCode: 'YP_WMS.CONTAINER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'containerIdList',
      type: FieldType.string,
      bind: 'containerLov.containerId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料编码'),
      lovCode: `MT.MATERIAL`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'materialLov.materialId',
    },
    {
      name: 'specifiedLevelLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.specifiedLevelLov`).d('档位'),
      lovCode: `WMS.MATERIAL_SPECIFIED_LEVEL`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'specifiedLevel',
      type: FieldType.string,
      bind: 'specifiedLevelLov.specifiedLevel',
    },
    {
      name: 'containerTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerType`).d('容器类型'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.CONTAINER_TYPE_CODE`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'containerTypeId',
      type: FieldType.number,
      bind: 'containerTypeLov.containerTypeId',
    },
  ],
  fields: [
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器条码'),
    },
    {
      name: 'containerTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeCode`).d('容器类型'),
    },
    {
      name: 'containerTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeDescription`).d('容器类型名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'specifiedLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specifiedLevel`).d('档位'),
    },
    {
      name:'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'containerNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerNumber`).d('数量'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位'),
    },
    {
      name: 'areaLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库'),
    },
    {
      name: 'autoCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoCode`).d('具体存储货位'),
    },

    {
      name: 'qualityStatusDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDescription`).d('质量状态'),
    },
    {
      name: 'containerStatusDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerStatusDescription`).d('容器状态'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('送货单'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/exceptional-warehouse-out/query/head/ui`,
        method: 'GET',
      };
    },
  },
});

const lineDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器条码'),
    },
    {
      name: 'loadObjectTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadObjectTypeDescription`).d('对象类型'),
    },
    {
      name: 'containerObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerObjectCode`).d('对象编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'specifiedLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specifiedLevel`).d('档位'),
    },
    {
      name: 'containerNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerNumber`).d('容器下物料批数量合'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位'),
    },
    {
      name: 'areaLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库'),
    },
    {
      name: 'qualityStatusDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDescription`).d('质量状态'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'stocktakeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeFlag`).d('盘点标识'),
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('冻结标识'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'inLocatorTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTime`).d('收货日期'),
    },
    {
      name: 'container',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerDetail`).d('容器明细'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/exceptional-warehouse-out/query/line/ui`,
        method: 'GET',
      };
    },
  },
});

const detailDS=(): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows',
  // totalKey: 'rows.totalElements',
  paging:false,
  fields: [
    {
      name: 'loadObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.materialLoadObjectCode`).d('对象编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位'),
    },
    {
      name: 'loadObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadObjectTypeDescription`).d('对象类型'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadObjectCode`).d('容器条码'),
    },
    {
      name: 'inLocatorTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTime`).d('收货日期'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
    },
    {
      name: 'parentLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.parentLocatorCode`).d('仓库'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
    },
    {
      name: 'stocktakeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeFlag`).d('盘点标识'),
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('冻结标识'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
  ],
  transport: {
    read: () => {
      return {
        // url: `${BASIC.HMES_BASIC}/v1/${tenantId}/exceptional-warehouse-out/query/ui`,
        url:`${BASIC.HMES_BASIC}/v1/${tenantId}/exceptional-warehouse-out/query/ui`,
        method: 'GET',
      };
    },
  },
});

export { entranceDS, lineDS,detailDS };
