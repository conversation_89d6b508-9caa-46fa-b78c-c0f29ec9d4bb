/*
 * @Author: NJQ <<EMAIL>>
 * @Date: 2023-08-28 15:04:27
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-03-05 17:43:57
 * @FilePath: \yp-wms-front\packages\yp-wms-front\src\routes\inventory\exceptionalOutbound\OutboundList\index.tsx
 * @Description:
 *
 * Copyright (c) 2023 by 用户/公司名, All Rights Reserved.
 */
import React, { useEffect, useState } from 'react';
import { Collapse, Badge } from 'choerodon-ui';
import {
  DataSet,
  Table,
  Modal,
  Row,
  Col,
  Lov,
  Select,
  Form,
  TextField,
  Icon,
  Button,
} from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import ExcelExport from 'components/ExcelExport';
import { isEmpty, isNil } from 'lodash';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import 'hzero-ued-icon/lib/style/icons.less';
import { entranceDS, lineDS, detailDS } from '../stores/indexDS';
import LovModal from './LovModal';
import InputLovDS from '../stores/InputLovDS';
import { QueryLocatorInfo } from '../services';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inventory.exceptionalOutbound';

const shouldDisabled = selectList => {
  if (isEmpty(selectList)) {
    return true;
  }
  const allY = selectList.every(ele => ele.loadDetailFlag === 'Y');
  const allN = selectList.every(ele => ele.loadDetailFlag === 'N');
  return !(allN || allY);
};

const exceptionalOutboundList = props => {
  const {
    match: { path },
    dataSet,
    lineDs,
    detailDs,
  } = props;
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);
  const [expandForm, setExpandForm] = useState(false);
  const [selectList, setSelectList] = useState([]);
  const { run: handleCreateOutbound } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/exceptional-warehouse-out/create/task`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: queryLocatorInfo } = useRequest(QueryLocatorInfo(), { manual: true, needPromise: true });

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (dataSet) {
      const handerQuery = flag
        ? dataSet.queryDataSet.addEventListener
        : dataSet.queryDataSet.removeEventListener;
      const handler = flag ? dataSet.addEventListener : dataSet.removeEventListener;
      // 查询条件更新时操作
      handerQuery.call(dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
      handler.call(dataSet, 'load', handleDataSetSelectQuery);
      // 头选中和撤销选中事件
      handler.call(dataSet, 'select', handleDataSetSelectUpdate);
      handler.call(dataSet, 'unSelect', handleDataSetSelectUpdate);
      handler.call(dataSet, 'selectAll', handleDataSetSelectUpdate);
      handler.call(dataSet, 'unSelectAll', handleDataSetSelectUpdate);
    }
  };

  // 默认查询行
  const handleDataSetSelectQuery = () => {
    const data = dataSet.toData() || [];
    if (!isEmpty(data)) {
      const { containerId } = data[0];
      lineDs.setQueryParameter('containerId', containerId);
      lineDs.query();
    } else {
      lineDs.loadData([]);
    }
  };

  // 点击头查询行
  const headerRowClick = record => {
    const { containerId } = record.toData();
    lineDs.setQueryParameter('containerId', containerId);
    lineDs.query();
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ record, name }) => {
    if (name === 'locatorLov') {
      record.set('outLocationLov', {});
    }
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    if (dataSet.selected.length) {
      const list = dataSet.selected.map(item => {
        return item.toData();
      });
      setSelectList(list);
    } else {
      setSelectList([]);
    }
  };

  // 创建出库任务
  const goCreateOutbound = async () => {
    const { outLocationId } = dataSet.queryDataSet.current.toData();
    if (outLocationId) {
      const data = dataSet.selected.map(val => {
        return val.toData();
      });
      return handleCreateOutbound({
        params: {
          dto2List: data,
          outLocationId,
        },
      }).then(res => {
        if (res && res.success) {
          dataSet.batchUnSelect(dataSet.selected);
          notification.success({});
          dataSet.clearCachedSelected();
          dataSet.query();
        }
      });
    }
    notification.warning({
      message: intl.get(`${modelPrompt}.notification.import.validate`).d('请选择出库位置'),
    });
    return false;
  };

  const detailTableColumns: ColumnProps[] = [
    { name: 'loadObjectCode', align: ColumnAlign.center, width: 150 },
    { name: 'locatorCode', align: ColumnAlign.center, width: 150 },
    { name: 'loadObjectTypeDesc', align: ColumnAlign.center, width: 150 },
    { name: 'containerCode', align: ColumnAlign.center, width: 120 },
    { name: 'inLocatorTime', align: ColumnAlign.center, width: 150 },
    { name: 'materialCode', align: ColumnAlign.center, width: 150 },
    { name: 'primaryUomQty', align: ColumnAlign.center, width: 150 },
    {
      name: 'siteCode',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'parentLocatorCode',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'qualityStatusDesc',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'enableFlag',
      align: ColumnAlign.center,
      width: 100,
      renderer: ({ record }) => {
        return (
          <Badge
            status={record?.get('enableFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('enableFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        );
      },
    },
    {
      name: 'stocktakeFlag',
      align: ColumnAlign.center,
      width: 100,
      renderer: ({ record }) => {
        return (
          <Badge
            status={record?.get('stocktakeFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('stocktakeFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        );
      },
    },
    {
      name: 'freezeFlag',
      align: ColumnAlign.center,
      width: 100,
      renderer: ({ record }) => {
        return (
          <Badge
            status={record?.get('freezeFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('freezeFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        );
      },
    },
    {
      name: 'lot',
      align: ColumnAlign.center,
      width: 150,
    },
  ];

  const gotDetail = async record => {
    detailDs.setQueryParameter('containerId', record.get('containerId'));
    detailDs.query();
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.title.containerDetail`).d('容器明细'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      keyboardClosable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: (
        <Table
          customizedCode="shdgl3"
          dataSet={detailDs}
          highLightRow={false}
          columns={detailTableColumns}
        />
      ),
    });
  };

  const columns: ColumnProps[] = [
    { name: 'containerCode', align: ColumnAlign.center, width: 150 },
    { name: 'containerTypeCode', align: ColumnAlign.center, width: 150 },
    { name: 'containerTypeDescription', align: ColumnAlign.center, width: 120 },
    { name: 'materialCode', align: ColumnAlign.center, width: 150 },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'specifiedLevel',
      align: ColumnAlign.center,
    },
    {
      name: 'lot',
      align: ColumnAlign.center,
    },
    { name: 'containerNumber', align: ColumnAlign.center, width: 150 },
    { name: 'siteCode', align: ColumnAlign.center, width: 150 },
    { name: 'locatorCode', align: ColumnAlign.center, width: 100 },
    {
      name: 'areaLocatorCode',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'autoCode',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'qualityStatusDescription',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'containerStatusDescription',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'instructionDocNum',
      align: ColumnAlign.center,
      width: 150,
    },
  ];

  const lineTableColumns: ColumnProps[] = [
    { name: 'containerCode', align: ColumnAlign.center, width: 150 },
    { name: 'loadObjectTypeDescription', align: ColumnAlign.center, width: 150 },
    { name: 'containerObjectCode', align: ColumnAlign.center, width: 120 },
    { name: 'materialCode', align: ColumnAlign.center, width: 150 },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'specifiedLevel',
      align: ColumnAlign.center,
    },
    { name: 'containerNumber', align: ColumnAlign.center, width: 150 },
    { name: 'siteCode', align: ColumnAlign.center, width: 150 },
    {
      name: 'locatorCode',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'areaLocatorCode',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'qualityStatusDescription',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'enableFlag',
      align: ColumnAlign.center,
      width: 100,
      renderer: ({ record }) => {
        return (
          <Badge
            status={record?.get('enableFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('enableFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        );
      },
    },
    {
      name: 'stocktakeFlag',
      align: ColumnAlign.center,
      width: 100,
      renderer: ({ record }) => {
        return (
          <Badge
            status={record?.get('stocktakeFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('stocktakeFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        );
      },
    },
    {
      name: 'freezeFlag',
      align: ColumnAlign.center,
      width: 100,
      renderer: ({ record }) => {
        return (
          <Badge
            status={record?.get('freezeFlag') === 'Y' ? 'success' : 'error'}
            text={
              record?.get('freezeFlag') === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        );
      },
    },
    {
      name: 'lot',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'inLocatorTime',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'container',
      align: ColumnAlign.center,
      width: 150,
      renderer: ({ record }) => {
        if (record?.get('loadObjectType') === 'MATERIAL_LOT') {
          return (
            <a style={{ color: 'rgb(191, 191, 191)' }}>
              {intl.get(`${modelPrompt}.detail`).d('明细')}
            </a>
          );
        }
        return (
          <a onClick={() => gotDetail(record)}>{intl.get(`${modelPrompt}.detail`).d('明细')}</a>
        );
      },
    },
  ];
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS?.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS?.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
      handleSearch();
    }
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: dataSet,
    onOpenInputModal,
  };

  const toggleForm = () => {
    setExpandForm(!expandForm);
  };

  const handleChangeLocator = async (value) => {
    if (!value?.locatorId) {
      return;
    }
    const res = await queryLocatorInfo({
      params: {
        areaLocatorId: value?.locatorId,
      },
    });
    if (res?.locatorId) {
      dataSet.queryDataSet.current?.set('goodsAllocationLov', [{
        locatorId: res?.locatorId,
        locatorCode: res?.locatorCode,
        locatorName: res?.locatorName,
      }])
    } else {
      dataSet.queryDataSet.current?.set('goodsAllocationLov', undefined);
    }
  }

  const renderQueryBar = ({ buttons, queryDataSet, dataSet, queryFields }) => {
    if (queryDataSet) {
      return (
        <Row gutter={24}>
          <Col span={18}>
            <Form columns={3} dataSet={queryDataSet} labelWidth={120}>
              <Lov name="locatorLov" onChange={handleChangeLocator} />
              <Lov name="outLocationLov" />
              <Lov name="instructionDocLov" />
              {expandForm && (
                <>
                  <TextField
                    name="autoCodes"
                    suffix={
                      <div className="c7n-pro-select-suffix">
                        <Icon
                          type="search"
                          // @ts-ignore
                          onClick={() =>
                            onOpenInputModal(true, 'autoCodes', '具体存储货位', queryDataSet)
                          }
                        />
                      </div>
                    }
                  />
                  <Lov name="goodsAllocationLov" />
                  <Select name="loadDetailFlag" />
                  <Lov name="containerLov" />
                  <Lov name="materialLov" />
                  <Lov name="specifiedLevelLov" />
                  <Lov name="containerTypeLov" />
                </>
              )}
            </Form>
          </Col>
          <Col span={6}>
            <div
              style={{
                flexShrink: 0,
                display: 'flex',
                alignItems: 'center',
                marginTop: '7px',
              }}
            >
              <Button
                funcType={FuncType.link}
                icon={expandForm ? 'expand_less' : 'expand_more'}
                onClick={toggleForm}
              >
                {expandForm
                  ? intl.get('hzero.common.button.collected').d('收起')
                  : intl.get(`hzero.common.button.viewMore`).d('更多')}
              </Button>
              <Button
                onClick={() => {
                  queryDataSet.current.reset();
                  dataSet.fireEvent('queryBarReset', {
                    dataSet,
                    queryFields,
                  });
                }}
              >
                {intl.get('hzero.common.button.reset').d('重置')}
              </Button>
              {/* @ts-ignore */}
              <Button dataSet={null} onClick={handleSearch} color={ButtonColor.primary}>
                {intl.get('hzero.common.button.search').d('查询')}
              </Button>
              {buttons}
            </div>
          </Col>
        </Row>
      );
    }
    return null;
  };
  const handleSearch = async () => {
    dataSet.query();
  };

  // 导出组件所需的功能模块查询参数
  const getExportQueryParams = () => {
    if (!dataSet.queryDataSet || !dataSet.queryDataSet.current) {
      return {};
    }
    const queryParmas = dataSet.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return {
      ...queryParmas,
      containerIdList: dataSet.selected?.length ? dataSet.selected?.map(_record => _record?.get('containerId')) : queryParmas?.containerIdList,
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.exceptionalOutbound`).d('例外出库')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          onClick={goCreateOutbound}
          disabled={shouldDisabled(selectList)}
          permissionList={[
            {
              code: `${path}.button.create`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.stock.transfer`).d('创建出库任务')}
        </PermissionButton>
        <ExcelExport
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/exceptional-warehouse-out/export`}
          queryParams={getExportQueryParams}
        >
          {intl.get(`${modelPrompt}.button.export`).d('导出')}
        </ExcelExport>
      </Header>
      <Content>
        <Table
          // queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
          }}
          // @ts-ignore
          queryBar={renderQueryBar}
          dataSet={dataSet}
          columns={columns}
          highLightRow
          searchCode="exceptionalOutbound"
          customizedCode="exceptionalOutbound"
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
        />
        <Collapse bordered={false} collapsible="icon" defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineDs}
          >
            <Table
              customizedCode="shdgl2"
              dataSet={lineDs}
              highLightRow={false}
              columns={lineTableColumns}
            />
          </Panel>
        </Collapse>
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.inventory.exceptionalOutbound', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...entranceDS() });
      const lineDs = new DataSet(lineDS());
      const detailDs = new DataSet(detailDS());
      return {
        dataSet,
        lineDs,
        detailDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(exceptionalOutboundList),
);
