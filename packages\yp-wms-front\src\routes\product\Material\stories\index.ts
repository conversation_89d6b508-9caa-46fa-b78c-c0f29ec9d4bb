/**
 * @Description: 物料维护-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-07-25 15:24:51
 * @LastEditTime: 2023-05-18 11:37:45
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.product.materialManager.model.materialManager';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'messageId',
  queryFields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'materialIdentifyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentifyCode`).d('物料简码'),
    },
    {
      name: 'materialDesignCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesignCode`).d('物料图号'),
    },
  ],
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialIdentifyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentifyCode`).d('物料简码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('材质/型号'),
    },
    {
      name: 'materialDesignCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesignCode`).d('物料图号'),
    },
    {
      name: 'primaryUomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomName`).d('基本计量单位'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('基本计量单位编码'),
    },
    {
      name: 'secondaryUomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondaryUomName`).d('辅助单位'),
    },
    {
      name: 'secondaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondaryUomCode`).d('辅助单位编码'),
    },
    {
      name: 'conversionRate',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.conversionRate`).d('主辅单位换算'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/list/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_LIST.LIST`,
        method: 'GET',
      };
    },
  },
});

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'materialId', // 主键
      type: FieldType.number,
    },
    // 基础属性 start
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      required: true,
    },
    {
      name: 'materialName',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      required: true,
    },
    {
      name: 'longDescription',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.longDescription`).d('长描述'),
    },
    {
      name: 'materialDesignCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesignCode`).d('物料图号'),
    },
    {
      name: 'materialIdentifyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialIdentifyCode`).d('物料简码'),
    },
    {
      name: 'oldItemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.oldItemCode`).d('旧物料号'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('材质/型号'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'primaryUomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.primaryUomName`).d('基本计量单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'primaryUomId',
      bind: 'primaryUomLov.uomId',
    },
    {
      name: 'primaryUomCode',
      bind: 'primaryUomLov.uomCode',
    },
    {
      name: 'secondaryUomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.secondaryUomName`).d('辅助单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'secondaryUomId',
      bind: 'secondaryUomLov.uomId',
    },
    {
      name: 'secondaryUomCode',
      bind: 'secondaryUomLov.uomCode',
    },
    {
      name: 'conversionRate',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.conversionRate`).d('主辅单位换算'),
      min: 0,
      precision: 2,
      step: 1,
    },
    {
      name: 'responEmployeeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responEmployee`).d('负责人'),
      lovCode: 'YP.QIS.EMPLOYEE',
      multiple: true,
      textField: 'name',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'responEmployeeIdList',
      bind: 'responEmployeeLov.employeeId',
    },
    {
      name: 'responEmployeeNumList',
      bind: 'responEmployeeLov.employeeNum',
    },
    {
      name: 'responEmployeeNameList',
      bind: 'responEmployeeLov.name',
    },
    // 基础属性 start
    // 规格尺寸 start
    {
      name: 'sizeUomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sizeUomName`).d('尺寸单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: {
        tenantId,
        uomType: 'LENGTH',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'sizeUomId',
      bind: 'sizeUomLov.uomId',
    },
    {
      name: 'sizeUomCode',
      bind: 'sizeUomLov.uomCode',
    },
    {
      name: 'length',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.length`).d('长'),
      min: 0,
      precision: 2,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('sizeUomId');
        },
      },
    },
    {
      name: 'width',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.width`).d('宽'),
      min: 0,
      precision: 2,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('sizeUomId');
        },
      },
    },
    {
      name: 'height',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.height`).d('高'),
      min: 0,
      precision: 2,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('sizeUomId');
        },
      },
    },
    {
      name: 'volumeUomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.volumeUomName`).d('体积单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: {
        tenantId,
        uomType: 'VOLUME',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'volumeUomId',
      bind: 'volumeUomLov.uomId',
    },
    {
      name: 'volumeUomCode',
      bind: 'volumeUomLov.uomCode',
    },
    {
      name: 'volume',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.volume`).d('体积'),
      min: 0,
      precision: 2,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('volumeUomId');
        },
      },
    },
    {
      name: 'weightUomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.weightUomName`).d('重量单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: {
        tenantId,
        uomType: 'WEIGHT',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'weightUomId',
      bind: 'weightUomLov.uomId',
    },
    {
      name: 'weightUomCode',
      bind: 'weightUomLov.uomCode',
    },
    {
      name: 'weight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.weight`).d('重量'),
      min: 0,
      precision: 2,
      step: 1,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('weightUomId');
        },
      },
    },
    // 规格尺寸 end
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/property/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DETAIL.BASIC`,
        method: 'GET',
        transformResponse: val => {
          const res = JSON.parse(val);
          return {
            ...res,
            rows: {
              ...res.rows,
              primaryUomId: res.rows.primaryUomId || null,
              secondaryUomId: res.rows.secondaryUomId || null,
              sizeUomId: res.rows.sizeUomId || null,
              volumeUomId: res.rows.volumeUomId || null,
              weightUomId: res.rows.weightUomId || null,
            },
          }
        },
      };
    },
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.method.domain.entity.MtMaterial';
      return {
        data: { materialId: record.get('materialId') || '' },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_METHOD}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
  },
});

const copyDS: () => DataSetProps = () => ({
  autoQuery: false,
  fields: [
    {
      name: 'sourceMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.copyFrom`).d('从物料'),
      required: true,
    },
    {
      name: 'sourceMaterialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.originmaterialName`).d('来源物料描述'),
      required: true,
    },
    {
      name: 'targetMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.copyTo`).d('到物料'),
      required: true,
    },
  ],
});



export { tableDS, detailDS, copyDS };
