/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-06-28 15:10:32
 * @LastEditTime: 2023-06-30 17:51:00
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { getCurrentSiteInfo } from '@utils/utils';
import moment from 'moment';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.wms.purchase.materialApproval';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'processDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processDocNum`).d('流程单号'),
      disabled: true,
    },
    {
      name: 'applyPersonName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyPersonName`).d('申请人'),
      disabled: true,
      defaultValue: getCurrentUser().realName,
    },
    {
      name: 'unitLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.unitCode`).d('申请部门'),
      lovCode: 'WMS_APPLY_UNIT',
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'applyUnit',
      bind: 'unitLov.unitId',
    },
    {
      name: 'applyUnitName',
      bind: 'unitLov.unitName',
    },
    {
      name: 'applyTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.applyTime`).d('申请日期'),
      required: true,
      defaultValue: moment(new Date()),
    },
    {
      name: 'createdByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.budgetPerson`).d('预算员'),
      lovCode: 'MT.USER.ORG',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'budgetPerson',
      bind: 'createdByLov.id',
    },
    {
      name: 'budgetPersonName',
      bind: 'createdByLov.realName',
    },
    // {
    //   name: 'budgetPerson',
    //   type: FieldType.dateTime,
    //   label: intl.get(`${modelPrompt}.budgetPerson`).d('预算员'),
    // },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      noCache: true,
      required: true,
      dynamicProps: {
        defaultValue: () => {
          const siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo };
          }
          return undefined;
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'wareHouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
      lovCode: 'WMS.SYN_LOCATOR_LOV',
      required: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        locatorCategory: ['AREA'],
        enableFlag: 'Y',
      },
    },
    {
      name: 'warehouseCode',
      bind: 'wareHouseLov.locatorCode',
    },
    {
      name: 'costcenter',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.costcenter`).d('成本中心'),
      lovCode: 'MT.WMS.COST_CENTER',
      // required: true,
      textField: 'costcenterCode',
      ignore: FieldIgnore.always,
      lovPara: { tenantId, accountType: 'COST_CENTER', enableFlag: 'Y' },
      dynamicProps: {
        required: ({ record }) => record.get('demandType') === '生产',
        disabled: ({ record }) => record.get('demandType') !== '生产',
      },
    },
    {
      name: 'costCenterId',
      bind: 'costcenter.costcenterId',
    },
    {
      name: 'costCenterCode',
      bind: 'costcenter.costcenterCode',
    },
    {
      name: 'centralizedUnit',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.centralizedUnit`).d('归口部门'),
      required: true,
      lookupCode: 'WMS.CENTRALIZED_UNIT',
    },
    {
      name: 'demandTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.demandTypeObj`).d('需求类型'),
      required: true,
      ignore: FieldIgnore.always,
      lookupCode: 'WMS.DEMAND_TYPE',
    },
    {
      name: 'demandType',
      bind: 'demandTypeObj.value',
    },
    {
      name: 'demandTypeDescription',
      bind: 'demandTypeObj.description',
    },
    {
      name: 'applyReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyReason`).d('申请原因'),
    },
    {
      name: 'totalPrice',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.totalPrice`).d('总价'),
    },
    {
      name: 'attribute1',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.attribute1`).d('附件'),
      dynamicProps: {
        required: ({ record }) => record.get('demandType') === '销售',
      },
    },
  ],

  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-synthesise-demands/query/edit/ui`,
        method: 'GET',
      };
    },
  },
});

const lineTableDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  forceValidate: true,
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      siteId: 'siteId',
    },
    {
      name: 'materialObjLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'WMS.CONTRACT_MATERIAL',
      required: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      bind: 'materialObjLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialObjLov.materialCode',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'materialObjLov.revisionFlag',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialObjLov.materialName',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierLov`).d('供应商编码'),
      lovCode: 'WMS.CONTRACT_SUPPLIER',
      ignore: FieldIgnore.always,
      textField: 'supplierCode',
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            materialId: record.get('materialId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('materialId');
        },
      },
    },
    {
      name: 'supplierName',
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
      bind: 'supplierLov.supplierName',
    },
    {
      name: 'supplierCode',
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'contractNumberObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.contractNumber`).d('合同'),
      lovCode: 'WMS_CONTRACT',
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            supplierCode: record.get('supplierCode'),
            materialCode: record.get('materialCode'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('supplierCode') || !record.get('materialCode');
        },
      },
    },
    {
      name: 'contractNumber',
      bind: 'contractNumberObj.contractNumber',
    },
    {
      name: 'attributeVarchar15',
      label: intl.get(`${modelPrompt}.attributeVarchar15`).d('交货周期'),
      bind: 'contractNumberObj.attributeVarchar15',
    },
    {
      name: 'brand',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.brand`).d('品牌'),
    },
    {
      name: 'canRequestQty',
      bind: 'contractNumberObj.canRequestQty',
      label: intl.get(`${modelPrompt}.canRequestQty`).d('可提需数量'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('型号'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
      required: true,
      min: 0,
    },
    {
      name: 'minPoQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.minPoQty`).d('最小采购量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'demandTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.demandTime`).d('需求时间'),
      required: true,
    },
    {
      name: 'unitPrice',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.unitPrice`).d('未税单价'),
    },
    {
      name: 'price',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.price`).d('未税总价'),
    },
    {
      name: 'applyNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyNum`).d('财审单号'),
      dynamicProps: {
        required: ({ record }) => {
          if (record.get('flag') === 'Y') {
            return true;
          }
        },
      },
    },
    {
      name: 'applyLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyLineNum`).d('财审单行号'),
      dynamicProps: {
        required: ({ record }) => {
          if (record.get('flag') === 'Y') {
            return true;
          }
        },
      },
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  events: {
    update: ({ record, name, value }) => {
      if (name === 'contractNumberObj' && value) {
        if (value) {
          record.set('brand', value.brand);
          record.set('model', value.model);
          record.set('uomCode', value.uom);
          record.set('unitPrice', value.unitPrice);
        } else {
          record.set('brand', null);
          record.set('model', null);
          record.set('uomCode', null);
          record.set('unitPrice', null);
        }
      }
      if (name === 'materialObjLov') {
        record.set('supplierLov', {});
      }
    },
  },
});

export { detailDS, lineTableDS };
