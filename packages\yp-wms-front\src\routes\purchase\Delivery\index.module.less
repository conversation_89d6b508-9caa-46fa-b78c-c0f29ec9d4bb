/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2022-03-29 16:14:32
 * @LastEditTime: 2022-11-10 17:31:27
 * @LastEditors: <<EMAIL>>
 */
:global {
  .expand-table-control-btn {
    position: absolute;
    top: 14px;
    right: 5px;
    z-index: 10;
    margin: 0;
  }
}
.deliver-content {
  :global {
    .c7n-collapse.c7n-collapse {
      margin-top: 10px;
    }
  }
}
.expand-table {
  :global {
    .c7n-pro-table-filter-select-suffix {
      display: none;
    }
    .c7n-pro-table-filter-select-clear-button {
      right: 26px;
    }
  }
}
.card-table {
  :global {
    .ant-card-body {
      padding: 0 !important;
    }
    .c7n-pro-table-filter-select .c7n-pro-table-filter-select-inner-editor {
      border: none !important;
      height: 0.22rem;
      line-height: 0.2rem;
      margin-bottom: 0.02rem;
    }
    .c7n-pro-table-filter-select-empty .c7n-pro-table-filter-select-placeholder {
      text-indent: 0.1rem;
    }
  }
}

.list-label {
  text-align: right;
  padding-right: 18px;
  line-height: 28px;
  &:last-of-type {
    line-height: 30px;
  }
}
.list-max {
  padding-left: 18px;
  line-height: 28px;
  &:last-of-type {
    line-height: 30px;
  }
}
.pro-list-row {
  margin-bottom: 14px;
  position: relative;
  padding: 4px 0;
  height: 28px;
  width: 100%;
  background-color: #efefef;
  display: flex;
  color: #fff;
  text-align: center;
  flex-wrap: nowrap;
  .pro-list-col-ab {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
  .pro-list-col-ab-short:hover {
    z-index: 3 !important;
  }
  & > div {
    height: 20px;
    flex-grow: 0;
    flex-shrink: 1;
    display: flex;
    & > span {
      flex-grow: 1;
      vertical-align: middle;
    }
  }
  .pro-list-row-sub {
    display: flex;
    & > span {
      flex-grow: 1;
      display: flex;
      & > div {
        position: relative;
        flex-grow: 1;
        &:nth-last-of-type(n + 2)::after {
          content: ' ';
          position: absolute;
          right: 0;
          top: 2px;
          bottom: 2px;
          border-right: 1px solid #1fe697;
        }
      }
    }
  }
}
.pro-list-row-2 {
  height: 30px;
}

.split-menu {
  :global {
    .c7n-menu-item > a,
    .c7n-menu-item > a:hover {
      color: #000;
    }
    .c7n-menu-item.c7n-menu-item-selected {
      background-color: inherit;
    }
  }
}

.modal-title-container {
  flex-grow: 1;
  display: flex;
  justify-content: space-between;
  width: 100%;
  & > div {
    flex-grow: 1;
    &:last-of-type {
      text-align: right;
    }
  }
}
.modal-footer-container {
  width: 100%;
  text-align: right;
}
.modal-title-btn-box {
  margin-right: 30px;
  > button {
    margin-left: 8px;
  }
}
.split-qty-box {
  position: relative;
  width: 100%;
  & > span {
    width: 100%;
  }
}
.split-qty-icon {
  position: absolute;
  right: -18px;
  top: 6px;
}

.icon-new {
  position: absolute;
  right: 0;
  top: 0;
  height: 24px;
  width: 24px;
  overflow: hidden;
  .icon-new-inner {
    position: absolute;
    bottom: 11px;
    left: 0;
    width: 34px;
    height: 12px;
    line-height: 14px;
    font-size: 10px;
    text-align: center;
    background-color: #29bece;
    color: #fff;
    font-weight: bolder;
    transform: rotate(45deg);
    .icon-new-inner-text {
      display: inline-block;
      transform: scale(0.65, 0.65);
    }
  }
}
.text-after {
  position: absolute;
  left: 100%;
  top: 0;
  display: flex;
  width: 32px;
  height: 28px;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.progress-item {
  :global {
    .c7n-progress-text {
      width: 3em;
    }
    .c7n-progress-outer {
      margin-right: calc(-3em - 0.08rem);
      padding-right: calc(3em + 0.08rem);
    }
  }
}

.line-left {
  :global {
    .c7n-pro-input {
      border-radius: 4px 0 0 4px;
    }
  }
}
.line-right {
  :global {
    .c7n-pro-input {
      border-radius: 0 4px 4px 0;
      border-left: none;
    }
  }
}

.icon-new {
  position: absolute;
  right: 0;
  top: 0;
  height: 24px;
  width: 24px;
  overflow: hidden;
  .icon-new-inner {
    position: absolute;
    bottom: 11px;
    left: 0;
    width: 34px;
    height: 12px;
    line-height: 14px;
    font-size: 10px;
    text-align: center;
    background-color: #29bece;
    color: #fff;
    font-weight: bolder;
    transform: rotate(45deg);
    .icon-new-inner-text {
      display: inline-block;
      transform: scale(0.65, 0.65);
    }
  }
}
.cus-bread-crumb {
  .cusCrumb {
    font-size: 12px!important;
    color: rgb(8, 64, 248)!important;
  }
  .cusCrumbNormal {
    font-size: 12px!important;
    color: rgb(89, 89, 89)!important;
  }
}
.table-container {
  margin-top: 10px;
  height: 700px;
  overflow-y: scroll;

  .cusPcolor {
    color: rgb(8, 64, 248)!important;
  }

  p{
    margin-top: 10px;
  }
  .empty-container {
    display: block;
    height: 600px;
  }
}


