/**
 * @Description: 库存预留日记账查询 - 入口页
 * @Author: <EMAIL>
 * @Date: 2022/7/5 13:57
 * @LastEditTime: 2022-11-21 13:40:17
 * @LastEditors: <<EMAIL>>
 */
import React from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { useDataSetEvent } from 'utils/hooks';
import { Content, Header } from 'components/Page';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { entranceDS } from '../stores/EntranceDS';

const ReserveQuery = props => {
  const { entranceDs } = props;

  // 查询条件更新时操作
  useDataSetEvent(entranceDs.queryDataSet, 'update', ({ name, record }) => {
    if (name === 'ownerType') {
      record.set('ownerLov', {});
    }
    if (name === 'siteLov') {
      record.set('materialLov', {});
      record.set('orgLov', {});
    }
  });

  const columns: ColumnProps[] = [
    { name: 'materialCode', lock: ColumnLock.left, width: 150 },
    { name: 'materialDesc', lock: ColumnLock.left, width: 160 },
    { name: 'revisionCode', lock: ColumnLock.left, width: 120 },
    { name: 'eventTime', align: ColumnAlign.center, width: 150 },
    { name: 'changeQuantity', width: 130 },
    { name: 'holdQuantity', width: 130 },
    { name: 'uomCode' },
    { name: 'locatorCode', width: 200 },
    { name: 'locatorDesc', width: 160 },
    { name: 'lotCode' },
    { name: 'qualityStatusDesc', width: 120 },
    {
      name: 'ownerTypeDesc',
      width: 110,
      renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
    },
    { name: 'ownerCode', width: 100 },
    { name: 'ownerDesc', width: 160 },
    { name: 'holdTypeDesc', width: 100 },
    { name: 'orderTypeDesc', width: 120 },
    { name: 'orderCode', width: 150 },
    { name: 'eventType', width: 150 },
    { name: 'eventTypeDesc', width: 120 },
    { name: 'eventRequestTypeCode', width: 120 },
    { name: 'eventRequestTypeDesc', width: 150 },
    { name: 'eventId', width: 100 },
    { name: 'eventByUserName' },
  ];

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get('tarzan.inventory.reserveQuery.view.title.reserveQueryst')
          .d('库存预留日记账')}
      />
      <Content>
        <Table
          searchCode="kcylrjzcx1"
          customizedCode="kcylrjzcx1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
          }}
          dataSet={entranceDs}
          columns={columns}
          highLightRow
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.inventory.reserveQuery', 'tarzan.common'],
})(
  withProps(
    () => {
      const entranceDs = new DataSet({ ...entranceDS() });
      return {
        entranceDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(ReserveQuery),
);
