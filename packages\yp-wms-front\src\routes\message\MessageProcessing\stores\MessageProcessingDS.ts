/**
 * @Description: 消息处理查询DS
 * @Author: <<EMAIL>>
 * @Date: 2022-01-29 20:32:06
 * @LastEditTime: 2023-03-06 14:46:22
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.message.message.messageProcessing';

const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  primaryKey: 'globalMessageId',
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'topic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topic`).d('主题'),
    },
    {
      name: 'dealStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dealResult`).d('执行结果'),
      lookupCode: 'MT.MESSAGE.DEAL_STATUS',
    },
    {
      name: 'dateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateFrom`).d('消息处理时间从'),
      max: 'dateTo',
    },
    {
      name: 'dateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('消息处理时间至'),
      min: 'dateFrom',
    },
  ],
  record: {
    dynamicProps: {
      selectable: record => record.get('dealStatus') !== 'CONSUME_SUCCESS',
    },
  },
  fields: [
    {
      name: 'topic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topic`).d('主题'),
    },
    {
      name: 'content',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.content`).d('内容'),
    },
    {
      name: 'dealStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dealResult`).d('执行结果'),
      lookupCode: 'MT.MESSAGE.DEAL_STATUS',
    },
    {
      name: 'dealMessage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dealMessage`).d('报错内容'),
    },
    {
      name: 'dealTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dealTime`).d('消息处理时间'),
    },
  ],
  transport: {
    read: ({ dataSet, data }) => {
      const { services, ...others } = data;
      const _url =
        dataSet?.getState('messageType') === 'send'
          ? `/${services}/v1/${tenantId}/mt-message-send-records/list/ui`
          : `/${services}/v1/${tenantId}/mt-message-receive-records/list/ui`;
      return {
        url: _url,
        method: 'GET',
        data: others,
      };
    },
  },
});

const servicesDS: () => DataSetProps = () => ({
  autoCreate: true,
  fields: [
    {
      name: 'messageType',
      type: FieldType.string,
      required: true,
      defaultValue: 'send',
    },
    {
      name: 'sendServicesCode',
      type: FieldType.string,
      lookupCode: 'MT.MESSAGE_SERVICE',
      required: true,
    },
    {
      name: 'receiveServicesCode',
      type: FieldType.string,
      lookupCode: 'MT.MESSAGE_SERVICE',
      multiple: true,
    },
  ],
});

export { tableDS, servicesDS };
