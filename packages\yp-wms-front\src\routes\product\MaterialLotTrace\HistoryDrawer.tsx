/**
 * @Description: 物料批管理平台-列表页-历史抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-01-26 00:37:15
 * @LastEditTime: 2022-07-04 17:46:32
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { Table } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { Badge } from 'choerodon-ui';
import intl from 'utils/intl';

export default ({ ds }) => {
  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'eventId',
        align: ColumnAlign.left,
        width: 150,
      },
      {
        name: 'eventTypeCode',
        width: 150,
      },
      {
        name: 'eventTypeDesc',
        width: 150,
      },
      {
        name: 'eventRequestId',
        width: 150,
      },
      {
        name: 'requestTypeCode',
        width: 150,
      },
      {
        name: 'requestTypeDesc',
        width: 150,
      },
      {
        name: 'eventUserName',
        width: 150,
      },
      {
        name: 'eventTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'identification',
        align: ColumnAlign.left,
        width: 250,
      },
      {
        name: 'materialLotCode',
        width: 250,
      },
      {
        name: 'materialCode',
        width: 250,
      },
      {
        name: 'revisionCode',
        width: 100,
      },
      {
        name: 'materialDesc',
        width: 250,
      },
      {
        name: 'primaryUomQty',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'primaryUomCode',
        width: 120,
      },
      {
        name: 'locatorCode',
        width: 250,
      },
      {
        name: 'lot',
        width: 100,
      },
      {
        name: 'productionDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'expirationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'extendedShelfLifeTimes',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'supplierLot',
        width: 100,
      },
      {
        name: 'secondaryUomQty',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'secondaryUomCode',
        width: 120,
      },
      {
        name: 'enableFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'qualityStatusDesc',
        width: 120,
      },
      {
        name: 'createReasonDesc',
        width: 150,
      },
      {
        name: 'inLocatorTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'inSiteTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'siteCode',
        width: 250,
      },
      {
        name: 'eoNum',
        width: 150,
      },
      {
        name: 'ownerTypeDesc',
        width: 150,
        renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
      },
      {
        name: 'ownerCode',
        width: 250,
      },
      {
        name: 'ownerDesc',
        width: 250,
      },
      {
        name: 'supplierCode',
        width: 250,
      },
      {
        name: 'supplierDesc',
        width: 250,
      },
      {
        name: 'supplierSiteCode',
        width: 250,
      },
      {
        name: 'supplierSiteDesc',
        width: 250,
      },
      {
        name: 'customerCode',
        width: 250,
      },
      {
        name: 'customerDesc',
        width: 250,
      },
      {
        name: 'customerSiteCode',
        width: 250,
      },
      {
        name: 'customerSiteDesc',
        width: 250,
      },
      {
        name: 'reservedFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'reservedObjectTypeDesc',
        width: 120,
      },
      {
        name: 'reservedObjectCode',
        width: 250,
      },
      {
        name: 'assembleToolCode',
        width: 250,
      },
      {
        name: 'assemblePointCode',
        width: 250,
      },
      {
        name: 'unloadTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'loadTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'ovenNumber',
        width: 250,
      },
      {
        name: 'overOrderInterceptionFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'freezeFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'stocktakeFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'materialLotStatus',
        width: 150,
        renderer: ({ record }) => record!.getField('materialLotStatus')!.getText(),
      },
      {
        name: 'instructionDocNum',
        width: 150,
      },
      {
        name: 'instructionNum',
        width: 250,
      },
      {
        name: 'currentContainerCode',
        width: 250,
      },
      {
        name: 'topContainerCode',
        width: 250,
      },
      {
        name: 'printTimes',
        width: 250,
        renderer: ({ value }) => value || 0,
      },
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'createdUsername',
        width: 250,
      },
      {
        name: 'lastUpdateDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'lastUpdatedUsername',
        width: 250,
      },
      {
        name: 'trxPrimaryQty',
        width: 150,
      },
      {
        name: 'trxSecondaryQty',
        width: 150,
      },
      {
        name: 'reason',
        width: 150,
      },
      {
        name: 'remark',
        width: 150,
      },
    ],
    [],
  );

  return (
    <>
      <Table customizedCode="wlpglpt3" dataSet={ds} columns={columns} />
    </>
  );
};
