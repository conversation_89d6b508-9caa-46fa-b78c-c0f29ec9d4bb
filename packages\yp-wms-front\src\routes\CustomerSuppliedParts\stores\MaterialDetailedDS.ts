import intl from 'utils/intl';
import uuid from 'uuid/v4';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.customerSuppliedParts';
const tenantId = getCurrentOrganizationId();

const tableDetailedDS = (): DataSetProps => {
  return {
    autoQuery: false,
    autoCreate: false,
    pageSize: 10,
    selection: false,
    autoLocateFirst: false,
    forceValidate: true,
    transport: {
      read: () => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms_customer_supplied_parts/executed/material-lot/list/ui?`,
          method: 'GET',
          // 后台数据没有主键，故前端自定义uuid
          transformResponse: val => {
            const data = JSON.parse(val);
            data.rows.content.forEach((item) => {
              item.uuid = uuid()
            });
            return { ...data};
          },
        };
      },
    },
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    primaryKey: 'uuid', // 后台数据没有主键，故前端自定义uuid
    fields: [
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'qualityStatusDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.quantityStatus`).d('质量状态'),
      },
      {
        name: 'materialIdentification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.materialIdentification`).d('物料批标识'),
      },
      {
        name: 'materialLotStatusDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.materialLotStatusDesc`).d('条码状态'),
      },
      {
        name: 'identification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.container`).d('所在容器'),
      },
      {
        name: 'sumActualQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.table.sumActualQty`).d('数量'),
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.uomCode`).d('单位'),
      },
      {
        name: 'lot',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.batch`).d('批次'),
      },
      {
        name: 'receiveLocatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.receiveLocatorCode`).d('接收仓库'),
      },
      {
        name: 'receiveData',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.table.receiveData`).d('接收时间'),
      },
      {
        name: 'receiveBy',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.receiveBy`).d('接收人'),
      },
      {
        name: 'putOnLocatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.putOnLocatorCode`).d('上架库位'),
      },
      {
        name: 'putOnData',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.table.putOnData`).d('上架时间'),
      },
      {
        name: 'putOnBy',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.putOnBy`).d('上架人'),
      },
      {
        name: 'productionDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.productionDate`).d('生产日期'),
      },
      {
        name: 'expirationDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.table.expirationDate`).d('到期时间'),
      },
    ],
  };
};

export { tableDetailedDS };
