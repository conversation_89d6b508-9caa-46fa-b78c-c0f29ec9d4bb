// 报废单查询
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.workshop.varianceAdjustment';
const tenantId = getCurrentOrganizationId();

// 头表格ds
const headTableDS = () => {
  return {
    dataKey: 'content',
    totalKey: 'totalElements',
    paging: true,
    autoQuery: false,
    queryFields: [
      {
        name: 'siteCodeObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
        lovCode: 'MT.MODEL.SITE',
        ignore: 'always',
        lovPara: {
          tenantId,
        },
        required: true,
      },
      {
        name: 'siteIdList',
        bind: 'siteCodeObj.siteId',
      },
      {
        name: 'flowNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.flowNum`).d('单据号'),
      },
      {
        name: 'statusList',
        type: 'string',
        label: intl.get(`${modelPrompt}.status`).d('单据状态'),
        lovPara: {
          tenantId,
        },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INVENTORY_DOC_STATUS`,
        textField: 'description',
        valueField: 'statusCode',
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'costcenterCodeObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.costcenterCode`).d('成本中心'),
        lovCode: 'MT.WMS.COST_CENTER',
        ignore: 'always',
        lovPara: { tenantId, accountType: 'COST_CENTER', enableFlag: 'Y' },
        required: false,
      },
      {
        name: 'costcenterIdList',
        bind: 'costcenterCodeObj.costcenterId',
      },

      {
        name: 'requestByList',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestBy`).d('申请人'),
      },
      {
        name: 'materialCodeObj',
        type: 'object',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        lovCode: 'MT.METHOD.MATERIAL',
        lovPara: {
          tenantId,
        },
        ignore: 'always',
        required: false,
      },
      {
        name: 'materialIdList',
        bind: 'materialCodeObj.materialId',
      },

      {
        name: 'materialLotIdList',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotIdList`).d('物料批编码'),
      },
    ],
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      },
      {
        name: 'flowNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.flowNum`).d('单据号'),
      },
      {
        name: 'statusDescription',
        type: 'string',
        label: intl.get(`${modelPrompt}.status`).d('任务状态'),
      },
      {
        name: 'costcenterCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.costcenterCode`).d('成本中心编码'),
      },
      {
        name: 'requestByRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.requestByRealName`).d('申请人'),
      },
      {
        name: 'remark',
        type: 'string',
        label: intl.get(`${modelPrompt}.remark`).d('盘点调整说明'),
      },
      {
        name: 'realname',
        type: 'string',
        label: intl.get(`${modelPrompt}.realname`).d('创建人'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'lastUpdatedRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdatedRealName`).d('最后更新人'),
      },
      {
        name: 'lastUpdateDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-inventory-variance-docs`,
          method: 'GET',
          data,
        };
      },
    },
  };
};

// 行表格ds
const lineTableDS = () => {
  return {
    dataKey: 'content',
    totalKey: 'totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'lineNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineNum`).d('行号'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'lineStatus',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineStatus`).d('调整状态'),
      },
      {
        name: 'qty',
        type: 'string',
        label: intl.get(`${modelPrompt}.qty`).d('账面数量'),
      },
      {
        name: 'actualQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.actualQty`).d('实际数量'),
      },
      {
        name: 'adjustQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.adjustQty`).d('盘盈盘亏数量'),
      },
      {
        name: 'adjustReason',
        type: 'string',
        label: intl.get(`${modelPrompt}.adjustReason`).d('调整原因'),
      },
      {
        name: 'createdRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.createdRealName`).d('创建人'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'lastUpdatedRealName',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdatedRealName`).d('最后更新人'),
      },
      {
        name: 'lastUpdateDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-inventory-variance-docs/detail`,
          method: 'GET',
          data,
        };
      },
    },
  };
};


export { headTableDS, lineTableDS };
