import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Table, Dropdown, Menu, Attachment } from 'choerodon-ui/pro';
import { Collapse, Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { getCurrentOrganizationId } from 'utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import request from 'utils/request';
import notification from 'utils/notification';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { LabelLayout, ShowValidation } from 'choerodon-ui/pro/es/form/enum';
import { BASIC } from '@utils/config';
import { TabelDS, LineDs } from '../stores/TabelDS';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.wms.purchase.materialApproval';
const tenantId = getCurrentOrganizationId();

const MaterialApprovalList = observer(props => {
  const {
    tableDs,
    lineDs,
    match: { path },
  } = props;

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (tableDs) {
      const handler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // 列表加载事件
      handler.call(tableDs, 'load', resetHeaderDetail);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    if (props?.location?.state) {
      // eslint-disable-next-line no-param-reassign
      props.location.state = {};
    }
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      const { processDocId } = dataSet?.current?.toData();
      handleLineQuery(processDocId);
    } else {
      lineDs.loadData([]);
    }
  };

  const handleLineQuery = processDocId => {
    lineDs.setQueryParameter('processDocId', processDocId);
    lineDs.query();
  };

  const handleRowClick = record => {
    const { processDocId } = record.toData();
    handleLineQuery(processDocId);
  };

  const goLotDetail = id => {
    props.history.push(`/hwms/purchase/material-approval/detail/${id}`);
  };

  const enclosureProps: any = {
    name: 'attribute1',
    bucketName: 'wms-package',
    bucketDirectory: 'material-approval',
    accept: ['.pdf', 'image/*'],
    labelLayout: LabelLayout.float,
    showValidation: ShowValidation.newLine,
    viewMode: 'popup',
  };

  const columns: ColumnProps[] = [
    {
      name: 'creationDate',
      width: 150,
    },
    {
      name: 'processDocNum',
      width: 120,
      renderer: ({ record, value }) => {
        if (record?.get('status') === 'DISAPPROVED' || record?.get('status') === 'NEW') {
          return (
            <span className="action-link">
              <a onClick={() => goLotDetail(record?.get('processDocId'))}>{value}</a>
            </span>
          );
        }
        return value;
      },
    },
    {
      name: 'title',
    },
    {
      name: 'price',
    },
    {
      name: 'status',
    },
    {
      name: 'applyPersonName',
    },
    {
      name: 'applyUnitName',
    },
    {
      name: 'applyTime',
    },
    {
      name: 'budgetPersonName',
    },
    {
      name: 'siteCode',
    },
    {
      name: 'warehouseCode',
    },
    {
      name: 'costCenterCode',
    },
    {
      name: 'costCenterName',
    },
    {
      name: 'centralizedUnit',
    },
    {
      name: 'demandType',
    },
    {
      name: 'applyReason',
    },
    {
      name: 'attribute1',
      width: 150,
      align: ColumnAlign.center,
      editor: () => <Attachment {...enclosureProps} readOnly />,
    },
  ];

  const lineColumns: ColumnProps[] = [
    {
      name: 'lineNumber',
      width: 80,
    },
    {
      name: 'materialCode',
      width: 80,
    },
    {
      name: 'materialName',
      width: 80,
    },
    {
      name: 'supplierCode',
      width: 80,
    },
    {
      name: 'supplierName',
      width: 80,
    },
    {
      name: 'contractNumber',
      width: 80,
    },
    {
      name: 'attributeVarchar15',
      width: 100,
    },
    {
      name: 'brand',
      width: 80,
    },
    {
      name: 'model',
      width: 80,
    },
    {
      name: 'qty',
      width: 80,
    },
    {
      name: 'minPoQty',
      width: 100,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'uomName',
    },
    {
      name: 'demandTime',
      width: 80,
    },
    {
      name: 'unitPrice',
      width: 80,
    },
    {
      name: 'price',
      width: 80,
    },
    {
      name: "lineDeleteFlag",
      renderer: ({ record }) => (<Badge status={record?.get('lineDeleteFlag') === 'X' ? 'success' : 'error'} text={record?.get('lineDeleteFlag') === 'X' ? '是' : '否'} />),
      width: 80,
    },
    {
      name: 'returnOrder',
      width: 110,
    },
    {
      name: 'instructionDocNum',
      width: 110,
    },
    {
      name: 'expectedArrivalTime',
      width: 130,
    },
    {
      name: 'applyNum',
      width: 80,
    },
    {
      name: 'applyLineNum',
      width: 80,
    },
    {
      name: 'remark',
      width: 80,
    },
  ];

  // 跳转新建页面
  const handleCreate = () => {
    props.history.push('/hwms/purchase/material-approval/detail/create');
  };

  const clickMenu = () => {
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-synthesise-demands/statusChange`, {
      method: 'POST',
      body: {
        processDocIdList: tableDs.selected.map(e => e.get('processDocId')),
      },
    }).then(res => {
      if (res.success) {
        notification.success({});
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
    });
  };

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '100px' }}>
      <Menu.Item key="APPROVED">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu()}>
          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const getQueryParams = () => {
    const searchForm = tableDs?.queryDataSet?.current?.toData();
    return {
      ...searchForm,
      processDocIdList:
        tableDs.selected.length > 0 ? tableDs.selected.map(e => e.get('processDocId')) : [],
    };
  };

  const handleGenerate = () => {
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-synthesise-demands/createDemand`, {
      method: 'POST',
      body: {
        processDocIdList: tableDs.selected.map(e => e.get('processDocId')),
      },
    }).then(res => {
      if (res.success) {
        notification.success({});
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
    });
  };

  const handleSubmit = async () => {
    // const res = await tableDs.submit();
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-synthesise-demands/submit`, {
      method: 'POST',
      body: tableDs.selected.map(e => e.get('processDocId')),
    }).then(res => {
      if (!res.failed) {
        tableDs.query();
      } else {
        notification.error({ message: res.message });
      }
    });
  };

  const renderHeader = () => {
    return (
      <>
        <Dropdown
          overlay={menu}
          disabled={
            !tableDs.selected.length ||
            tableDs.selected.some(
              item => item.toData().status !== 'NEW' && item.toData().status !== 'DISAPPROVED'&& item.toData().status !== 'APPROVED',
            )
          }
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={
              !tableDs.selected.length ||
              tableDs.selected.some(
                item => item.toData().status !== 'NEW' && item.toData().status !== 'DISAPPROVED'&& item.toData().status !== 'APPROVED',
              )
            }
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <PermissionButton
          type="c7n-pro"
          disabled={
            !tableDs.selected.length ||
            tableDs.selected.some(item => item.toData().status !== 'APPROVED')
          }
          permissionList={[
            {
              code: `${path}.button.generate`,
              type: 'button',
              meaning: '资材审批平台-生成需求',
            },
          ]}
          onClick={handleGenerate}
        >
          {intl.get(`${modelPrompt}.button.changeGenerate`).d('生成需求')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          onClick={handleSubmit}
          disabled={
            !tableDs.selected.length ||
            tableDs.selected.some(item => item.toData().status !== 'NEW')
          }
        >
          {intl.get('tarzan.common.button.submit').d('提交')}
        </PermissionButton>
      </>
    );
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.materialApproval`).d('资材审批平台')}>
        <ExcelExport
          method="GET"
          // exportAsync
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-synthesise-demands/export`}
          queryParams={getQueryParams}
          buttonText={intl.get(`${modelPrompt}.`).d('导出')}
        />
        {renderHeader()}
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
      </Header>
      <Content>
        <Table
          searchCode="materialApprovalList"
          customizedCode="materialApprovalList"
          dataSet={tableDs}
          columns={columns}
          highLightRow
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          onRow={({ record }) => {
            return {
              onClick: () => {
                handleRowClick(record);
              },
            };
          }}
        />
        ,
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineDs}
          >
            {lineDs && (
              <Table
                customizedCode="materialApprovalLine"
                className={styles['expand-table']}
                dataSet={lineDs}
                highLightRow={false}
                columns={lineColumns}
              />
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.wms.purchase.materialApproval', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({ ...TabelDS() });
      const lineDs = new DataSet({ ...LineDs() });
      return {
        tableDs,
        lineDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MaterialApprovalList),
);
