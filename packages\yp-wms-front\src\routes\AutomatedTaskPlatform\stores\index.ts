import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import DataSet from 'choerodon-ui/pro/lib/data-set/DataSet';

import { getCurrentOrganizationId } from 'utils/utils';
import { searchCopy } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'modelPrompt_code';
const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: true,
  selection: DataSetSelection.single,
  dataKey: 'rows.content', // 列表数据在接口返回json中的相对路径
  totalKey: 'rows.totalElements',
  primaryKey: 'materialId', // 表格唯一性主键
  queryDataSet: new DataSet({
    events: {
      update({ record, name, value }) {
        searchCopy(
          [
            'materialLotCodes', 'containerCodes'
          ],
          name,
          record,
          value,
        );
      }
    },
    fields: [
      {
        name: 'instructionDocNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.instructionDocNum`).d('关联单据'),
      },
      {
        name: 'autoInsStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.autoInsStatus`).d('指令状态'),
        lookupCode: 'WMS.AUTO_INS_STATUS',
      },
      {
        name: 'autoInstructionNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.autoInstructionNum`).d('任务编号'),
      },
      {
        name: 'siteLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
        lovCode: 'MT.MODEL.SITE',
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'siteCode',
        bind: 'siteLov.siteCode',
      },
      {
        name: 'siteId',
        bind: 'siteLov.siteId',
      },
      {
        name: 'materialLotCodes',
        multiple: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotCodes`).d('物料批编码'),
      },
      {
        name: 'containerCodes',
        multiple: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.containerCodes`).d('容器编码'),
      },
      {
        name: 'fromWarehouseLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.fromWarehouseLov`).d('来源仓库'),
        lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
        ignore: FieldIgnore.always,
        multiple: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              siteId: record?.get('siteId') || null,
              locatorCategory: ['AREA'],
            };
          },
        },
      },
      {
        name: 'fromWarehouseCodes',
        bind: 'fromWarehouseLov.locatorCode',
      },
      {
        name: 'toWarehouseLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.toWarehouseLov`).d('目标仓库'),
        lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
        ignore: FieldIgnore.always,
        multiple: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              siteId: record?.get('siteId') || null,
              locatorCategory: ['AREA'],
            };
          },
        },
      },
      {
        name: 'toWarehouseCodes',
        bind: 'toWarehouseLov.locatorCode',
      },
      {
        name: 'fromLocatorLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.fromLocatorLov`).d('来源库位'),
        lovCode: 'MT.MODEL.LOCATOR',
        ignore: FieldIgnore.always,
        multiple: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              siteId: record?.get('siteId') || null,
            };
          },
        },
      },
      {
        name: 'fromLocatorCodes',
        bind: 'fromLocatorLov.locatorCode',
      },
      {
        name: 'toLocatorLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.toLocatorLov`).d('目标库位'),
        lovCode: 'MT.MODEL.LOCATOR',
        ignore: FieldIgnore.always,
        multiple: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              siteId: record?.get('siteId') || null,
            };
          },
        },
      },
      {
        name: 'toLocatorCodes',
        bind: 'toLocatorLov.locatorCode',
      },
      {
        name: 'autoInstructionType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.autoInstructionType`).d('任务类型'),
        lookupCode: 'WMS_AUTO_TASK_TYPE',
      },
      {
        name: 'autoCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.autoCode`).d('厂商'),
        lookupCode: 'WMS.AUTO_INFORMATION',
        textField: 'value',
      },
      {
        name: 'creationDateFrom',
        type: FieldType.dateTime,
        max: 'creationDateTo',
        label: intl.get(`${modelPrompt}.creationDateFromFrom`).d('最后更新时间从'),
        // defaultValue: moment(new Date()).format('YYYY-MM-DD 00:00:00'),
      },
      {
        name: 'creationDateTo',
        type: FieldType.dateTime,
        min: 'creationDateFrom',
        label: intl.get(`${modelPrompt}.creationDateTo`).d('最后更新时间至'),
        // defaultValue: moment(new Date()).format('YYYY-MM-DD 23:59:59'),
      },
    ]
  }),
  fields: [
    {
      name: 'autoInstructionNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoInstructionNum`).d('任务编号'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('关联单据'),
    },
    {
      name: 'autoInstructionStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoInstructionStatusDesc`).d('指令状态'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('指令数量'),
    },
    {
      name: 'actualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualQty`).d('实绩数量'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'fromWarehouse',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromWarehouse`).d('来源仓库'),
    },
    {
      name: 'toWarehouse',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toWarehouse`).d('目标仓库'),
    },
    {
      name: 'fromLocator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromLocator`).d('来源库位'),
    },
    {
      name: 'toLocator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toLocator`).d('目标库位'),
    },
    {
      name: 'containerTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeCode`).d('容器类型'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'autoInstructionTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoInstructionTypeDesc`).d('任务类型'),
    },
    {
      name: 'toLocation',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toLocation`).d('目标地点'),
    },
    {
      name: 'autoCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoCode`).d('厂商'),
    },
    {
      name: 'errorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.errorCode`).d('错误编码'),
    },
    {
      name: 'errorMessage',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.errorMessage`).d('错误消息'),
    },
    {
      name: 'taskLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLevel`).d('任务等级'),
    },
    {
      name: 'taskLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.taskLot`).d('任务批次'),
    },
    {
      name: 'priorty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.priorty`).d('优先级'),
    },
    {
      name: 'createRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createRealName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastupdateRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastupdateRealName`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-auto-instructions/query/head/ui`,
        method: 'GET',
      };
    },
  },
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content', // 列表数据在接口返回json中的相对路径
  totalKey: 'rows.totalElements',
  primaryKey: 'materialId', // 表格唯一性主键
  fields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'actualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.actualQty`).d('实绩数量'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('顶层容器编码'),
    },
    {
      name: 'lineFromLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineFromLocatorCode`).d('来源库位'),
    },
    {
      name: 'lineToLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineToLocatorCode`).d('目标库位'),
    },
    {
      name: 'arrivalFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.arrivalFlag`).d('是否到达'),
    },
    {
      name: 'lineCreateRealname',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineCreateRealname`).d('创建人'),
    },
    {
      name: 'lineCreateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineCreateDate`).d('创建时间'),
    },
    {
      name: 'lineUpdateRealname',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineUpdateRealname`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-auto-instructions/query/line/ui`,
        method: 'GET',
      };
    },
  },
});

export { headDS, lineDS };
