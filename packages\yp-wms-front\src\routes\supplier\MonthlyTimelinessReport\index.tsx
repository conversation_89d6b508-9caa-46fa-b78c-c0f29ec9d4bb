import React, { useMemo } from 'react';
import { Table, DataSet, Button, Modal } from 'choerodon-ui/pro';
import { ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import { isNil } from 'lodash';
import { observer } from 'mobx-react-lite';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import SupplierDetailsModal from './modals/SupplierDetailsModal';
import { tableDS } from './stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.wms.supplier.monthlyTimelinessReport';

const MonthlyTimelinessReport = props => {
  const { tableDs } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'receiveDoodsTime',
        width: 150,
      },
      {
        name: 'planQty',
        width: 150,
      },
      {
        name: 'punctualQty',
        width: 150,
      },
      {
        name: 'noPunctualQty',
        width: 120,
      },
      {
        name: 'punctualRate',
        width: 120,
      },
    ];
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return {
      ...queryParmas,
      times: tableDs.selected.map((item: any) => item.toData().receiveDoodsTime),
    };
  };

  const handleJumpToMobileEventDetailReport = () => {
    Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.wms.supplier.monthlyTimelinessReport.supplierDetail').d('供应商明细'),
      style: {
        width: 720,
      },
      destroyOnClose: true,
      drawer: true,
      closable: true,
      children: (
        <SupplierDetailsModal
          data={tableDs.selected}
          queryData={tableDs.queryDataSet.toData()[0]}
        />
      ),
    });
  };

  const renderHeader = () => {
    const ObserverButtons = observer((props: any) => {
      const isDisabled = props.dataSet.toData().length === 0;
      return (
        <Button
          type={ButtonType.button}
          onClick={props.onClick}
          disabled={isDisabled || props.disabled}
          color={props.color}
        >
          {props.title}
        </Button>
      );
    });
    return (
      <div>
        <ObserverButtons
          onClick={handleJumpToMobileEventDetailReport}
          dataSet={tableDs}
          title="供应商明细"
        ></ObserverButtons>
      </div>
    );
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get('tarzan.wms.supplier.monthlyTimelinessReport.title.New.list')
          .d('供应商每月到货及时率报表')}
      >
        <ExcelExport
          method="GET"
          // exportAsync
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-month-delivery-timely/list/get/ui/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        {renderHeader()}
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="monthlyTimelinessReport"
          customizedCode="monthlyTimelinessReport"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.wms.supplier.monthlyTimelinessReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MonthlyTimelinessReport),
);
