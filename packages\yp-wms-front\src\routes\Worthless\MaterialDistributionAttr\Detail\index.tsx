/**
 * @feature 物料配送属性维护-详情页入口文件
 * @date 2021-3-8
 * <AUTHOR> <<EMAIL>>
 */
import React, { useMemo, useRef, useState } from 'react';
import { Button, DataSet, Form, Lov, Modal } from 'choerodon-ui/pro';
import { Divider } from 'choerodon-ui';
import intl from 'utils/intl';
import myInstance from '@utils/myAxios';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content, Header } from 'components/Page';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { AttributeDrawer, LovSelect } from '@components/tarzan-ui';
import { Button as PermissionButton } from 'components/Permission';
import { BASIC } from '@utils/config';
import Detail from './detail';
import { copySourceDS, copyTargetDS } from '../stories/CopyPageDs';
import { baseInfoDS } from '../stories/BaseInfoDs';

const tenantId = getCurrentOrganizationId();

const TABLENAME = 'org.tarzan.method.domain.entity.MtPfepDistribution';
const TABLENAME2 = 'org.tarzan.method.domain.entity.MtPfepDistributionCategory';

const index = props => {
  const {
    match: { path },
    customizeForm,
    custConfig,
  } = props;
  const { id, type } = props.match.params;
  const [canEdit, setCanEdit] = useState(id === 'create');
  const childRef = useRef();
  const copySourceDs = useMemo(() => new DataSet(copySourceDS()), []);
  const copyTargetDs = useMemo(() => new DataSet(copyTargetDS()), []) as any;

  const baseInfoDs = useMemo(
    () =>
      new DataSet({
        ...baseInfoDS(),
        children: {
          copyTargetDs,
        },
      }),
    [],
  );

  let copyDrawer;

  const handleSave = async () => {
    const childRefCurrent = childRef.current as any;
    const { success, resultId, materialSelect } = await childRefCurrent.submit();
    if (success) {
      setCanEdit(prev => !prev);
      const materialType = materialSelect === 'material' ? materialSelect : 'category';

      props.history.push(`/hmes/product/pfep-distribution-new/detail/${materialType}/${resultId}`);
      childRefCurrent.detailQuery(resultId, materialType);
    }
  };

  const handleCancel = () => {
    if (id === 'create') {
      props.history.push('/hmes/product/pfep-distribution-new/list');
    } else {
      setCanEdit(prev => !prev);
      (childRef as any).current.detailQuery(id, type);
    }
  };

  const copyDrawerSave = async () => {
    const validate = await copyTargetDs.validate(false, true); // 抽屉信息校验
    if (validate) {
      const copyTargetData = copyTargetDs.toData() as object;
      const url = `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-pfep-distribution/pfep/copy/ui`;
      let goalTargetLocatorId;
      if (copyTargetData[0].goalOrganizationType === 'LOCATOR') {
        goalTargetLocatorId = copyTargetData[0].pfepDisOrganization.locatorId;
      } else {
        goalTargetLocatorId = copyTargetData[0].targetLocatorCode.locatorId;
      }
      const parentData = (copyTargetDs as any).parent.current.toData();
      myInstance
        .post(url, {
          ...copyTargetData[0],
          sourcePfepDistributionId: id,
          pfepDistributionType: type,
          goalOrganizationId:
            copyTargetData[0].pfepDisOrganization.locatorId ||
            copyTargetData[0].pfepDisOrganization.workcellId,
          goalTargetLocatorId,
          goalSiteId: parentData.siteId,
        })
        .then(res => {
          if (res.data.success) {
            const { pfepDistributionType, pfepDistributionId } = res.data.rows;
            copyDrawer.close();
            notification.success({});
            props.history.push(
              `/hmes/product/pfep-distribution-new/detail/${pfepDistributionType}/${pfepDistributionId}`,
            );
            // 复制成功后调用query方法
            (childRef as any).current.detailQuery(pfepDistributionId, pfepDistributionType);
          } else {
            notification.error({
              message: res.data.message,
            });
          }
        });
    }
  };

  const changeLovSelectCopyTarget = () => {
    copyTargetDs.current.init('materialCode');
  };

  const changeLovSelectPfep = () => {
    copyTargetDs.current.init('pfepDisOrganization');
    copyTargetDs.current.init('targetLocatorCode');
  };

  // 打开复制抽屉
  const handleCopyDrawerShow = () => {
    copyTargetDs.create({ goalOrganizationType: copySourceDs?.current?.get('organizationType') });
    copyDrawer = Modal.open({
      key: Modal.key(),
      title: intl.get('tarzan.common.button.copy').d('复制'),
      drawer: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal copy-drawer-modal',
      children: (
        <>
          <Form disabled={!canEdit} dataSet={copySourceDs} columns={1} labelWidth={112}>
            <LovSelect
              dataSet={copySourceDs}
              name="materialCode1"
              selectName="materialSelect"
              placeholder=" "
              noCache
            />
            <Lov name="sourceLocatorCode" placeholder=" " />
            <LovSelect
              dataSet={copySourceDs}
              name="pfepDisOrganization"
              selectName="organizationType"
              placeholder=" "
              noCache
            />
            <Lov name="targetLocatorCode" placeholder=" " />
          </Form>
          <Divider style={{ margin: '12px' }} />
          <Form dataSet={copyTargetDs} columns={1} labelWidth={112}>
            <LovSelect
              dataSet={copyTargetDs}
              name="materialCode"
              selectName="materialSelect"
              placeholder=" "
              noCache
              selectChange={changeLovSelectCopyTarget}
            />
            <Lov name="sourceLocatorCode" placeholder=" " />
            <LovSelect
              dataSet={copyTargetDs}
              name="pfepDisOrganization"
              selectName="goalOrganizationType"
              placeholder=" "
              noCache
              selectChange={changeLovSelectPfep}
            />
            <Lov name="targetLocatorCode" placeholder=" " />
          </Form>
        </>
      ),
      footer: (
        <>
          <div style={{ float: 'right' }}>
            <Button onClick={() => copyDrawer.close()}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
            <Button type={ButtonType.submit} onClick={copyDrawerSave} color={ButtonColor.primary}>
              {intl.get('tarzan.common.button.confirm').d('确定')}
            </Button>
          </div>
        </>
      ),
    });
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get('tarzan.product.materialDistributionNew.title.list').d('物料配送属性维护')}
        backPath="/hmes/product/pfep-distribution-new/list"
      >
        {canEdit ? (
          <>
            <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </Button>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        ) : (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="content_copy-o"
          disabled={canEdit}
          style={{ marginLeft: '10px' }}
          onClick={handleCopyDrawerShow}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.copy').d('复制')}
        </PermissionButton>
        <AttributeDrawer
          className={type === 'material' ? TABLENAME : TABLENAME2}
          kid={id}
          canEdit={canEdit}
          disabled={id === 'create'}
          serverCode={BASIC.TARZAN_METHOD}
          custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DISTRIBUTION_DETAIL.BUTTON`}
          custConfig={custConfig}
        />
      </Header>
      <Content>
        <Detail
          canEdit={canEdit}
          ref={childRef}
          id={id}
          type={type}
          columns={3}
          copySourceDs={copySourceDs}
          baseInfoDs={baseInfoDs}
          customizeForm={customizeForm}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.product.materialDistributionNew', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DISTRIBUTION_DETAIL.MATERIAL_BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DISTRIBUTION_DETAIL.CATEGORY_BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DISTRIBUTION_DETAIL.BUTTON`],
  // @ts-ignore
})(index));
