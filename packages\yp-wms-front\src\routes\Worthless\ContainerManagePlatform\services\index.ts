/**
 * @Description: 物料批管理平台-services
 * @Author: <<EMAIL>>
 * @Date: 2022-01-18 20:19:41
 * @LastEditTime: 2023-05-18 16:07:44
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 容器管理平台-保存
export function SaveContainer() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.CREATE`,
    method: 'POST',
  };
}
