/**
 * @feature 物料配送属性维护
 * @date 2021-3-8
 * <AUTHOR> <<EMAIL>>
 */

import React, { useEffect } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Badge } from 'hzero-ui';
import queryString from 'querystring';
import { openTab } from 'utils/menuTab';
import { getCurrentOrganizationId } from 'utils/utils';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import withProps from 'utils/withProps';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { entranceDS } from './stories/EntranceDs';

const modelPrompt = 'tarzan.product.materialDistributionNew.model.materialDistributionNew';
const tenantId = getCurrentOrganizationId();

/**
 * 头行结构的表单示例
 */
const entrance = props => {
  const {
    match: { path },
  } = props;
  const columns = [
    {
      name: 'materialCateGoryName',
      align: 'left',
      renderer: ({ record }) => {
        return (
          <Tag color={record.data.materialId ? 'blue' : 'orange'}>
            {record.data.materialId
              ? intl.get(`${modelPrompt}.materialCode`).d('物料')
              : intl.get(`${modelPrompt}.materialCategoryCode`).d('物料类别')}
          </Tag>
        );
      },
      lock: 'left',
      width: 180,
    },
    {
      name: 'materialCode',
      align: 'left',
      lock: 'left',
      width: 150,
      renderer: ({ record }) => {
        const code = record.data.materialCode
          ? record.data.materialCode
          : record.data.materialCategoryCode;
        if (record.data.upperMaterialCateCode) {
          return code;
        }
        return (
          <a
            onClick={() => {
              const type = record.data.materialId ? 'material' : 'category';
              const id = record.data.pfepDistributionId;
              props.history.push(`/hmes/product/pfep-distribution-new/detail/${type}/${id}`);
            }}
          >
            {code}
          </a>
        );
      },
    },
    {
      name: 'description',
      align: 'left',
      lock: 'left',
      width: 150,
      renderer: ({ record }) => {
        return record.data.materialId ? record.data.materialName : record.data.materialCategoryDesc;
      },
    },
    {
      name: 'upperMaterialCateCode',
      align: 'left',
      lock: 'left',
      width: 150,
      renderer: ({ record }) => {
        if (record.data.upperMaterialCateCode) {
          return (
            <a
              onClick={() => {
                const type = record.data.materialId ? 'material' : 'category';
                const id = record.data.pfepDistributionId;

                props.history.push(`/hmes/product/pfep-distribution-new/detail/${type}/${id}`);
              }}
            >
              {record.data.upperMaterialCateCode}
            </a>
          );
        }
      },
    },
    {
      name: 'siteCode',
      align: 'left',
      renderer: ({ record }) => {
        return record.data.siteCode;
      },
      width: 150,
    },
    {
      name: 'targetPlaceCode',
      width: 150,
    },
    {
      name: 'distributionQty',
      width: 150,
    },
    {
      name: 'singleQty',
      width: 150,
    },
    {
      name: 'autoCallingFlag',
      width: 120,
      align: 'center',
      renderer: ({ record }) => (
        <Badge
          status={record.get('autoCallingFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('autoCallingFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
    { name: 'distributionRouteCode', align: 'left', width: 200 },
    {
      name: 'distributionCategoryDesc',
      align: 'center',
      width: 180,
      renderer: ({ record }) => {
        const data = record.data.distributionCategory;
        if (data) {
          return (
            <Tag color={data === 'TIMING_DISTRIBUTION' ? 'blue' : 'orange'}>
              {record.data.distributionCategoryDesc}
            </Tag>
          );
        }
      },
    },
    {
      name: 'organizationType',
      renderer: ({ record }) => {
        const data =
          record.data.organizationType === 'LOCATOR'
            ? intl.get(`${modelPrompt}.locator`).d('库位')
            : intl.get(`${modelPrompt}.workcell`).d('工作单元');
        return (
          <Tag color={record.data.organizationType === 'LOCATOR' ? 'blue' : 'orange'}>{data}</Tag>
        );
      },
      align: 'left',
    },
    {
      name: 'organizationCode',
      align: 'left',
    },
    {
      name: 'enableFlag',
      renderer: ({ record }) => (
        <Badge
          status={record.get('enableFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('enableFlag') === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
      align: 'center',
    },
    { name: 'packQty', align: 'right' },
    { name: 'sourceLocatorCode', align: 'left', width: 200 },
    {
      name: 'multiplesOfPackFlag',
      renderer: ({ record }) => (
        <Badge
          status={record.get('multiplesOfPackFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('multiplesOfPackFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
      align: 'center',
    },
    {
      name: 'distributionModeDesc',
      align: 'center',
      width: 180,
    },
    { name: 'distributionCycle', align: 'right' },
    { name: 'materialConsumeRate', align: 'right' },
    { name: 'uomName', align: 'left', width: 150 },
    { name: 'bufferInventory', align: 'right' },
    { name: 'maxInventory', align: 'right' },
    { name: 'minInventory', align: 'right' },
    { name: 'bufferPeriod', align: 'right' },
    { name: 'calendarCode', align: 'left', width: 200 },
    {
      name: 'instructionCreatedByEoFlag',
      renderer: ({ record }) => (
        <Badge
          status={record.get('instructionCreatedByEoFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('instructionCreatedByEoFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
      align: 'center',
      width: 200,
    },
    {
      name: 'fromScheduleRateFlag',
      renderer: ({ record }) => (
        <Badge
          status={record.get('fromScheduleRateFlag') === 'Y' ? 'success' : 'error'}
          text={
            record.get('fromScheduleRateFlag') === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
      align: 'center',
      width: 150,
    },
  ];

  const goDetail = () => {
    props.history.push(`/hmes/product/pfep-distribution-new/detail/default/create`);
  };

  useEffect(() => {
    props.dataSet.query(props.dataSet.currentPage);
  }, []);
  useEffect(() => {
    function processDataSetListener(flag) {
      if (props.dataSet.queryDataSet) {
        const handler = flag
          ? props.dataSet.queryDataSet.addEventListener
          : props.dataSet.queryDataSet.removeEventListener;
        handler.call(props.dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
      }
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (!data.siteId) {
      // 让queryFields里的siteId相关信息清空
      record.set('material', {});
      record.set('materialCategoryCode', {});
      record.set('areaCode', {});
      record.set('sourceLocatorCode', {});
      record.set('pfepDistributionOrganizationId', {});
    }
    if (!data.organizationType) {
      record.set('pfepDistributionOrganizationId', {});
    }
  };

  const handleImport = () => {
    openTab({
      key: '/himp/commentImport/MT_PFEP_DISTRIBUTION_IMPORT',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl.get('tarzan.product.materialDistributionNew.title.list').d('物料配送属性维护')}
        header={
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="add"
              onClick={goDetail}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.create').d('新建')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
              icon="file_upload"
              onClick={handleImport}
            >
              {intl.get('tarzan.common.button.import').d('导入')}
            </PermissionButton>
          </>
        }
      >
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={props.dataSet}
          columns={columns as ColumnProps[]}
          searchCode="MaterialDistributionAttr"
          customizedCode="MaterialDistributionAttr"
        />
      </PageHeaderWrapper>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.product.materialDistributionNew', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...entranceDS(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(entrance),
);
