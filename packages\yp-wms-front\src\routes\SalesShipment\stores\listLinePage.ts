import { DataSet } from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.aps.salesShipment';
const listPageLineFactory = () =>
  new DataSet({
    primaryKey: 'id',
    selection: false,
    autoQuery: false,
    pageSize: 10,
    dataKey: 'content',
    totalKey: 'totalElements',
    fields: [
      {
        name: 'saleOrderLineNum',
        label: '发运单行号',
        type: FieldType.string,
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'qty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.qty`).d('数量'),
      },
      {
        name: 'plantCode',
        label: intl.get(`${modelPrompt}.plantCode`).d('工厂编码'),
      },
      {
        name: 'locatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
      },
      {
        name: 'lot',
        label: '批次',
        type: FieldType.string,
      },
      {
        name: 'cancelFlag',
        label: '取消标识',
        type: FieldType.string,
      },
      {
        name: 'creationDate',
        label: '创建时间',
        type: FieldType.string,
      },
      {
        name: 'createByName',
        label: '创建人',
        type: FieldType.string,
      },

      {
        name: 'lastUpdateDate',
        label: '最后更新时间',
        type: FieldType.dateTime,
      },
      {
        name: 'lastUpdateByName',
        label: '最后更新人',
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${
            BASIC.HMES_BASIC
          }/v1/${getCurrentOrganizationId()}/wms-so-create/line/list`,
        };
      },
    },
  });

export default listPageLineFactory;
