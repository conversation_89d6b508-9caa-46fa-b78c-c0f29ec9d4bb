import React, { useEffect, useState, useMemo } from 'react';
import { Table, Button, DataSet, Modal, NumberField } from 'choerodon-ui/pro';
import { Badge, Collapse } from 'choerodon-ui';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { observer } from 'mobx-react';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import ExcelExport from 'components/ExcelExport';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import { tableDS, lineTableDS } from '../stores/ListDS';

const titlePromptCode = 'hwms.purchaseNeed.view.title';
const modelPrompt = 'hwms.purchaseNeed.field';
const tenantId = getCurrentOrganizationId();

const { Panel } = Collapse;


const TablePage = observer(props => {
  const { lineTableDs, tableDs, customizeTable } = props;

  // 重新执行 按钮点击状态
  const [isErrorEdit, setIsErrorEdit] = useState<boolean>(false)
  // 编辑按钮 点击状态
  const [isSuccessEdit, setIsSuccessEdit] = useState<boolean>(false)

  useEffect(() => {
    if (tableDs) {
      tableDs.query();
      tableDs.addEventListener('load', handleResetHeaderQuery);
    }
    return () => {
      if (tableDs) {
        tableDs.removeEventListener('load', handleResetHeaderQuery);
      }
    }
  }, []);

  useDataSetEvent(tableDs, 'select', ({ record }) => {
    headerRowClick(record)
  })

  const handleResetHeaderQuery = ({ dataSet }) => {
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      lineTableDs.loadData([]);
    }
  };

  /** @type {ColumnProps} 头列 */
  const headerTableColumns = [
    { name: 'demandOrder' },
    { name: 'demandOrderTypeMeaning' },
    { name: 'returnOrder' },
    { name: 'supplierCode' },
    { name: 'supplierName' },
    {
      name: 'prodSampleFlag',
      renderer: ({ record }) => (<Badge
        status={record.get('prodSampleFlag') === 'Y' ? 'success' : 'error'}
        text={
          record.get('prodSampleFlag') === 'Y'
            ? intl.get('tarzan.common.label.yes').d('是')
            : intl.get('tarzan.common.label.no').d('否')
        }
      />),
    },
    { name: 'buyerCode' },
    { name: 'makerCode' },
    { name: 'demandType' },
    { name: 'message' },
    { name: 'creationDate' },
    { name: 'createdByName' },
    { name: 'lastUpdateDate' },
    { name: 'lastUpdatedByName' },
  ];

  const lineTableColumns = [
    { name: 'lineNum' },
    { name: 'materialCode' },
    { name: 'materialName' },
    {
      name: 'qty',
      // editor: (isErrorEdit || isSuccessEdit) && <NumberField name='qtyCopy' />,
    },
    { name: 'uomCode' },
    { name: 'siteCode' },
    { name: 'locatorLov', editor: isErrorEdit || isSuccessEdit },
    {
      name: 'processDocNum',
      width:180
    },
    {
      name: 'applyPersonName',
    },
    {
      name: 'deleteFlag',
      renderer: ({ record }) => (<Badge
        status={record.get('deleteFlag') === 'X' ? 'success' : 'error'}
        text={
          record.get('deleteFlag') === 'X'
            ? intl.get('tarzan.common.label.yes').d('是')
            : intl.get('tarzan.common.label.no').d('否')
        }
      />),
    },
    { name: 'demandDate', editor: isErrorEdit || isSuccessEdit },
    { name: 'creationDate' },
    { name: 'lastUpdateDate' },
    { name: 'poCotractNum' },
  ];

  // const handlePrint = () => {
  //   // if(tableDs.selected.length > 0) {
  //     request(`${BASIC.HMES_BASIC}/v1/${tenantId}/procurement-demand-platform/demandExport`, {
  //       method: 'GET',
  //       // body: tableDs.selected.map(e => e.get('demandOrderId')),
  //       body:tableDs.queryDataSet.current.toData(),
  //       responseType: 'blob',
  //     }).then(res => {
  //       console.log(res)
  //       if (res.failed) {
  //         notification.error({ message: res.exception });
  //       } else {
  //         const file = new Blob([res], { type: 'application/pdf' });
  //         const fileURL = URL.createObjectURL(file);
  //         const newwindow = window.open(fileURL, 'newwindow');
  //         if (newwindow) {
  //           newwindow.print();
  //         } else {
  //           notification.error({ message: intl.get(`${modelPrompt}.notification.browser.config`).d('当前窗口已被浏览器拦截，请手动设置浏览器！') });
  //         }
  //       }
  //     })
  //   // }
  // };

  const headerRowClick = (record) => {
    setIsErrorEdit(false);
    setIsSuccessEdit(false);
    lineTableDs.setQueryParameter('demandOrderId', record.get('demandOrderId'));
    lineTableDs.query();
  };

  const getQueryParams = () => {
    const params = tableDs.queryDataSet.current.toData()
    return params
  }

  const changeStatus = () => {
    const list = lineTableDs.toData()
    if (isErrorEdit) {
      lineTableDs.loadData(list.map(ele => ({
        ...ele,
        qty: ele.qtyCopy,
      })));
    } else {
      lineTableDs.loadData(list.map(ele => ({
        ...ele,
        qtyCopy: ele.qty,
      })))
    }
    setIsErrorEdit(prev => !prev)
  }

  const saveData = async (_data = undefined) => {
    let data = _data;
    if (!data) {
      data = lineTableDs.toData();
    }
    const res = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/procurement-demand-platform/demandLineSave`, {
      method: 'POST',
      body: data,
    })
    if (res && res.message&&!res.success) {
      notification.error({
        message: res.message,
      })
      return false;
    }
    setIsErrorEdit(false);
    setIsSuccessEdit(false);
    lineTableDs.query();
    return true;
  }

  const handleCancelEdit = () => {
    setIsSuccessEdit(false);
    lineTableDs.reset();
  };

  const reRunBtnDisabled = useMemo(() => {
    return tableDs.selected.length === 0 || !tableDs.selected[0]?.get('message')?.startsWith('E');
  }, [tableDs, tableDs.selected]);

  const editBtnDisabled = useMemo(() => {
    return tableDs.selected.length === 0 || !tableDs.selected[0]?.get('message')?.startsWith('S');
  }, [tableDs, tableDs.selected]);

  const editLineBtnDisabled = useMemo(() => {
   const flag= lineTableDs.selected.every(item=>item.get('cancelFlag')==='Y')
    return lineTableDs.selected.length === 0||!flag;
  }, [lineTableDs, lineTableDs.selected]);



  const handleClickCancel = () => {
    Modal.confirm({
      title: intl.get(`${modelPrompt}.confirm.cancel`).d('请确认是否要取消该订单?'),
      okText: intl.get('tarzan.common.button.confirm').d('确认'),
      cancelText: intl.get('tarzan.common.button.cancel').d('取消'),
      onOk() {
        return new Promise(async (resolve) => {
          let lineData;
          if (!lineTableDs.selected.length) {
            lineData = lineTableDs.map((record) => ({
              ...record?.toData(),
              lineDeleteFlag: 'X',
              headFlag: 'Y',
              status: 'CANCEL',
            }));
          } else {
            lineData = lineTableDs.selected?.map((record) => ({
              ...record?.toData(),
              lineDeleteFlag: 'X',
              headFlag: 'N',
              status: 'CANCEL',
            }));
          }
          const res = await saveData(lineData);
          if (res) {
            lineTableDs.unSelectAll();
            notification.success({
              message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
            });
            return resolve(true);
          }
          return resolve(false);
        })
      },
      onCancel() { },
    });
  };

  return (
    <PageHeaderWrapper
      header={
        <>
          {isErrorEdit ? (
            <>
              <Button onClick={changeStatus}>
                {intl.get('hzero.common.button.cancel').d('取消')}
              </Button>
              <Button onClick={() => saveData()} color={ButtonColor.primary}>
                {intl.get('hzero.common.button.saveand').d('保存并重新执行')}
              </Button>
            </>
          ) : (
            <Button onClick={changeStatus} color={ButtonColor.primary} disabled={reRunBtnDisabled}>
              {intl.get(`${modelPrompt}.button.reRun`).d('重新执行')}
            </Button>
          )}
          {isSuccessEdit ? (
            <>
              <Button onClick={() => handleCancelEdit()}>
                {intl.get('hzero.common.button.cancel').d('取消')}
              </Button>
              <Button onClick={() => saveData()} color={ButtonColor.primary}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
            </>
          ) : (
            <>
              <Button
                onClick={() => setIsSuccessEdit(prep => !prep)}
                color={ButtonColor.primary}
                disabled={editBtnDisabled}
              >
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
              <Button
                disabled={editLineBtnDisabled}
                onClick={handleClickCancel}
              >
                {intl.get('tarzan.common.button.cancelDoc').d('取消需求单')}
              </Button>
            </>
          )}
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/procurement-demand-platform/demandExport`}
            queryParams={getQueryParams}
            buttonText={intl.get(`${modelPrompt}.`).d('导出')}
          />
        </>
      }
      title={intl.get(`${titlePromptCode}.outFactoryApply`).d('采购需求平台')}
    >
      {customizeTable(
        {
          filterCode: `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.QUERY`,
          code: `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.HEAD`,
        },
        <Table
          searchCode="ltlgzt1"
          customizedCode="ltlgzt1"
          dataSet={tableDs}
          columns={headerTableColumns}
          highLightRow
          queryFieldsLimit={8}
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
        />,
      )}
      <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
          key="basicInfo"
          dataSet={lineTableDs}
        >
          {lineTableDs && (
            customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.LINE`,
              },
              <Table
                customizedCode="ltlgzt2"
                dataSet={lineTableDs}
                highLightRow={false}
                columns={lineTableColumns}
              />,
            )
          )}

        </Panel>
      </Collapse>
    </PageHeaderWrapper>
  );
});
export default flow(
  withProps(
    () => {
      const tableDs = new DataSet({ ...tableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        tableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.LINE`,
    ],
  }),
  formatterCollections({ code: ['hwms.outFactoryApply', 'hzero.common'] }),
)(TablePage);
