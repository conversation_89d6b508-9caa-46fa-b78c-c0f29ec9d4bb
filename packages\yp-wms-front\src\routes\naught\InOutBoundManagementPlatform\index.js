// 无价值出入库管理平台单查询
import React, { Component, Fragment } from 'react';
import {DataSet, Table, Modal, Button, Form, NumberField,DateTimePicker, TextField, Lov, TextArea, Select,Dropdown,Menu,Icon} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { isNil } from 'lodash';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import notification from 'utils/notification';
import { headTableDS, lineTableDS, detailTableDS,exportDS, createNewOrderDS, createMaterialDS, materialLotTableDS } from './stores/InOutBoundManagementPlatformDS';

// const BASIC = {
//   HMES_BASIC: '/yp-mes-40066',
// };
const tenantId = getCurrentOrganizationId();
const Panel = Collapse.Panel;
let modalAssembly;

export default class InOutBoundManagementPlatform extends Component {
  constructor(props) {
    super(props);
    this.state = {
      cancelDisabled:true,
      exportDisabled:true,
      excelExportDisabled:true,
    };
    this.headTableDs = new DataSet({
      ...headTableDS(),
      events:{
        select:({dataSet})=>{
          const list=dataSet.selected.map(ele=>ele.toJSONData())
          this.setState({
            cancelDisabled:list.length===0||list.some(ele=>ele.instructionDocStatus!=='RELEASED'),
            exportDisabled:list.length===0||list.some(ele=>ele.dangerousDocFlag!=='Y'),
            excelExportDisabled:list.length===0||list.some(ele=>ele.dangerousDocFlag==='Y'),
          })
        },
        selectAll:({dataSet})=>{
          const list=dataSet.selected.map(ele=>ele.toJSONData())
          this.setState({
            exportDisabled:list.length===0||list.some(ele=>ele.dangerousDocFlag!=='Y'),
            excelExportDisabled:list.length===0||list.some(ele=>ele.dangerousDocFlag==='Y'),
            cancelDisabled:list.length===0||list.some(ele=>ele.instructionDocStatus!=='RELEASED')
          })
        },
        unSelect:({dataSet})=>{
          const list=dataSet.selected.map(ele=>ele.toJSONData())
          this.setState({
            exportDisabled:list.length===0||list.some(ele=>ele.dangerousDocFlag!=='Y'),
            excelExportDisabled:list.length===0||list.some(ele=>ele.dangerousDocFlag==='Y'),
            cancelDisabled:list.length===0||list.some(ele=>ele.instructionDocStatus!=='RELEASED')
          })
        },
        unSelectAll:({dataSet})=>{
          const list=dataSet.selected.map(ele=>ele.toJSONData())
          this.setState({
            exportDisabled:list.length===0||list.some(ele=>ele.dangerousDocFlag!=='Y'),
            excelExportDisabled:list.length===0||list.some(ele=>ele.dangerousDocFlag==='Y'),
            cancelDisabled:list.length===0||list.some(ele=>ele.instructionDocStatus!=='RELEASED')
          })
        },
        load: ({dataSet})=>{
          this.handleHeadTableQuery(dataSet);
        },
      },
    }); // 头表格dateSet
    this.lineTableDs = new DataSet(lineTableDS()); // 行表格dateSet
    this.detailTableDs = new DataSet(detailTableDS()); // 明细表格dateSet
    this.createNewOrderDs = new DataSet(createNewOrderDS());
    this.createMaterialDs = new DataSet(createMaterialDS());
    this.exportDs=new DataSet(exportDS())
    this.materialLotTableDs = new DataSet(materialLotTableDS());
    this.materialLotColumns = [
      { name: 'materialLotCode', width: 180 },
      { name: 'siteCode', width: 130 },
      { name: 'siteName' },
      { name: 'locatorCode' },
      { name: 'materialCode', width: 120 },
      { name: 'materialName', width: 130 },
      { name: 'weight' },
      { name: 'uomCode' },
      { name: 'creationDate', width: 150 },
      { name: 'remark' },
    ];
  }

  // 处理头表格查询事件
  handleHeadTableQuery = dataSet => {
    if (!dataSet.length) {
      this.lineTableDs.loadData([]);
    }
  };

  // 查询行
  headerRowClick = record => {
    const instructionDocId = record.get('instructionDocId');
    this.lineTableDs.setQueryParameter('instructionDocId', instructionDocId);
    this.lineTableDs.query();
  };

  // 查询物料批明细
  handleMaterialLotDetail = record => {
    const instructionDocLineId = record.get('instructionDocLineId');
    this.detailTableDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
    this.detailTableDs.query();
    const detailColumns = [
      { name: 'materialLotCode', width: 150, align: 'center' },
      { name: 'materialLotStatusDesc', align: 'center' },
      { name: 'containerCode', align: 'center' },
      { name: 'primaryUomQty', align: 'center' },
      { name: 'uomCode', align: 'center' },
      { name: 'lot', align: 'center' },
    ];
    modalAssembly = Modal.open({
      title: '物料批明细',
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <Table
          dataSet={this.detailTableDs}
          columns={detailColumns}
          customizedCode="detailInOutBoundManagementPlatform"
        />
      ),
      footer: <Button onClick={() => modalAssembly.close()}>返回</Button>,
    });
  };

  // 操作列渲染
  optionRender = record => (
    <>
      <a
        style={{ marginRight: '8px' }}
        onClick={() => {
          this.handleMaterialLotDetail(record);
        }}
      >
        物料批明细
      </a>
    </>
  );

  getExportQueryParams = () => {
    if (!this.headTableDs.queryDataSet || !this.headTableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = this.headTableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return {
      ...queryParmas,
      instructionDocIds:this.headTableDs.selected.map(item=>item.get("instructionDocId")),
    };
  };

  cancelData=()=>{
    const list=this.headTableDs.selected.map(ele=>ele.toJSONData())
    const data=list.map(ele=>ele.instructionDocId)
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-in-and-out/status-change`, {
      method: 'post',
      data,
    }).then(res => {
      console.log(res)
      if (res&&res.message) {
        notification.error({ message: res.exception });
      } else {
        this.headTableDs.query()
      }
    })
  }

  exportExcel=()=>{
    const list=this.headTableDs.selected.map(ele=>ele.toJSONData())
    
    const isRequired=list.some(ele=>ele.instructionDocType==='NOWORTH_IN_DOC')
    this.exportDs.fields.get('remark').set('required',isRequired)
    this.exportDs.loadData([{
      instructionDocNums:list.map(ele=>ele.instructionDocNum)
    }])
    Modal.open({
      key: Modal.key(),
      title: '记录表导出',
      destroyOnClose: true,
      closable: true,
      keyboardClosable: true,
      style: {
        width: 760,
      },
      className: 'hmes-style-modal',
      children:<Form dataSet={this.exportDs}>
        <TextField name='instructionDocNums' disabled/>
        <TextField name='remark'/>
      </Form>,
      onOk:async ()=>{
        const flag = await this.exportDs.validate();
        if(!flag) return
        const data= list.map(ele=>({
          "instructionDocId": ele.instructionDocId,
          "instructionDocNum": ele.instructionDocNum,
          "instructionDocType": ele.instructionDocType,
          "remark": this.exportDs.current?.get('remark')
        }))
        const res=await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-in-and-out/doc/export`, {
          method: 'post',
          data,
          responseType:'blob'
        })
        if((res.type||'').indexOf('json')>=0){
          const err=new Blob([res], { type: 'application/json' })
          let reader=new FileReader()
          reader.addEventListener('loadend',()=>{
            let errorMsg=JSON.parse(reader.result)
            notification.error({message:errorMsg.message})
          })
          reader.readAsText(err,'UTF-8')
          return 
        }
          const file = new Blob([res], { type: 'application/zip' });
          const fileURL = URL.createObjectURL(file);
          const newwindow = window.open(fileURL, 'newwindow');
          if (newwindow) {
            newwindow.print();
          } else {
            notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
          }
      }
    })
    
  }

  handleFetchDefaultSite = () => {
    this.createMaterialDs.create({});
    return request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/lovs/sql/data`, {
      method: 'get',
      query: {
        lovCode: 'WMS.NOWORTH_SITE',
        tenantId: 0,
      },
    }).then(res => {
      if(res && res.content.length === 1) {
        this.createMaterialDs.current.set('siteLov', res.content[0]);
      }
    })
  };

  handleCreateMaterialLot = async () => {
    const flag = await this.createMaterialDs.validate();
    if(flag) {
      return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-in-and-out/creating-worthless-barcodes`, {
        method: 'POST',
        body: this.createMaterialDs.current.toData(),
      }).then(res => {
        if(res && res.success) {
          res.rows.forEach(e => {
            this.materialLotTableDs.create(e, 0);
          })
          notification.success();
        } else {
          notification.warning({ description: res.message });
        }
      })
    }
  };

  handlePrint = () => {
    if (this.materialLotTableDs.selected.length <= 0) {
      return notification.warning({ description: '请勾选生成的条码进行打印！'});
    }
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-in-and-out/print-worthless-barcodes`, {
      method: 'POST',
      responseType: 'blob',
      body: this.materialLotTableDs.selected.map(e => e.toData()),
    }).then(res => {
      if (res) {
        if (res.type === 'application/json') {
          const fileReader = new FileReader();
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result);
            // 普通对象，读取信息
            getResponse(jsonData);
          };
          fileReader.readAsText(res);
        } else {
          const file = new Blob([res], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(file);
          const newwindow = window.open(fileURL, 'newwindow');
          if (newwindow) {
            newwindow.print();
            notification.success({
              message: '打印成功',
            });
          } else {
            notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
          }
        }
      }
    })
  };

  handleCreate = () => {
    this.createMaterialDs.loadData([]);
    this.materialLotTableDs.loadData([]);
    this.handleFetchDefaultSite();
    this.createModal = Modal.open({
      key: Modal.key(),
      title: (
        <div>
          <span style={{ fontSize: '14px' }}>创建无价值标签</span>
          <div
            style={{
              float: 'right',
              display: 'flex',
              flexDirection: 'row-reverse',
              alignItems: 'center',
              marginRight: '0.3rem',
            }}
          >
            <Button icon="save" color="primary" onClick={this.handleCreateMaterialLot}>确定</Button>
          </div>
        </div>
      ),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      keyboardClosable: true,
      style: {
        width: 1200,
      },
      footer: null,
      className: 'hmes-style-modal',
      onClose: () => {
        this.createMaterialDs.loadData([]);
        this.materialLotTableDs.loadData([]);
      },
      children: (
        <>
          <Form dataSet={this.createMaterialDs} columns={4}>
            <Lov name="siteLov" />
            <TextField name="siteName" disabled />
            <Lov name="materialLov" />
            <TextField name="materialName" disabled />
            <Lov name="locatorLov" />
            <TextField name="locatorName" disabled />
            <NumberField name="weight" />
            <NumberField name="number" />
            <TextArea colSpan={2} name="remark" />
          </Form>
          <Collapse defaultActiveKey={['1']}>
            <Panel header="行表格" key="1">
              <Table
                dataSet={this.materialLotTableDs}
                columns={this.materialLotColumns}
                buttons={[
                  <Button onClick={this.handlePrint}>打印</Button>,
                ]}
              />
            </Panel>
          </Collapse>
        </>
      ),
    });
  };

  handleCancel = (modal) => {
    modal.close();
    this.headTableDs.query(this.headTableDs.currentPage);
  };

  handleCreateNewDocOrder = async (modal) => {
    const flag = await this.createNewOrderDs.validate();
    if(flag) {
      return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-in-and-out/create-save`, {
        method: 'POST',
        body: this.createNewOrderDs.current.toData(),
      }).then(res => {
        if(res && !res.failed) {
          this.createNewOrderDs.loadData([{
            ...res,
            disabled: true,
          }]);
          modal.update({
            title: (
              <div>
                <span style={{ fontSize: '14px' }}>新建</span>
                <div
                  style={{
                    float: 'right',
                    display: 'flex',
                    flexDirection: 'row-reverse',
                    alignItems: 'center',
                    marginRight: '0.3rem',
                  }}
                >
                  <Button icon="save" color="primary" disabled onClick={() => this.handleCreateNewDocOrder(this.createNewModal)}>保存</Button>
                </div>
                <div
                  style={{
                    float: 'right',
                    display: 'flex',
                    flexDirection: 'row-reverse',
                    alignItems: 'center',
                    marginRight: '0.2rem',
                  }}
                >
                  <Button icon="save" color="primary" onClick={() => this.handleCancel(this.createNewModal)}>取消</Button>
                </div>
              </div>
            ),
            children: (
              <>
                <Form dataSet={this.createNewOrderDs} columns={3}>
                  <TextField name="instructionDocNum" disabled />
                  <Select name="instructionDocType"  disabled/>
                  <Select name="instructionDocStatus" disabled />
                  <Lov name="siteLov" />
                  <Lov name="supplerLov" />
                  <TextField name="licensePlateNumber" />
                  <TextField name="outLot" disabled/>
                  <TextField name="disposeLot" disabled/>
                  <DateTimePicker name="outTime" disabled/>
                  <TextField name="contacts" />
                  <TextField name="contactInformation" />
                </Form>
              </>
            ),
          });
          notification.success();
        } else {
          notification.warning({ description: res.message });
        }
      })
    }
  };

  

  handleCreateNewOrder = () => {
    
    // this.createNewOrderDs.loadData([{}])
    const options=(this.headTableDs.fields?.get('instructionDocType')?.getOptions()?.data??[]).map(ele=>ele.toJSONData())
    const targtList=options.filter(ele=>ele.tag==='DISPOSE')
    this.createNewOrderDs.loadData([{
      instructionDocType: targtList.length===1?targtList[0].value:'',
      instructionDocStatus: 'RELEASED',
    }])
    this.createNewOrderDs.fields?.get('instructionDocType').set('options',new DataSet({
      data:targtList
    }))
    this.createNewModal = Modal.open({
      key: Modal.key(),
      title: (
        <div>
          <span style={{ fontSize: '14px' }}>新建</span>
          <div
            style={{
              float: 'right',
              display: 'flex',
              flexDirection: 'row-reverse',
              alignItems: 'center',
              marginRight: '0.3rem',
            }}
          >
            <Button icon="save" color="primary" onClick={() => this.handleCreateNewDocOrder(this.createNewModal)}>保存</Button>
          </div>
          <div
            style={{
              float: 'right',
              display: 'flex',
              flexDirection: 'row-reverse',
              alignItems: 'center',
              marginRight: '0.2rem',
            }}
          >
            <Button icon="save" color="primary" onClick={() => this.handleCancel(this.createNewModal)}>取消</Button>
          </div>
        </div>
      ),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      keyboardClosable: true,
      style: {
        width: 900,
      },
      footer: null,
      className: 'hmes-style-modal',
      onClose: () => {
        this.createNewOrderDs.loadData([]);
        this.headTableDs.query(this.headTableDs.currentPage);
      },
      children: (
        <>
          <Form dataSet={this.createNewOrderDs} columns={3}>
            <TextField name="instructionDocNum" disabled />
            <Select name="instructionDocType"  disabled={targtList.length<=1}/>
            <Select name="instructionDocStatus" disabled />
            <Lov name="siteLov" />
            <Lov name="supplerLov" />
            <TextField name="licensePlateNumber" />
            <TextField name="outLot" />
            <TextField name="disposeLot" />
            <DateTimePicker name="outTime" />
            <TextField name="contacts" />
            <TextField name="contactInformation" />
            <TextArea
              name="remark"
              newLine
              colSpan={3}
              autoSize={{ minRows: 2, maxRows: 8 }}
            />
          </Form>
        </>
      ),
    });
  };

  handlePrintOrder = () => {
    if (this.headTableDs.selected.length <= 0) {
      return notification.warning({ description: '请勾选无价值处置单进行打印！'});
    }
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-in-and-out/print-pdf`, {
      method: 'POST',
      responseType: 'blob',
      body: this.headTableDs.selected.map(e => e.toData().instructionDocId),
    }).then(res => {
      if (res) {
        if (res.type === 'application/json') {
          const fileReader = new FileReader();
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result);
            // 普通对象，读取信息
            getResponse(jsonData);
          };
          fileReader.readAsText(res);
        } else {
          const file = new Blob([res], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(file);
          const newwindow = window.open(fileURL, 'newwindow');
          if (newwindow) {
            newwindow.print();
            notification.success({
              message: '打印成功',
            });
          } else {
            notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
          }
        }
      }
    })
  };



  render() {
    const {cancelDisabled,exportDisabled,excelExportDisabled}=this.state
    const headColumns = [
      { name: 'siteCode', align: 'center' },
      { name: 'instructionDocNum', align: 'center' },
      { name: 'instructionDocType', align: 'center' },
      { name: 'instructionDocStatusDesc', align: 'center' },
      { name: 'supplierCode', align: 'center' },
      { name: 'licensePlateNumber', align: 'center' },
      { name: 'weight', align: 'center' },
      { name: 'tare', align: 'center' },
      { name: 'netWeight', align: 'center' },
      { name: 'containerWeight', align: 'center' },
      { name: 'repeatDifferRate', align: 'center' },
      { name: 'uuid', align: 'center' },
      { name: 'remark', width: 220 },
      { name: 'realName', align: 'center' },
      { name: 'creationDate', align: 'center' },
    ];
    const lineColumns = [
      { name: 'lineNumber', align: 'center' },
      { name: 'lineStatusDesc', align: 'center' },
      { name: 'materialCode', align: 'center' },
      { name: 'materialName', align: 'center' },
      { name: 'quantity', align: 'center' },
      { name: 'uomCode', align: 'center' },
      { name: 'sumActualQty', align: 'center' },
      { name: 'fromLocatorCode', align: 'center' },
      { name: 'toLocatorCode', align: 'center' },
      {
        name: 'option',
        fixed: 'right',
        lock: 'right',
        width: 120,
        align: 'center',
        renderer: ({ record }) => this.optionRender(record),
      },
    ];

    
    return (
      <Fragment>
        <Header title="无价值出入库管理平台">
          <ExcelExport
            method="GET"
            // exportAsync
            requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-worthless-in-and-out/export/ui`}
            queryParams={this.getExportQueryParams}
            buttonText='导出'
            // otherButtonProps={{
            //   disabled: excelExportDisabled,
            // }}
          />
          {/* <Button icon="add" color="primary" onClick={this.handleCreate}>创建无价值标签</Button> */}
          <Dropdown
          overlay={
            <Menu onClick={this.cancelData}>
              <Menu.Item key="cancel" disabled={cancelDisabled}>取消</Menu.Item>
            </Menu>
          }
          trigger={['click']}
          disabled={cancelDisabled}
          >
            <Button disabled={cancelDisabled} icon={"cached"}>状态变更</Button>
          </Dropdown>
          <Button icon="add" color="primary" onClick={this.handleCreateNewOrder}>新建</Button>
          <Button icon="print" color="primary" onClick={this.handlePrintOrder}>打印</Button>
          <Button color="primary" disabled={exportDisabled} onClick={this.exportExcel}>记录表导出</Button>
        </Header>
        <Content>
          <Table
            dataSet={this.headTableDs}
            columns={headColumns}
            queryBar="filterBar"
            queryFieldsLimit={10}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            searchCode="headInOutBoundManagementPlatform"
            customizedCode="headInOutBoundManagementPlatform"
            onRow={({ record }) => {
              return {
                onClick: () => {
                  this.headerRowClick(record);
                },
              };
            }}
            pagination={{
              showPager: true, // 显示数字按钮
              pageSizeOptions: ['10', '20', '50', '100', '200', '500', '1000', '5000'],
            }}
          />
          <Collapse defaultActiveKey={['1']}>
            <Panel header="行表格" key="1">
              <Table
                dataSet={this.lineTableDs}
                columns={lineColumns}
                customizedCode="lineInOutBoundManagementPlatform"
                pagination={{
                  showPager: true, // 显示数字按钮
                  pageSizeOptions: ['10', '20', '50', '100', '200', '500', '1000', '5000'],
                }}
              />
            </Panel>
          </Collapse>
        </Content>
      </Fragment>
    );
  }
}
