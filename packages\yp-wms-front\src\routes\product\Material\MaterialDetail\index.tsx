/**
 * @Description: 物料维护-详情页
 * @Author: <<EMAIL>>
 * @Date: 2022-07-25 15:56:22
 * @LastEditTime: 2023-05-18 11:38:22
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  Button,
  DataSet,
  Form,
  IntlField,
  Lov,
  NumberField,
  Switch,
  TextField,
  Modal,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { AttributeDrawer, drawer<PERSON>rops<PERSON>7n, Tarzan<PERSON><PERSON> } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import CopyDrawer from './CopyDrawer';
import { detailDS, copyDS } from '../stories';
import { SaveMaterial, CheckMaterial } from '../services';

// const TABLENAME = 'mt_material_attr';
const modelPrompt = 'tarzan.product.materialManager.model.materialManager';
const { Panel } = Collapse;

const MaterialDetail = (props) => {
  const {
    history,
    match: { path, params },
    customizeForm,
    custConfig,
  } = props;
  const { id: materialId } = params;

  const copyModal = useRef<null | Modal>(null);

  const [canEdit, setCanEdit] = useState(false);
  const [overrideMaterialId, setOverrideMaterialId] = useState<null | number | 'noRepeat'>(null)
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const copyDs = useMemo(() => new DataSet(copyDS()), []);
  const { run: saveMaterial, loading: saveLoading } = useRequest(SaveMaterial(), { manual: true });
  const { run: checkMaterial } = useRequest(CheckMaterial(), { manual: true, needPromise: true });

  useEffect(() => {
    if (materialId === 'copyStatus') {
      setCanEdit(true);
      // 复制时，不做其它操作
      if (!overrideMaterialId) {
        // 如果在复制页面做了刷新，那么跳转到新建页面
        history.push('/hmes/product/material-manager/dist/create')
        return;
      }
      return;
    }
    if (materialId === 'create') {
      // 新建时
      setCanEdit(true);
      return;
    }
    // 编辑时
    detailDs.setQueryParameter('materialId', materialId);
    detailDs.query();
  }, [materialId]);

  const handleEdit = () => {
    setCanEdit(true);
  }

  const handleCancel = () => {
    if (materialId === 'create' || materialId === 'copyStatus') {
      history.push('/hmes/product/material-manager/list')
    } else {
      setCanEdit(false);
      detailDs.query();
    }
  }

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    const queryParams = detailDs!.current!.toData();
    if (overrideMaterialId && overrideMaterialId !== 'noRepeat') {
      queryParams.materialId = overrideMaterialId;
      setOverrideMaterialId(null);
    }
    if (validateFlag) {
      saveMaterial({
        params: {
          ...queryParams,
        },
        onSuccess: (res) => {
          notification.success({});
          setCanEdit(false);
          history.push(`/hmes/product/material-manager/dist/${res}`);
        },
      })
    }
  }

  const handleCopy = () => {
    const { materialCode, materialName } = detailDs!.current!.toData();
    copyDs.loadData([{
      sourceMaterialCode: materialCode,
      sourceMaterialDesc: materialName,
    }]);
    copyModal.current = Modal.open({
      ...drawerPropsC7n({ ds: copyDs }),
      key: Modal.key(),
      title: intl.get('tarzan.common.button.copy').d('复制'),
      style: {
        width: 360,
      },
      children: <CopyDrawer ds={copyDs} />,
      onOk: handleCopyConfirm,
    });
  }

  const handleCopyConfirm = async () => {
    const validateFlag = await copyDs!.current!.validate(true);
    const targetMaterialCode = copyDs!.current!.get('targetMaterialCode');
    if (!validateFlag) {
      // 校验不通过，不关闭抽屉
      return false;
    }
    return checkMaterial({
      params: {
        materialCode: targetMaterialCode,
      },
    }).then(async res => {
      if (!res || !res.success) {
        return Promise.resolve(false);
      }
      if (res.rows && res.rows.materialId) {
        let closeFlag = false;
        // 已有该物料，要做覆盖
        await Modal.confirm({
          title: intl.get(`${modelPrompt}.confirm.hasAlready`).d('物料编码已经存在,是否覆盖?'),
          okText: intl.get('tarzan.common.button.confirm').d('确认'),
          cancelText: intl.get('tarzan.common.button.cancel').d('取消'),
          onOk() {
            closeFlag = true;
            setOverrideMaterialId(res.rows.materialId);
            detailDs!.current!.set('materialCode', targetMaterialCode);
            history.push('/hmes/product/material-manager/dist/copyStatus');
          },
          onCancel() { },
        });
        if (!closeFlag) {
          return Promise.resolve(false);
        }
      } else {
        // 预防复制后再复制，第二次没有重复物料，但是覆盖到了第一次使用的物料上
        setOverrideMaterialId('noRepeat');
        detailDs!.current!.set('materialCode', targetMaterialCode);
        detailDs!.current!.set('materialId', '');
        history.push('/hmes/product/material-manager/dist/copyStatus');
      }
    })
  }

  const siteAssignment = () => {
    history.push(`/hmes/product/material-manager/site-assignment/${materialId}`);
  }

  return (
    <div className='hmes-style'>
      <TarzanSpin dataSet={detailDs} spinning={saveLoading}>
        <Header
          title={intl.get('tarzan.product.materialManager.title.list').d('物料维护')}
          backPath='/hmes/product/material-manager/list'
        >
          {canEdit ? (
            <>
              <Button
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
                loading={saveLoading}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button
                icon="close"
                onClick={handleCancel}
              >
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
          <Button
            disabled={materialId === 'create'}
            icon="api"
            onClick={siteAssignment}
          >
            {intl.get(`${modelPrompt}.setSites`).d('站点分配')}
          </Button>
          <PermissionButton
            type="c7n-pro"
            icon="content_copy-o"
            onClick={handleCopy}
            disabled={materialId === 'create'}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.copy').d('复制')}
          </PermissionButton>
          <AttributeDrawer
            disabled={materialId === 'create'}
            // tablename={TABLENAME}
            className="org.tarzan.method.domain.entity.MtMaterial"
            kid={materialId}
            canEdit={canEdit}
            serverCode={BASIC.TARZAN_METHOD}
            custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DETAIL.BUTTON`}
            custConfig={custConfig}
          />
        </Header>
        <Content>
          <Collapse bordered={false} defaultActiveKey={['basic', 'rule']}>
            <Panel
              key="basic"
              header={intl.get('tarzan.product.materialManager.title.basic').d('基础属性')}
              dataSet={detailDs}
            >
              {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DETAIL.BASIC`,
                },

                <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                  <TextField name="materialCode" />
                  <IntlField
                    name="materialName"
                    modalProps={{ title: intl.get(`${modelPrompt}.materialName`).d('物料描述') }}
                  />
                  <TextField name="longDescription" />
                  <TextField name="materialDesignCode" />
                  <TextField name="materialIdentifyCode" />
                  <TextField name="oldItemCode" />
                  <TextField name="model" />
                  <Lov noCache name="primaryUomLov" />
                  <Lov noCache name="secondaryUomLov" />
                  <NumberField name="conversionRate" />
                  <Lov name="responEmployeeLov" />
                  <Switch name="enableFlag" />
                </Form>,
              )}
            </Panel>
            <Panel
              header={intl.get('tarzan.product.materialManager.title.rule').d('规格尺寸')}
              key="rule"
              dataSet={detailDs}
            >
              <Form dataSet={detailDs} columns={4} disabled={!canEdit} labelWidth={90}>
                <Lov noCache name="sizeUomLov" />
                <NumberField name="length" />
                <NumberField name="width" />
                <NumberField name="height" />
                <Lov noCache name="volumeUomLov" />
                <NumberField name="volume" />
                <Lov noCache name="weightUomLov" />
                <NumberField name="weight" />
              </Form>
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  )
};

export default formatterCollections({
  code: ['tarzan.product.materialManager', 'tarzan.common'],
})(withCustomize({
  unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DETAIL.BASIC`, `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DETAIL.BUTTON`],
  // @ts-ignore
})(MaterialDetail));
