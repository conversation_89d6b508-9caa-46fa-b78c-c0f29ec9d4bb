/**
 * @Description: 跳批拣配记录报表 - 列表页
 * @Author: <EMAIL>
 * @Date: 2022/7/6 15:40
 * @LastEditTime: 2022-11-21 13:37:41
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import ExcelExport from 'components/ExcelExport';
import { Content, Header } from 'components/Page';
import { TableQueryBarType, TableMode } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { BASIC, API_HOST } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { isNil } from 'lodash';
import { entranceDS } from '../stores/EntranceDS';

const modelPrompt = 'tarzan.wms.AutomaticShelvingConfiguration';
const tenantId = getCurrentOrganizationId();
const Query = props => {
  const {
    dataSet,
  } = props;

  useEffect(() => {
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    if (Object.keys(props?.location?.query).length === 0 || props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      // dataSet.query(dataSet.currentPage);
      return;
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const {
      siteId,
      siteCode,
      materialId,
      materialCode,
      revisionCode,
      lotCode,
      qualityStatus,
      ownerType,
      ownerId,
      ownerCode,
      locatorId,
      locatorCode,
    } = props?.location?.query || {};
    const queryParams = {
      siteLov: siteId ? { siteId, siteCode } : undefined, // 回显站点
      materialLov: materialId ? { materialId, materialCode } : undefined,
      revisionCodes: revisionCode && revisionCode.length ? [revisionCode] : undefined,
      lotCodes: lotCode ? [lotCode] : undefined,
      qualityStatus,
      ownerType,
      ownerLov: ownerId
        ? {
          soLineId: ownerId,
          customerId: ownerId,
          soNumContent: ownerCode,
          supplierCode: ownerCode,
          customerCode: ownerCode,
        } : undefined,
      locatorLov: locatorId ? { locatorId, locatorCode } : null,
    };
    // setExportFlag(siteId || null);
    setTimeout(() => {
      dataSet.queryDataSet.loadData([queryParams]);
      dataSet.query();
    }, 200);
  }, [props?.location?.query, props?.location?.state]);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (dataSet) {
      const handerQuery = flag
        ? dataSet.queryDataSet.addEventListener
        : dataSet.queryDataSet.removeEventListener;
      // 查询条件更新时操作
      handerQuery.call(dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ name, record }) => {
    if (name === 'ownerType') {
      record.set('ownerLov', null);
    }
    if (name === 'siteLov') {
      record.set('locatorLov', null);
      record.set('materialLov', null);
    }
  };

  const columns: ColumnProps[] = [
    { name: 'creationDate', width: 160 },
    { name: 'instructionDocNum', width: 160 },
    { name: 'materialCode', width: 160 },
    { name: 'materialName', width: 160 },
    { name: 'supplierCode', width: 160 },
    { name: 'supplierName', width: 160 },
    { name: 'lot', width: 160 },
    { name: 'jumpLot', width: 160 },
    { name: 'materialLotCode', width: 160 },
    { name: 'name', width: 160 },
    { name: 'lastUpdateDate', width: 160 },
    { name: 'reason', width: 160 },
  ];

  const getExportQueryParams = () => {
    if (dataSet.selected.length > 0) {
      const queryParams = {
        recordIds: dataSet.selected.map((item) => item.toData().recordId).join(','),
      }
      return queryParams;
    }

    if (!dataSet.queryDataSet || !dataSet.queryDataSet.current) {
      return {};
    }

    const queryParams = {
      ...dataSet.queryDataSet.current.toData(),
    }
    Object.keys(queryParams).forEach((i) => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });

    return {
      ...queryParams,
    };


  };
  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.inventory.query.view.title.level').d('跳批拣配记录报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-jump-lot-record/list/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          searchCode="kccx1"
          customizedCode="kccx1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: true,
          }}
          mode={TableMode.tree}
          // treeAsync
          dataSet={dataSet}
          columns={columns}
          highLightRow
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.inventory.query', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...entranceDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Query),
);
