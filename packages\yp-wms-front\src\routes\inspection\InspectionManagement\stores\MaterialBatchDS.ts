import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.inspection.inspection-management';
const tenantId = getCurrentOrganizationId();

const headDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  paging: true,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inspect-requests/dtl/list/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'objectType',
    },
    {
      name: 'objectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.objectType`).d('报检对象类型'),
    },
    {
      name: 'objectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestCode`).d('报检对象编码'),
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.count`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('场内批次'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'okQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.okQty`).d('合格数'),
    },
    {
      name: 'ngQuantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngQty`).d('不合格数'),
    },
  ],
});

const tableDS = (): DataSetProps => {
  return {
    autoQuery: false,
    autoCreate: false,
    cacheSelection: true,
    autoLocateFirst: false,
    paging: false,
    forceValidate: true,
    selection: false,
    primaryKey: 'inspectRequestDisposalId',
    fields: [
      {
        name: 'qualityStatusDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
      },
      {
        name: 'quantity',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.count`).d('数量'),
      },
      {
        name: 'uomName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.uomName`).d('单位'),
      },
      {
        name: 'disposalFunctionDesc',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.disposalFunction`).d('处置方法'),
      },
      {
        name: 'disposalUserName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.disposalUserName`).d('处置意见给出人'),
      },
      {
        name: 'disposalTime',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.disposalTime`).d('处置意见给出时间'),
      },      {
        name: 'disposalApartment',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.disposalApartment`).d('人员所属部门'),
      },      {
        name: 'degradeLevel',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.degradeLevel`).d('降级等级'),
      },      {
        name: 'degradeMaterialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.degradeMaterialName`).d('降级物料'),
      },
      {
        name: 'degradeRevisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.degradeRevisionCode`).d('降级物料版本'),
      },      {
        name: 'disposalExecuteUserName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.disposalExecuteUserName`).d('处置执行人'),
      },      {
        name: 'disposalExecuteTime',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.disposalExecuteTime`).d('处置执行时间'),
      },
      {
        name: 'disposalExecuteDocNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.disposalExecuteDocNum`).d('处置单据'),
      },
      {
        name: 'finalExecuteObjectCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.finalExecuteObjectCode`).d('最终执行物料批'),
      },
      {
        name: 'remark',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
    ],
  };
};

export { headDS, tableDS };
