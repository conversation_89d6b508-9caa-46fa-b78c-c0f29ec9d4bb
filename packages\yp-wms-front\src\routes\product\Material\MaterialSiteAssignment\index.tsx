/**
 * @Description: 物料维护-站点分配
 * @Author: <<EMAIL>>
 * @Date: 2022-07-28 14:39:10
 * @LastEditTime: 2022-10-31 15:25:45
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Table, DataSet, Switch, Button, Spin, Lov } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Badge, Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { Header, Content } from 'components/Page';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import { useDataSetEvent } from 'utils/hooks';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import { tableDS } from '../stories/MaterialSiteAssignmentDS';
import { SaveMaterialSiteAssignment } from '../services';

const modelPrompt = 'tarzan.product.materialManager.model.materialManager';

const MaterialSiteAssignment = props => {
  const {
    history,
    match: { path, params },
  } = props;
  const { id: materialId } = params;

  const [canEdit, setCanEdit] = useState(false);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const { run: runSave, loading: saveLoading } = useRequest(SaveMaterialSiteAssignment(), { manual: true });

  useEffect(() => {
    tableDs.setQueryParameter('materialId', materialId);
    tableDs.query();
  }, [materialId]);

  useDataSetEvent(tableDs, 'load', (data) => {
    autoControlSiteLovQueryParams(data);
  });

  useDataSetEvent(tableDs, 'remove', (data) => {
    autoControlSiteLovQueryParams(data);
  });

  useDataSetEvent(tableDs, 'reset', (data) => {
    autoControlSiteLovQueryParams(data);
  });

  useDataSetEvent(tableDs, 'update', (data) => {
    if (data.name === 'siteLov') {
      autoControlSiteLovQueryParams(data);
    }
  });

  const autoControlSiteLovQueryParams = ({ dataSet }) => {
    // 已选的站点，不可再次选择
    const tableDataList = dataSet.toData();
    const selectedSiteIdsList: any[] = [];
    tableDataList.forEach((item) => {
      if (item.siteId) {
        selectedSiteIdsList.push(item.siteId);
      }
    });
    dataSet.getField('siteLov').setLovPara('siteIds', selectedSiteIdsList.join(','));
  }

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        header: () => (
          <PermissionButton
            type='c7n-pro'
            icon='add'
            disabled={!canEdit}
            onClick={handleAddLine}
            funcType='flat'
            shape='circle'
            size='small'
          />
        ),
        align: ColumnAlign.center,
        width: 80,
        lock: ColumnLock.left,
        renderer: ({ record }) => !record?.get('materialSiteId') && (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              handleDelLine(record);
            }}
          >
            <PermissionButton
              type='c7n-pro'
              icon='remove'
              disabled={!canEdit}
              funcType='flat'
              shape='circle'
              size='small'
            />
          </Popconfirm>
        ),
      },
      {
        name: 'siteLov',
        width: 240,
        editor: record => !record.get('materialSiteId') && canEdit && <Lov name="siteLov" />,
        renderer: ({ record }) => {
          return record!.get('siteCode');
        },
      },
      {
        name: 'siteName',
      },
      {
        name: 'typeDesc',
        width: 180,
      },
      {
        name: 'revisionFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
        editor: () => canEdit && <Switch name="revisionFlag" />,
      },
      {
        name: 'productionVersionFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
        editor: () => canEdit && <Switch name="productionVersionFlag" />,
      },
      {
        name: 'enableFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
        editor: () => canEdit && <Switch name="enableFlag" />,
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        width: 180,
        renderer: ({ record }) => {
          return (
            <span className="action-link">
              <a
                disabled={!record!.get('materialSiteId')}
                onClick={() => {
                  const materialSiteId = record!.get('materialSiteId');
                  history.push(`/hmes/product/material-site-properties/detail/${materialId}/${materialSiteId}`);
                }}
              >
                {intl.get(`${modelPrompt}.materialSiteAttr`).d('物料站点属性维护')}
              </a>
            </span>
          );
        },
      },
    ];
  }, [canEdit]);

  const handleAddLine = useCallback(() => {
    tableDs.create({
      enableFlag: 'Y',
      revisionFlag: 'N',
      productionVersionFlag: 'N',
    }, 0);
  }, [tableDs]);

  const handleDelLine = useCallback((record) => {
    tableDs.remove(record);
  }, [tableDs]);

  const handleEdit = () => {
    setCanEdit(true);
  };

  const handleCancel = useCallback(
    () => {
      tableDs.reset();
      setCanEdit(false);
    },
    [tableDs],
  );

  const handleSave = async () => {
    const validateFlag = await tableDs.validate();
    if (!validateFlag) {
      return;
    }
    runSave({
      params: {
        materialId,
        materialSiteList: tableDs.toData(),
      },
      onSuccess: () => {
        notification.success({});
        setCanEdit(false);
        tableDs.query();
      },
    });
  };

  return (
    <div className="hmes-style">
      <Spin spinning={saveLoading}>
        <Header
          title={intl.get(`${modelPrompt}.setSites`).d('站点分配')}
          backPath={`/hmes/product/material-manager/dist/${materialId}`}
        >
          {canEdit ? (
            <>
              <Button
                type={ButtonType.submit}
                icon="save"
                onClick={handleSave}
                loading={saveLoading}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button
                icon="close"
                onClick={handleCancel}
              >
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '物料维护-站点分配页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
        </Header>
        <Content>
          <Table
            dataSet={tableDs}
            columns={columns}
            pagination={false}
          />
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.product.materialManager', 'tarzan.common'],
})(MaterialSiteAssignment);