/**
 * @Description: 事务明细报表-主表格-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-25 14:54:49
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.wms.event.inventorySnapshotReport';
const tenantId = getCurrentOrganizationId();

export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'warehouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
      lovCode: 'WMS.INVENTORY_DETAILS_LOCATOR',
      multiple: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            // siteIds: [record.get('siteId')],
            type: 'LOCATOR',
            queryLocatorCategoryList: ['AREA'],
            locatorCategoryList: ['AREA', 'INVENTORY'],
          };
        },
        // disabled: ({ record }) => {
        //   return !record.get('siteId');
        // },
      },
    },
    {
      name: 'warehouseIds',
      bind: 'warehouseLov.warehouseId',
    },
    {
      name: 'reservoirAreaLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reservoirAreaCode`).d('库区编码'),
      lovCode: 'WMS.RESERVOIR_AREA',
      multiple: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            // siteId: record.get('siteId'),
            enableFlag: 'Y',
          };
        },
        // disabled: ({ record }) => {
        //   return !record.get('siteId');
        // },
      },
    },
    {
      name: 'reservoirAreaIds',
      bind: 'reservoirAreaLov.reservoirAreaId',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            // siteId: record.get('siteId'),
            warehouseIds: record.get('warehouseIds'),
          };
        },
      },
    },
    {
      name: 'locatorIds',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      // lovCode: 'MT.METHOD.MATERIAL',
      lovCode: 'MT.MATERIAL',
      multiple: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      // lovCode: 'MT.METHOD.MATERIAL',
      lovCode: 'MT.MODEL.SUPPLIER',
      multiple: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierIds',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'onhandLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.snapTime`).d('快照时间'),
      // lovCode: 'MT.METHOD.MATERIAL',
      lovCode: 'WMS_MLT_ONHAND_INDEX',
      required: true,
      multiple: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'batchIds',
      bind: 'onhandLov.batchId',
    },
  ],
  fields: [
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库'),
    },
    {
      name: 'reservoirAreaCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservoirAreaCode`).d('库区'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'qualityStatusMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusMeaning`).d('质量状态'),
    },
    {
      name: 'onhandQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.onhandQty`).d('库存数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('快照时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-mlt-onhands/list/get/ui`,
        method: 'GET',
      };
    },
  },
});
