import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, } from 'utils/utils';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.ass.demandManage';

const listLineFactory = () =>
  new DataSet({
    primaryKey: 'instructionDocId',
    selection: DataSetSelection.multiple,
    paging: true,
    autoQuery: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    record: {
      dynamicProps: {
        selectable: record =>['RELEASED','PROCESSING'].includes(record.get('instructionDocStatus'))  ,
      },
    },
    fields: [
      {
        name: 'lineNumber',
        label: intl.get(`${modelPrompt}.form.num`).d('序号'),
        type: FieldType.string,
      },
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
        type: FieldType.string,
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
      },
      {
        name: 'quantity',
        label: intl.get(`${modelPrompt}.form.quantity`).d('需求数量'),
        type: FieldType.string,
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.uomCode`).d('单位'),
      },
      {
        name: 'lineStatus',
        type: FieldType.string,
        lookupCode: 'ASS.INSTRUCTION_DOC_STATUS',
        label: intl.get(`${modelPrompt}.form.lineStatus`).d('需求行状态'),
      },
      {
        name: 'lastUpdateDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.form.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-demand-manage-platform/line/list`,
        };
      },
    },
  });

export default listLineFactory;
