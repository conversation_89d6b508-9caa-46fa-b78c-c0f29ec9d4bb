/**
 * @Description: 物料维护-复制抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-07-27 15:47:25
 * @LastEditTime: 2022-07-27 16:30:43
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Form, TextField } from 'choerodon-ui/pro';

export default ({ ds }) => {
  return (
    <Form labelWidth={112} dataSet={ds} columns={1}>
      <TextField name="sourceMaterialCode" disabled />
      <TextField name="sourceMaterialDesc" disabled />
      <TextField name="targetMaterialCode" />
    </Form>
  );
};