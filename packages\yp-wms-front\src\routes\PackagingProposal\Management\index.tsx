/**
 * @Description: 销售发运平台 - 入口页面
 * @Author: <EMAIL>
 * @Date: 2022/2/10 15:08
 */
import React, { useEffect, useState } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
// import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import request from 'utils/request';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { tableDS } from '../stores/ListDs';

const modelPrompt = 'hwms.packageProposalManagement';
const tenantId = getCurrentOrganizationId();

const packageProposalManagement = props => {
  const {
    tableDs,
    match: { path },
    customizeTable,
    history,
  } = props;

  const [rejectDisabled, setRejectDisabled] = useState(true);
  const [approvalDisabled, setApprovalDisabled] = useState(true);
  const [cancelDisabled, setCancelDisabled] = useState(true);

  const rejectRequest = useRequest({
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-package-proposals/wmsProposalRejection`,
    method: 'POST',
  }, {
    manual: true,
  });


  const cancelRequest = useRequest({
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-package-proposals/wmsProposalCancel`,
    method: 'POST',
  }, {
    manual: true,
  });

  const approvalRequest = useRequest({
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-package-proposals/wmsProposalApproval`,
    method: 'POST',
  }, {
    manual: true,
  });

  useEffect(() => {
    tableDs.addEventListener('batchSelect', handleSelectTable);
    tableDs.addEventListener('batchUnSelect', handleSelectTable);
    return () => {
      tableDs.removeEventListener('batchSelect', handleSelectTable);
      tableDs.removeEventListener('batchUnSelect', handleSelectTable);
    };
  }, []);

  const handleSelectTable = () => {
    const statusList = tableDs.queryDataSet.getField('status').options.props.data;
    setRejectDisabled(!(tableDs.selected.length === 1 && statusList?.find(e => e.value === tableDs.selected[0]?.get('status'))?.tag?.includes('D')));
    setApprovalDisabled(!(tableDs.selected.length === 1 && statusList?.find(e => e.value === tableDs.selected[0]?.get('status'))?.tag?.includes('A')));
    setCancelDisabled(!(statusList?.find(e => e.value === tableDs.selected[0]?.get('status'))?.tag?.includes('C')));
  };

  const handleToDetail = (record) => {
    history.push(`/hwms/packaging-proposal/management/detail/${record.get('packageProposalId')}`);
  };

  const columns: ColumnProps[] = [
    { name: 'number', align: ColumnAlign.left },
    {
      name: 'statusDesc',
      lock: ColumnLock.left,
      width: 120,
    },
    { name: 'supplierCode' },
    { name: 'supplierName' },
    { name: 'materialCode', width: 150 },
    { name: 'materialName', align: ColumnAlign.right },
    { name: 'packageTypeDesc' },
    { name: 'creationDate' },
    { name: 'createByName' },
    { name: 'lastUpdateDate', width: 150 },
    { name: 'lastUpdateByName' },
    {
      header: intl.get(`${modelPrompt}.column.operation`).d('操作列'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      width: 260,
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => handleToDetail(record)}
            >
              {intl.get(`${modelPrompt}.create.materialBatch`).d('提案详细内容')}
            </a>
          </span>
        );
      },
    },
  ];

  const handleToCreatePage = () => {
    history.push(`/hwms/packaging-proposal/management/detail/create`);
  };

  const handleRejectProposal = () => {
    return rejectRequest.run({
      params: tableDs.selected.map(e => e?.get('packageProposalId')),
      onSuccess: () => {
        notification.success();
        tableDs.query();
      },
    });
  };

  const handleApprovalProposal = () => {
    return approvalRequest.run({
      params: tableDs.selected.map(e => e?.get('packageProposalId')),
      onSuccess: () => {
        notification.success();
        tableDs.query();
      },
    });
  };

  const handleCancelProposal = () => {
    return cancelRequest.run({
      params: tableDs.selected.map(e => e?.get('packageProposalId')),
      onSuccess: () => {
        notification.success();
        tableDs.query();
      },
    });
  };

  const handleExport = async () => {
    const packageProposalIdList = tableDs.selected.map(item => item.get('packageProposalId'));
    // 请求后台
    const result = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-package-proposals/export`, {
      method: 'POST',
      responseType: 'blob',
      body: {
        packageProposalIdList,
        ...tableDs.queryDataSet?.current?.toData(),
      },
    });
    const res = getResponse(result);
    if (res) {
      const file = new Blob([res], { type: 'application/vnd.ms-excel' });
      const fileURL = URL.createObjectURL(file);
      const fileName = '包装提案导出.xls';
      const elink = document.createElement('a');
      elink.download = fileName;
      elink.style.display = 'none';
      elink.href = fileURL;
      document.body.appendChild(elink);
      elink.click();
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink);
    }
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.packageProposalManagement`).d('包装提案管理')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => handleToCreatePage()}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.create`).d('新建包装提案')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="close-o"
          onClick={() => handleRejectProposal()}
          disabled={rejectDisabled}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.changeStatus`).d('提案驳回')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="send-o"
          onClick={() => handleApprovalProposal()}
          disabled={approvalDisabled}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.changeStatus`).d('会签审批')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="cancel"
          onClick={() => handleCancelProposal()}
          disabled={cancelDisabled}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.changeStatus`).d('提案取消')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="file_download_black-o"
          onClick={() => handleExport()}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.export`).d('导出')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.PACKAGING_PROPOSAL_MANAGEMENT_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.PACKAGING_PROPOSAL_MANAGEMENT_LIST.HEAD`,
          },
          <Table
            searchCode="xsfypt1"
            customizedCode="xsfypt1"
            dataSet={tableDs}
            columns={columns}
            highLightRow
            showCachedSelection={false}
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
            }}
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['hwms.packageProposalManagement', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet(tableDS('/wms-package-proposals/query/head/ui'));
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.PACKAGING_PROPOSAL_MANAGEMENT_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.PACKAGING_PROPOSAL_MANAGEMENT_LIST.HEAD`,
    ],
  }),
)(packageProposalManagement);
