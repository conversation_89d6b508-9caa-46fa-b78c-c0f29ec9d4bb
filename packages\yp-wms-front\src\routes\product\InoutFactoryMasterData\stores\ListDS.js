import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

// const Host = `/yp-wms-33139`
const Host = `${BASIC.HMES_BASIC}`;

const modelPrompt = 'tarzan.wms.InoutFactoryMasterData';
const tenantId = getCurrentOrganizationId();

const headerTableDS = () => ({
  autoQuery: true,
  pageSize: 10,
  selection: 'multiple',
  transport: {
    read: ({ data }) => {
      return {
        url: `${Host}/v1/${tenantId}/wms-in-out-plant-datas/list`,
        method: 'GET',
        data: {
          ...data,
        },
      };
    },
  },
  queryFields: [
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'toLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.targetWarehouse`).d('目标仓库'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        locatorCategory: ['AREA'],
      },
    },
    {
      name: 'locatorId',
      type: FieldType.string,
      bind: 'toLocatorLov.locatorId',
    },
    {
      name: 'pullType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.pullType`).d('拉动类型'),
      textField: 'meaning',
      valueField: 'value',
      lookupCode: 'WMS.PULL_TYPE',
    },
    {
      name: 'sourceSupplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('来源供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierId',
      type: FieldType.number,
      bind: 'sourceSupplierLov.supplierId',
    },
  ],
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: 'always',
      noCache: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: 'always',
      textField: 'materialCode',
      valueField: 'materialId',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      bind: 'materialLov.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      bind: 'materialLov.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'toLocatorCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.toLocatorCode`).d('目标仓库'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      textField: 'locatorCode',
      valueField: 'locatorId',
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId') || null,
            locatorCategory: ['AREA'],
          };
        },
      },
      required: true,
    },
    {
      name: 'toLocatorId',
      type: FieldType.string,
      bind: 'toLocatorCodeLov.locatorId',
    },
    {
      name: 'toLocatorCode',
      type: FieldType.string,
      bind: 'toLocatorCodeLov.locatorCode',
    },

    {
      name: 'category',
      type: FieldType.string,
      lookupCode: 'WMS.CATEGORY',
      textField: 'meaning',
      valueField: 'value',
      required: true,
      label: intl.get(`${modelPrompt}.category`).d('类别'),
    },
    {
      name: 'pullType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.pullType`).d('拉动类型'),
      textField: 'meaning',
      valueField: 'value',
      required: true,
      lookupCode: 'WMS.PULL_TYPE',
    },
    {
      name: 'fromLocatorCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromLocatorCode`).d('来源仓库'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      multiple: false,
      ignore: 'always',
      textField: 'locatorCode',
      valueField: 'locatorId',
      lovPara: {
        tenantId,
        locatorCategory: ['AREA'],
      },
      dynamicProps: {
        required: ({ record }) => {
          return (
            record?.get('pullType') === 'IN_PLANT' && record?.get('category') === 'SPARE_PARTS'
          );
        },
        disabled: ({ record }) => {
          return (
            record?.get('category') === 'MATERIALS_DEPARTMENT' ||
            (record?.get('pullType') === 'OUT_PLANT' &&
              record?.get('category') === 'SPARE_PARTS' &&
              record?.get('supplierId'))
          );
        },
      },
    },
    {
      name: 'fromLocatorId',
      type: FieldType.number,
      bind: 'fromLocatorCodeLov.locatorId',
    },
    {
      name: 'fromLocatorCode',
      type: FieldType.string,
      bind: 'fromLocatorCodeLov.locatorCode',
    },
    {
      name: 'sourceSupplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('来源供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      noCache: true,
      textField: 'supplierCode',
      valueField: 'supplierId',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            (record?.get('pullType') === 'IN_PLANT' && record?.get('category') === 'SPARE_PARTS') ||
            (record?.get('pullType') === 'OUT_PLANT' &&
              record?.get('category') === 'SPARE_PARTS' &&
              record?.get('fromLocatorId'))
          );
        },
        required: ({ record }) => {
          return record?.get('category') === 'MATERIALS_DEPARTMENT';
        },
      },
    },
    {
      name: 'supplierId',
      type: FieldType.number,
      bind: 'sourceSupplierLov.supplierId',
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      bind: 'sourceSupplierLov.supplierCode',
    },
    {
      name: 'safeInventory',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.safeInventory`).d('安全库存'),
    },
    {
      name: 'maxInventory',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maxInventory`).d('最大库存'),
      dynamicProps: {
        min: ({ record }) => {
          return record?.get('safeInventory');
        },
      },
    },
    {
      name: 'pullBatch',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.pullBatch`).d('拉动固定批量'),
    },
    {
      name: 'deliveryCycle',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.deliveryCycle`).d('交货周期'),
    },
    {
      name: 'jitFlag',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'uomCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('交货周期单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: {
        tenantId,
        uomTypeDesc: '时间单位',
      },
      ignore: 'always',
      required: true,
    },
    {
      name: 'uomId',
      bind: 'uomCodeLov.uomId',
    },
    {
      name: 'uomCode',
      bind: 'uomCodeLov.uomCode',
    },

    {
      name: 'overdueWarn',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overdueWarn`).d('超时未制单预警期'),
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('jitFlag');
        },
      },
    },
    {
      name: 'overdueUomCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.overdueUomCodeLov`).d('超时未制单预警期单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: {
        tenantId,
        uomTypeDesc: '时间单位',
      },
      ignore: 'always',
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('jitFlag');
        },
      },
    },
    {
      name: 'overdueUomId',
      bind: 'overdueUomCodeLov.uomId',
    },
    {
      name: 'overdueUomCode',
      bind: 'overdueUomCodeLov.uomCode',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      lovPara: { tenantId },
      lookupCode: 'MT.FLAG',
      required: true,
      defaultValue: 'Y',
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('更新时间'),
    },
  ],
});

export { headerTableDS };
