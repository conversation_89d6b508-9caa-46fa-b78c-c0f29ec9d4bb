/**
 * @Description: 采购退货平台-列表页
 * @Author: <<EMAIL>>
 * @Date: 2021-12-21 14:14:48
 * @LastEditTime: 2023-06-30 17:46:18
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Table, Modal, Button, Dropdown, Menu } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
// import FRPrintButton from '@components/tarzan-ui/FRPrintButton';
import { getCurrentOrganizationId } from 'utils/utils';
import { getResponse } from '@utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import {
  ColumnAlign,
  TableColumnTooltip,
  ColumnLock,
  TableQueryBarType,
} from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import request from 'utils/request';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';
import { PurchaseListDS, LineDs, DetailDs } from '../stores/PurchaseListDS';
import styles from './index.module.less';
import { StatusAlter } from '../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.purchase.purchaseReturn';
const tenantId = getCurrentOrganizationId();
let modalBarCode;

const closeList: string[] = [
  'PROCESSING',
  '1_PROCESSING',
  '1_COMPLETED',
  '2_PROCESSING',
  'COMPLETED',
];

const PurchaseListWms = observer(props => {
  const {
    purchaseListDS,
    lineDs,
    detailDs,
    match: { path },
    customizeTable,
  } = props;

  const [printIds, setPrintIds] = useState<any>([]); // 头表格选择的id
  const [selectedLinesInfo, setSelectedLinesInfo] = useState([]);
  const [closeFlag, setCloseFlag] = useState(false);
  const [cancelFlag, setCancelFlag] = useState(false);
  const { run: statusAlter, loading: statusLoading } = useRequest(StatusAlter(), { manual: true });

  useEffect(() => {
    /* (async () => {
      const res = await purchaseListDS.query(props.purchaseListDS.currentPage);
      if (res?.success && !isEmpty(res?.rows?.content)) {
        const { instructionDocId } = res?.rows?.content[0];
        handleLineQuery(instructionDocId);
      }
    })(); */
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  useEffect(() => {
    const instructionDocNum = props?.location?.query?.instructionDocNum;
    if (instructionDocNum && purchaseListDS) {
      setTimeout(() => {
        awitDsQuery(instructionDocNum);
      }, 10);
    } else if (props?.history?.action === 'PUSH') {
      purchaseListDS.query(purchaseListDS.currentPage);
    }
  }, [props?.location?.query?.instructionDocNum]);

  useEffect(() => {
    function processDataSetListener(flag) {
      if (props.purchaseListDS.queryDataSet) {
        const handler = flag
          ? props.purchaseListDS.queryDataSet.addEventListener
          : props.purchaseListDS.queryDataSet.removeEventListener;
        handler.call(props.purchaseListDS.queryDataSet, 'update', handleQueryDataSetUpdate);
      }
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  const awitDsQuery = instructionDocNum => {
    purchaseListDS.queryDataSet.forEach(record => {
      record.set('instructionDocNum', instructionDocNum);
    });
    purchaseListDS.query();
  };

  // 如果供应商改变，那么供应商地点编码清空
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (!data.supplierId) {
      record.set('supplierSiteLov', {});
    }
  };

  const listener = flag => {
    // 列表交互监听
    if (purchaseListDS) {
      const handler = flag ? purchaseListDS.addEventListener : purchaseListDS.removeEventListener;
      // 列表加载事件
      handler.call(purchaseListDS, 'load', resetHeaderDetail);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    if (props?.location?.state) {
      // eslint-disable-next-line no-param-reassign
      props.location.state = {};
    }
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      const { instructionDocId } = dataSet?.current?.toData();
      handleLineQuery(instructionDocId);
    } else {
      lineDs.loadData([]);
    }
  };

  const handleLineQuery = instructionDocId => {
    lineDs.setQueryParameter('instructionDocId', instructionDocId);
    lineDs.query();
  };

  const handleRowClick = record => {
    const { instructionDocId } = record.toData();
    handleLineQuery(instructionDocId);
  };

  // 条码明细
  const handleBarCode = record => {
    detailDs.setQueryParameter('instructionId', record.toData().instructionId);
    detailDs.setQueryParameter('identifyType', record.toData().identifyType);
    detailDs.query();
    modalBarCode = Modal.open({
      title:
        record?.data?.identifyType === 'MATERIAL_LOT'
          ? intl.get(`${modelPrompt}.materialBatch.details`).d('物料批信息')
          : intl.get(`${modelPrompt}.materialDetail`).d('物料明细'),
      // title: intl.get(`${modelPrompt}.materialBatch.details`).d('物料批明细'),
      className: 'hmes-style-modal',
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      children: customizeTable(
        {
          code: `${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.LINE_DETAIL`,
        },
        <Table
          dataSet={detailDs}
          highLightRow={false}
          columns={record?.data?.identifyType === 'MATERIAL_LOT' ? detailColumns : matColumns}
        />,
      ),
      footer: (
        <Button onClick={() => modalBarCode.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  const columns: ColumnProps[] = [
    {
      name: 'instructionDocNum',
      tooltip: TableColumnTooltip.overflow,
      renderer: ({ record, value }) => {
        if (record?.get('instructionDocStatus') === 'RELEASED') {
          return (
            <a
              onClick={() => {
                purchaseListDS.batchUnSelect(purchaseListDS.selected);
                props.history.push(
                  `/hwms/purchase/purchase-return/detail/${record?.get('instructionDocId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        }
        return value;
      },
    },
    {
      name: 'instructionDocStatusDesc',
    },
    {
      name: 'instructionDocTypeDesc',
    },
    { name: 'returnOrder' },
    {
      name: 'fromLocatorCode',
    },
    {
      name: 'fromLocatorName',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'supplierSiteName',
    },
    {
      name: 'remark',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'printTimes',
      width: 100,
    },
    {
      name: 'realName',
    },
    {
      name: 'creationDate',
      align: ColumnAlign.center,
    },
    {
      name: 'demandTime',
      width: 150,
    },
  ];

  const lineColumns: ColumnProps[] = [
    {
      name: 'lineNumber',
      align: ColumnAlign.left,
      lock: ColumnLock.left,
      width: 80,
    },
    {
      name: 'identifyType',
      width: 120,
      align: ColumnAlign.left,
      lock: ColumnLock.left,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        }
        if (value === 'MATERIAL_LOT') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
        return '';
      },
    },
    {
      name: 'materialCode',
      align: ColumnAlign.left,
      lock: ColumnLock.left,
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'revisionCode',
      align: ColumnAlign.left,
      lock: ColumnLock.left,
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'materialName',
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'siteCode',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'instructionStatusDesc',
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'quantity',
      align: ColumnAlign.right,
    },
    {
      name: 'actualQty',
      align: ColumnAlign.right,
    },
    {
      name: 'uomCode',
    },
    {
      name: 'fromLocatorCode',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'poNumber',
      width: 150,
    },
    {
      name: 'lineNum',
    },
    {
      name: 'soNum',
    },

    {
      name: 'soLineNum',
    },
    {
      name: 'inspectDocNum',
      width: 180,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              props.history.push(`/hwms/inspect-doc-maintain/dist/${record?.get('inspectDocId')}`);
            }}
          >
            {value}
          </a>
        );
      },
    },
    { name: 'createByName' },
    { name: 'creationDate' },
    {
      name: 'remark',
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'poLineId',
      lock: ColumnLock.right,
      width: 140,
      align: ColumnAlign.center,
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      renderer: ({ record }) => (
        <PermissionButton
          type="text"
          onClick={() => handleBarCode(record)}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.details`).d('明细')}
        </PermissionButton>
      ),
    },
  ];

  const detailColumns: ColumnProps[] = [
    {
      name: 'identification',
      tooltip: TableColumnTooltip.overflow,
      width: 150,
    },
    {
      name: 'actualQty',
      align: ColumnAlign.right,
      width: 80,
    },
    {
      name: 'uomCode',
    },
    {
      name: 'materialLotStatusDesc',
    },
    {
      name: 'locatorCode',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'lot',
    },
    {
      name: 'containerCode',
    },
    {
      name: 'realName',
    },
    {
      name: 'lastUpdateDate',
      align: ColumnAlign.center,
      width: 200,
    },
  ];

  const matColumns: ColumnProps[] = [
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'qualityStatusDesc',
    },
    {
      name: 'actualQty',
      align: ColumnAlign.right,
      width: 80,
    },
    {
      name: 'uomCode',
    },

    {
      name: 'locatorCode',
      tooltip: TableColumnTooltip.overflow,
    },
    {
      name: 'lot',
    },
    {
      name: 'realName',
    },
    {
      name: 'lastUpdateDate',
      align: ColumnAlign.center,
      width: 200,
    },
  ];

  // 选择
  const handleTableChange = () => {
    const _printIds: string[] = [];
    purchaseListDS.selected.forEach(item => {
      _printIds.push(item.data.instructionDocId);
    });
    setPrintIds(_printIds);
  };

  // 跳转新建页面
  // const handleCreate = useCallback(() => {
  //   props.history.push('/hwms/purchase/purchase-return/detail/create');
  // }, []);

  useEffect(() => {
    let _cancelFlag = true;
    let _closeFlag = true;
    setSelectedLinesInfo(
      (purchaseListDS.selected || []).map(item => {
        if (item.get('instructionDocStatus') !== 'RELEASED') {
          _cancelFlag = false;
        }
        if (!closeList.includes(item.get('instructionDocStatus'))) {
          _closeFlag = false;
        }
        return {
          instructionDocId: item.get('instructionDocId'),
          instructionDocStatus: item.get('instructionDocStatus'),
          instructionDocType: item.get('instructionDocType'),
        };
      }),
    );
    setCancelFlag(_cancelFlag);
    setCloseFlag(_closeFlag);
  }, [purchaseListDS.selected]);

  const clickMenu = key => {
    const _params = {
      alterStatus: key,
      instructionDocList: selectedLinesInfo,
    };
    statusAlter({
      params: _params,
      onSuccess: () => {
        notification.success({});
        purchaseListDS.query(purchaseListDS.currentPage);
      },
    });
  };

  const handlePrint = async () => {
    const result = await request(
      `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wmsInstructionDoc/instruction/doc/print`,
      {
        method: 'POST',
        responseType: 'blob',
        body: printIds,
      },
    );
    const res = getResponse(result);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader: any = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: '打印成功',
          });
        } else {
          notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
        }
      }
    }
  };

  const menu = (
    <Menu className={styles['split-menu']} style={{ width: '100px' }}>
      <Menu.Item disabled={!cancelFlag} key="CANCEL">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CANCEL')}>
          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
        </a>
      </Menu.Item>
      <Menu.Item disabled={!closeFlag} key="CLOSED">
        <a target="_blank" rel="noopener noreferrer" onClick={() => clickMenu('CLOSED')}>
          {intl.get(`${modelPrompt}.button.close`).d('关闭')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const getQueryParams = () => {
    const searchForm = purchaseListDS?.queryDataSet?.current?.toData();
    return {
      ...searchForm,
      instructionDocIds: purchaseListDS.selected.length > 0 ? purchaseListDS.selected.map(e => e.get('instructionDocId')) : [],
    };
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.purchaseReturn`).d('采购退货平台')}>
        {/* <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
        >
          {intl.get('tarzan.common.button.print').d('打印')}
        </PermissionButton> */}
        {/* <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton> */}
        <Dropdown overlay={menu} disabled={!selectedLinesInfo.length}>
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={!selectedLinesInfo.length}
            loading={statusLoading}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <Button onClick={handlePrint} disabled={printIds.length === 0} color={ButtonColor.primary} >
          {intl.get(`${modelPrompt}.print`).d('打印')}
        </Button>
        {/* <FRPrintButton
          kid="PURCHASE_RETURN_PLATFORM"
          queryParams={printIds}
          disabled={!(printIds.length > 0)}
          printObjectType="INSTRUCTION_DOC"
        /> */}
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-instruction-doc/export`}
          queryParams={getQueryParams}
          buttonText={intl.get(`${modelPrompt}.`).d('导出')}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.HEAD`,
          },
          <Table
            searchCode="cgthpt1"
            customizedCode="cgthpt1"
            dataSet={purchaseListDS}
            columns={columns}
            highLightRow
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            // @ts-ignore
            onChange={handleTableChange}
            onRow={({ record }) => {
              return {
                onClick: () => {
                  handleRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineDs}
          >
            {lineDs &&
              customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.LINE`,
                },
                <Table
                  customizedCode="cgthpt2"
                  className={styles['expand-table']}
                  dataSet={lineDs}
                  highLightRow={false}
                  columns={lineColumns}
                />,
              )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
});

export default flow(
  withProps(
    () => {
      const purchaseListDS = new DataSet({ ...PurchaseListDS() });
      const lineDs = new DataSet({ ...LineDs() });
      const detailDs = new DataSet({ ...DetailDs() });
      return {
        purchaseListDS,
        lineDs,
        detailDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.PURCHASE_RETURN_LIST.LINE_DETAIL`,
    ],
  }),
  formatterCollections({ code: ['tarzan.hmes.purchase.purchaseReturn', 'tarzan.common'] }),
)(PurchaseListWms);
