import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, } from 'utils/utils';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.ass.worthlessIncomingInspection';
const tenantId = getCurrentOrganizationId();
const listPageFactory = () =>
  new DataSet({
    primaryKey: 'instructionDocId',
    selection: DataSetSelection.multiple,
    paging: true,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'materialLotCodes',
          label: intl.get(`${modelPrompt}.form.materialLotCodes`).d('物料批编码'),
          type: FieldType.string,
          transformRequest: (val, record) => {
            if (val?.trim().includes(" ")) {
              return record.set('materialLotCodes', val.trim().split(" ").join(","))
            }
            return val;
          },
        },
        {
          name: 'materialCode',
          label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
          ignore: FieldIgnore.always,
          type: FieldType.object,
          multiple: true,
          lovCode: 'MT.METHOD.MATERIAL',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'materialIds',
          bind: 'materialCode.materialId',
        },
        {
          name: 'relateDocCode',
          label: intl.get(`${modelPrompt}.form.relateDocCode`).d('关联单据'),
          type: FieldType.string,
        },
        {
          name: 'siteCode',
          type: FieldType.object,
          ignore: FieldIgnore.always,
          label: intl.get(`${modelPrompt}.form.siteCode`).d('站点'),
          lovCode: 'MT.MODEL.SITE',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'siteId',
          type: FieldType.string,
          bind: 'siteCode.siteId',
        },
        {
          name: 'wareHouseLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
          lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
          multiple: true,
          lovPara: {
            tenantId,
            locatorCategory: ['AREA'],
            operationType:'noValueInStorage'
          },
        },
        {
          name: 'wareHouseIds',
          type: FieldType.string,
          bind: 'wareHouseLov.locatorId',
        },
        {
          name: 'locatorLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
          multiple: true,
          lovCode: 'MT.MODEL.SUB_LOCATOR',
          ignore: FieldIgnore.always,
          dynamicProps: {
            // disabled: ({ record }) => {
            //   return !record?.get('wareHouseIds');
            // },
            lovPara: ({ record }) => {
              return {
                tenantId,
                locatorIds: record?.get('wareHouseIds'),
                locatorCategory: ['LOCATION', 'INVENTORY'].join(','),
              };
            },
          },
        },
        {
          name: 'locatorIds',
          bind: 'locatorLov.locatorId',
        },
        {
          name: 'inspectDateFrom',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.inspectDateFrom`).d('抽检时间从'),
        },
        {
          name: 'inspectDateTo',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.inspectDateTo`).d('抽检时间至'),
        },
      ],
    }),
    fields: [
      {
        name: 'materialLotCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'siteCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.siteCode`).d('工厂'),
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
      },
      {
        name: 'tagWeight',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.tagWeight`).d('标签重量'),
      },
      {
        name: 'sampleInspectionWeight',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.sampleInspectionWeight`).d('抽检重量'),
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.uomCode`).d('单位'),
      },
      {
        name: 'relateDocCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.relateDocCode`).d('关联单据'),
      },
      {
        name: 'wareHouseCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.wareHouseCode`).d('仓库'),
      },
      {
        name: 'locatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.locatorCode`).d('库位'),
      },
      {
        name: 'inspectMachine',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.inspectMachine`).d('抽检设备'),
      },
      {
        name: 'inspectDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.form.inspectDate`).d('抽检时间'),
      },
      {
        name: 'creationDate',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.form.creationDate`).d('创建时间'),
      },
      {
        name: 'creationByName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.creationByName`).d('创建人'),
      },
      {
        name: 'createWarehouseCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.createWarehouseCode`).d('创建仓库编码'),
      },
      {
        name: 'inspectEmployeeName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.inspectEmployeeName`).d('抽检人员'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/wms-valua-into-warehouse/detail-report-query`,
        };
      },
    },
  });

export default listPageFactory;
