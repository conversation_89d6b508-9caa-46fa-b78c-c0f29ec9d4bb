import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// Get请求
export function GetMethod() {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/list/ui`,
    method: 'GET',
  };
}

// Post请求
export function PostMethod() {
  return {
    url: `${BASIC.TARZAN_MODEL}/v1/${tenantId}/save/ui`,
    method: 'POST',
  };
}
