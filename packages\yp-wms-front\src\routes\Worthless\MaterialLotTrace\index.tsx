/**
 * @Description: 无价值标签管理平台-列表页
 * @Author: <<EMAIL>>
 * @Date: 2022-1-25 14:38:56
 * @LastEditTime: 2022-11-21 13:42:12
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Table, Button, Modal } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import ExcelExport from 'components/ExcelExport';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Badge, Popconfirm } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
// import { FRPrintButton } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { API_HOST, BASIC } from '@utils/config';
import { isNil } from 'lodash';
import request from 'utils/request';
import { getResponse } from '@utils/utils';
import notification from 'utils/notification';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import {
  tableDS,
  historyDS,
  creattableDS,
  dangerousCreattableDS,
  editTableDS,
} from './stores/ListTable';
import HistoryDrawer from './HistoryDrawer';
import {
  FetchDynamicColumn,
  creattableSave,
  goexportSave,
  tableSave,
  AbandonMaterialLot,
} from './services';
import EventDetailsDrawer from './EventDetailsDrawer';

const modelPrompt = 'tarzan.hmes.product.materialLotTrance';
const tenantId = getCurrentOrganizationId();

let TableHeight = 600;

const SubjectMaintainListWms = (props: any) => {
  const {
    tableDs,
    match: { path },
  } = props;
  // 存储选中数据的key

  useEffect(() => {
    TableHeight = window.screen.height - 450;
  }, []);

  const historyDs = useMemo(() => new DataSet(historyDS()), []);
  const creattableDs = useMemo(() => new DataSet(creattableDS()), []);
  const dangerousCreattableDs = useMemo(() => new DataSet(dangerousCreattableDS()), []);
  const editTableDs = useMemo(() => new DataSet(editTableDS()), []);
  const fetchDynamicColumn = useRequest(FetchDynamicColumn('mt_material_lot_attr'));
  const creattablesave = useRequest(creattableSave(), {
    manual: true,
    needPromise: true,
  });
  const tablesave = useRequest(tableSave(), {
    manual: true,
    needPromise: true,
  });
  const goexportsave = useRequest(goexportSave(), {
    manual: true,
    needPromise: true,
  });
  const { run: abandonMaterialLot } = useRequest(AbandonMaterialLot(), {
    needPromise: true,
    manual: true,
  });

  const [selectedMaterialLotList, setSelectedMaterialLotList] = useState<any[]>([]);

  const [, setSelectedKey] = useState([]);
  const [, setFreezeYList] = useState([]);
  const [, setFreezeNList] = useState([]);

  useEffect(() => {
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    if (Object.keys(props?.location?.query).length === 0 || props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      tableDs.query(props.tableDs.currentPage);
      return;
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const {
      siteId,
      siteCode,
      materialId,
      materialCode,
      revisionCode,
      lotCode,
      qualityStatus,
      ownerType,
      ownerId,
      ownerCode,
      loactorsInfo,
    } = props?.location?.query || {};
    const queryParams = {
      siteLov: siteId ? { siteId, siteCode } : undefined,
      materialLov: materialId ? { materialId, materialCode } : undefined,
      revisionCodes: revisionCode && revisionCode.length ? [revisionCode] : undefined,
      lotList: lotCode && lotCode.length ? [lotCode] : undefined,
      qualityStatus,
      ownerType,
      ownerLov: ownerId
        ? { soLineId: ownerId, customerId: ownerId, soNumContent: ownerCode }
        : undefined,
      locatorLov: loactorsInfo ? JSON.parse(loactorsInfo) : null,
    };
    tableDs.queryDataSet.loadData([queryParams]);
    setTimeout(() => {
      tableDs.query();
    }, 200);
  }, [props?.location?.query, props?.location?.state]);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
      tableDs.clearCachedRecords();
    };
  });
  let modalCreateShipping;
  let langModalCreateShipping;
  let editModal;
  // 处理选中条状态
  const handleDataSetSelectUpdateFun = () => {
    if (tableDs && tableDs.selected) {
      const selectList = tableDs.selected;
      if (selectList && selectList.length) {
        const arr = [];
        const freezeYArr = [];
        const freezeNArr = [];
        selectList.forEach(i => {
          // @ts-ignore
          arr.push(i.data.materialLotId);
          if (i.data.freezeFlag === 'Y') {
            // @ts-ignore
            freezeYArr.push(i.data.freezeFlag);
          }
          if (i.data.freezeFlag === 'N') {
            // @ts-ignore
            freezeNArr.push(i.data.freezeFlag);
          }
        });
        setSelectedKey(arr);
        setFreezeYList(freezeYArr);
        setFreezeNList(freezeNArr);
      } else {
        setSelectedKey([]);
      }
    } else {
      setSelectedKey([]);
    }
  };

  const listener = flag => {
    // 列表交互监听
    if (tableDs || creattableDs) {
      const handerQuery = flag
        ? tableDs.queryDataSet.addEventListener
        : tableDs.queryDataSet.removeEventListener;
      const handler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // 查询条件更新时操作
      handerQuery.call(tableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
      handler.call(tableDs, 'load', handleDataSetSelectUpdate);
      // 头选中和撤销选中事件
      handler.call(tableDs, 'select', handleDataSetSelectUpdate);
      handler.call(tableDs, 'unSelect', handleDataSetSelectUpdate);
      handler.call(tableDs, 'selectAll', handleDataSetSelectUpdate);
      handler.call(tableDs, 'unSelectAll', handleDataSetSelectUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ name, record }) => {
    if (name === 'ownerType') {
      record.set('ownerLov', {});
    }
    if (name === 'reservedObjectType') {
      record.set('reservedObjectLov', {});
    }
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    const _materialLotList: string[] = [];
    tableDs.selected.forEach(item => {
      const { materialLotId } = item.toData();
      _materialLotList.push(materialLotId);
    });
    setSelectedMaterialLotList(_materialLotList);
    handleDataSetSelectUpdateFun();
  };

  const [dynamicColumns, setDynamicColumns] = useState<ColumnProps[]>([]);

  useEffect(() => {
    const dynamicColumn: ColumnProps[] = [];
    ((fetchDynamicColumn.data || {}).content || []).forEach(item => {
      if (item.enableFlag === 'Y') {
        dynamicColumn.push({
          header: item.attrMeaning,
          name: item.attrName,
          align: ColumnAlign.left,
          width: 150,
          renderer: ({ record }) => {
            return (record?.get('attrMap') || {})[item.attrName];
          },
        });
      }
    });
    setDynamicColumns(dynamicColumn);
  }, [fetchDynamicColumn.data]);

  const handleSaveData = () => {
    return tablesave
      .run({
        params: [
          {
            materialLotId: editTableDs.toData()[0]?.materialLotId,
            primaryUomQty: editTableDs.toData()[0]?.primaryUomQty,
          },
        ],
      })
      .then(res => {
        if (res && res.success) {
          editModal.close();
          notification.success({});
          tableDs.query();
        } else {
          return Promise.resolve(false);
        }
      });
  };

  const handleEditData = record => {
    editTableDs.loadData([record]);
    editModal = Modal.open({
      title: '修改数量',
      maskClosable: true,
      destroyOnClose: true,
      resizable: true,
      style: {
        width: 1080,
      },
      drawer: true,
      children: <Table dataSet={editTableDs} columns={editColumns}></Table>,
      footer: (okBtn, cancelBtn) => (
        <div>
          {/* {okBtn} */}
          <Button onClick={handleSaveData}>保存</Button>
          {cancelBtn}
        </div>
      ),
    });
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'identification',
        align: ColumnAlign.left,
        width: 250,
        lock: ColumnLock.left,
        // renderer: ({ value, record }) => {
        //   return (
        //     <a
        //       onClick={() => {
        //         props.history.push(
        //           `/hwms/product/material-lot-traceability/detail/${record?.get('materialLotId')}`,
        //         );
        //       }}
        //     >
        //       {value}
        //     </a>
        //   );
        // },
      },
      {
        name: 'materialLotCode',
        width: 250,
        renderer: ({ record, value }) => {
          if (record.get('materialLotStatus') === 'NEW') {
            return <a onClick={() => handleEditData(record)}>{value}</a>;
          }
          return value;
        },
      },
      {
        name: 'materialCode',
        width: 250,
      },
      // {
      //   name: 'revisionCode',
      //   width: 100,
      // },
      {
        name: 'materialName',
        width: 250,
      },
      {
        name: 'materialLotStatusDesc',
        width: 150,
      },
      {
        name: 'primaryUomQty',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'primaryUomCode',
        width: 120,
      },
      {
        name: 'siteCode',
        width: 250,
      },
      {
        name: 'warehouseCode',
        width: 250,
      },
      {
        name: 'locatorCode',
        width: 250,
      },
      {
        name: 'enableFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'instructionDocNum',
        width: 150,
      },
      {
        name: 'produceLot',
        width: 100,
      },
      {
        name: 'produceTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'unitName',
        width: 250,
      },
      {
        name: 'directoryName',
        width: 150,
      },
      {
        name: 'containerCode',
        width: 150,
      },
      {
        name: 'containerType',
        width: 150,
      },
      {
        name: 'wasteMaterial',
        width: 150,
      },
      {
        name: 'wasteSpecification',
        width: 150,
      },
      {
        name: 'containerQty',
        width: 150,
      },
      {
        name: 'wasteContainer',
        width: 150,
      },
      {
        name: 'productAgent',
        width: 150,
      },
      {
        name: 'productAgentPhone',
        width: 150,
      },
      {
        name: 'transferAgent',
        width: 150,
      },
      {
        name: 'trace',
        width: 150,
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'createdByName',
        width: 150,
      },
      {
        name: 'lastUpdatedByName',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 150,
      },
      {
        name: 'mainIngredients',
        width: 150,
      },
      {
        name: 'attention',
        width: 150,
      },
      {
        name: 'remark',
        width: 150,
      },
    ];
  }, []);

  const editColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'identification',
        align: ColumnAlign.left,
        width: 250,
        lock: ColumnLock.left,
      },
      {
        name: 'materialLotCode',
        width: 250,
      },
      {
        name: 'materialCode',
        width: 250,
      },
      {
        name: 'materialName',
        width: 250,
      },
      {
        name: 'materialLotStatusDesc',
        width: 150,
      },
      {
        name: 'primaryUomQty',
        align: ColumnAlign.right,
        width: 100,
        editor: true,
      },
      {
        name: 'primaryUomCode',
        width: 120,
      },
      {
        name: 'siteCode',
        width: 250,
      },
      {
        name: 'warehouseCode',
        width: 250,
      },
      {
        name: 'locatorCode',
        width: 250,
      },
      {
        name: 'enableFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          >
            {}
          </Badge>
        ),
      },
      {
        name: 'instructionDocNum',
        width: 150,
      },
      {
        name: 'produceLot',
        width: 100,
      },
      {
        name: 'produceTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'unitName',
        width: 250,
      },
      {
        name: 'directoryName',
        width: 150,
      },
      {
        name: 'containerCode',
        width: 150,
      },
      {
        name: 'containerType',
        width: 150,
      },
      {
        name: 'containerQty',
        width: 150,
      },
      {
        name: 'wasteContainer',
        width: 150,
      },
      {
        name: 'productAgent',
        width: 150,
      },
      {
        name: 'transferAgent',
        width: 150,
      },
      {
        name: 'trace',
        width: 150,
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'createdByName',
        width: 150,
      },
      {
        name: 'lastUpdatedByName',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 150,
      },
    ];
  }, []);

  const dangerousCreatecolumns: ColumnProps[] = useMemo(() => {
    return [
      {
        header: (
          <Button
            icon="add"
            onClick={() => dangerHandleAdd()}
            funcType="flat"
            shape="circle"
            size="small"
          />
        ),
        align: 'center',
        name: 'name',
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDelete(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <Button
              disabled={record.status !== 'add'}
              icon="remove"
              funcType="flat"
              shape="circle"
              size="small"
            />
          </Popconfirm>
        ),
        lock: 'left',
      },
      {
        name: 'materialLotCode',
        // width: 250,
      },
      {
        name: 'identification',
        // width: 250,
      },
      {
        name: 'siteLov',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'materialLov',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'wareHouseLov',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'primaryUomQty',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'produceLot',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'produceTime',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'unitName',
        // width: 100,
        editor: record => record.status === 'add',
      },
      // {
      //   name: 'directoryName',
      //   // width: 100,
      //   editor: record => record.status === 'add',
      // },
      // {
      //   name: 'containerCode',
      //   // width: 100,
      //   editor: record => record.status === 'add',
      // },
      {
        name: 'containerType',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'wasteMaterial',
        editor: record => record.status === 'add',
      },
      {
        name: 'wasteSpecification',
        editor: record => record.status === 'add',
      },
      {
        name: 'containerQty',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'wasteContainer',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'productAgent',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'productAgentPhone',
        editor: record => record.status === 'add',
      },
      {
        name: 'transferAgent',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'trace',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'produceArea',
        editor: record => record.status === 'add',
      },
      // {
      //   name: 'produceProcess',
      //   editor: record => record.status === 'add',
      // },
      {
        name: 'mainIngredients',
        editor: record => record.status === 'add',
      },
      {
        name: 'attention',
        editor: record => record.status === 'add',
      },
      {
        name: 'remark',
        editor: record => record.status === 'add',
      },
    ];
  }, []);
  const handleQueryMaterialLotsHistory = () => {
    historyDs.setQueryParameter('selectedMaterialLotList', selectedMaterialLotList);
    historyDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}</div>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/export/his/ui`}
            queryParams={{
              materialLotIds: selectedMaterialLotList,
            }}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
        </div>
      ),
      destroyOnClose: true,
      children: <HistoryDrawer ds={historyDs} />,
    });
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return {
      ...queryParmas,
      materialLotIdList: selectedMaterialLotList,
    };
  };

  const handleAbandonTag = () => {
    return new Promise(async resolve => {
      const validateFlag = tableDs.selected.some(
        record => record?.get('materialLotStatus') !== 'NEW',
      );
      if (validateFlag) {
        notification.error({
          message: intl
            .get(`${modelPrompt}.error.abandonMustNew`)
            .d('只有新建的标签可以废弃，请检查！'),
        });
        return resolve(false);
      }
      const res = await abandonMaterialLot({ params: selectedMaterialLotList });
      if (res?.success) {
        notification.success({});
        tableDs.query(tableDs.currentPage);
        return resolve(true);
      }
      return resolve(false);
    });
  };

  const handlePrint = async selected => {
    const data = [];
    tableDs.selected.map(record => {
      data.push(record.data?.materialLotId);
    });
    const result = await request(
      `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/wms-worthless-label-manage/label/print/ui`,
      {
        method: 'POST',
        responseType: 'blob',
        body: data,
      },
    );
    const res = getResponse(result);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader: any = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: '打印成功',
          });
        } else {
          notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
        }
      }
    }
  };

  const creattableHandlePrint = async () => {
    console.log('打印', creattableDs.selected.length);
    if (creattableDs.selected.length == 0) {
      notification.error({ message: '请勾选要打印的数据' });
      return;
    }
    const data = [];
    creattableDs.selected.map(record => {
      data.push(record.data?.materialLotId);
    });
    const result = await request(
      `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/wms-worthless-label-manage/label/print/ui`,
      {
        method: 'POST',
        responseType: 'blob',
        body: data,
      },
    );
    const res = getResponse(result);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader: any = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: '打印成功',
          });
        } else {
          notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
        }
      }
    }
  };
  const dangerousCreattableHandlePrint = async () => {
    const data = [];
    if (dangerousCreattableDs.selected.length == 0) {
      notification.error({ message: '请勾选要导出的数据' });
      return;
    }
    dangerousCreattableDs.selected.map(record => {
      data.push(record.data?.materialLotId);
    });
    console.log('打印', data);
    const result = await request(
      `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/wms-worthless-label-manage/label/print/ui`,
      {
        method: 'POST',
        responseType: 'blob',
        body: data,
      },
    );
    const res = getResponse(result);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader: any = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: '打印成功',
          });
        } else {
          notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
        }
      }
    }
  };

  const dangerHandleAdd = () => {
    console.log('add');
    dangerousCreattableDs.create({}, 0);
    dangerousCreattableDs.current.setState('editing', true);
  };

  const creacteSabe = async () => {
    const validate = await creattableDs.validate();
    if (!validate) {
      return false;
    }
    const list = creattableDs.toData().map(item => ({ ...item, insertType: 'N' }));
    return creattablesave
      .run({
        params: list,
      })
      .then(res => {
        console.log(res);
        if (res && res.success) {
          notification.success({});
          // creattableDs.query();
          const materialLotIdList = [];
          res.rows.map((item, index) => {
            materialLotIdList.push(item.materialLotId);
          });
          console.log('materialLotIdList', materialLotIdList);
          creattableDs.setQueryParameter('materialLotIdList', materialLotIdList);
          creattableDs.query();
        } else {
          return Promise.resolve(false);
        }
      });
  };
  const dangerousCreacteSabe = async () => {
    console.log(dangerousCreattableDs.toData());
    const validate = await dangerousCreattableDs.validate();
    if (!validate) {
      return false;
    }
    const list = dangerousCreattableDs.toData().map(item => ({ ...item, insertType: 'Y' }));
    return creattablesave
      .run({
        params: list,
      })
      .then(res => {
        console.log(res);
        if (res && res.success) {
          notification.success({});
          // dangerousCreattableDs.query();
          const materialLotIdList = [];
          res.rows.map((item, index) => {
            materialLotIdList.push(item.materialLotId);
          });
          console.log('materialLotIdList', materialLotIdList);
          dangerousCreattableDs.setQueryParameter('materialLotIdList', materialLotIdList);
          dangerousCreattableDs.query();
        } else {
          return Promise.resolve(false);
        }
      });
  };
  const goImport = () => {
    modalCreateShipping.close();
    openTab({
      key: '/himp/commentImport/WMS.WORTHLESS_LABEL',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };
  const dangerousGoImport = () => {
    langModalCreateShipping.close();
    openTab({
      key: '/himp/commentImport/WMS.WORTHLESS_LABEL_WASTE',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };
  // 新建表格后可删除
  const handleDelete = record => {
    dangerousCreattableDs.delete(record, false);
  };
  const goexport = async () => {
    const data = [];
    if (creattableDs.selected.length == 0) {
      notification.error({ message: '请勾选要导出的数据' });
      return;
    }
    creattableDs.selected.forEach(item => {
      console.log(item);
      console.log(item.data);
      data.push(item.data.materialLotId);
    });
    const result = await request(
      `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/wms-worthless-label-manage/product/record/export/ui`,
      {
        method: 'POST',
        responseType: 'blob',
        body: data,
      },
    );
    const res = result;
    console.log('res', res);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          // @ts-ignore
          const jsonData = JSON.parse(fileReader.result);
          if (!jsonData?.success) {
            notification.error({ description: jsonData?.message });
          }
          try {
            getResponse(jsonData);
          } catch (error) {
            //
          }
        };
        fileReader.readAsText(res);
        tableDs.reset();
      } else {
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
        const a = document.createElement('a');
        const url = window.URL.createObjectURL(blob);
        a.href = url;
        a.download = decodeURI('危险废物产生环节记录表.xlsx');
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        a.parentNode?.removeChild(a);
        window.URL.revokeObjectURL(url);
        tableDs.reset();
      }
    }
  };
  const goexportonder = async () => {
    const data = [];
    if (tableDs.selected.length == 0) {
      notification.error({ message: '请勾选要导出的数据' });
      return;
    }
    tableDs.selected.forEach(item => {
      console.log(item);
      console.log(item.data);
      data.push(item.data.materialLotId);
    });
    const result = await request(
      `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/wms-worthless-label-manage/product/record/export/ui`,
      {
        method: 'POST',
        responseType: 'blob',
        body: data,
      },
    );
    const res = result;
    console.log('res', res);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          // @ts-ignore
          const jsonData = JSON.parse(fileReader.result);
          if (!jsonData?.success) {
            notification.error({ description: jsonData?.message });
          }
          try {
            getResponse(jsonData);
          } catch (error) {
            //
          }
        };
        fileReader.readAsText(res);
        tableDs.reset();
      } else {
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
        const a = document.createElement('a');
        const url = window.URL.createObjectURL(blob);
        a.href = url;
        a.download = decodeURI('危险废物产生环节记录表.xlsx');
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        a.parentNode?.removeChild(a);
        window.URL.revokeObjectURL(url);
        tableDs.reset();
      }
    }
  };
  const dangerousGoexport = async () => {
    const data = [];
    if (dangerousCreattableDs.selected.length == 0) {
      notification.error({ message: '请勾选要导出的数据' });
      return;
    }
    dangerousCreattableDs.selected.forEach(item => {
      console.log(item);
      console.log(item.data);
      data.push(item.data.materialLotId);
    });
    const result = await request(
      `${
        BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/wms-worthless-label-manage/product/record/export/ui`,
      {
        method: 'POST',
        responseType: 'blob',
        body: data,
      },
    );
    const res = result;
    console.log('res', res);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          // @ts-ignore
          const jsonData = JSON.parse(fileReader.result);
          if (!jsonData?.success) {
            notification.error({ description: jsonData?.message });
          }
          try {
            getResponse(jsonData);
          } catch (error) {
            //
          }
        };
        fileReader.readAsText(res);
        tableDs.reset();
      } else {
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
        const a = document.createElement('a');
        const url = window.URL.createObjectURL(blob);
        a.href = url;
        a.download = decodeURI('危险废物产生环节记录表.xlsx');
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        a.parentNode?.removeChild(a);
        window.URL.revokeObjectURL(url);
        tableDs.reset();
      }
    }
  };
  const openMoreButtons = React.useCallback(() => {
    creattableDs.loadData([]);
    modalCreateShipping = Modal.open({
      maskClosable: true,
      destroyOnClose: true,
      title: '一般固废创建',
      resizable: true,
      style: {
        width: 1080,
      },
      drawer: true,
      children: <EventDetailsDrawer dataSource={creattableDs} />,
      header: () => (
        <div>
          <p style={{ float: 'left', fontSize: '16px' }}>一般固废创建</p>

          <Button
            style={{ float: 'right', marginLeft: '10px' }}
            onClick={() => creattableHandlePrint()}
          >
            {intl.get('tarzan.common.button.print').d('打印')}
          </Button>
          <Button style={{ float: 'right' }} type="c7n-pro" icon="file_upload" onClick={goImport}>
            {intl.get(`tarzan.common.button.import`).d('导入')}
          </Button>
          <div style={{ float: 'right' }}>
            <Button icon="file_upload" onClick={goexport}>
              {intl.get(`tarzan.common.button.modelPrompta`).d('导出')}
            </Button>
          </div>
        </div>
      ),
      footer: (okBtn, cancelBtn) => (
        <div>
          {/* {okBtn} */}
          <Button onClick={creacteSabe}>保存</Button>
          {cancelBtn}
        </div>
      ),
    });
  }, [Modal]);

  const handleFetchDefaultSite = () => {
    return request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/lovs/sql/data`, {
      method: 'get',
      query: {
        lovCode: 'WMS.NOWORTH_SITE',
        tenantId: 0,
      },
    }).then(res => {
      if (res && res.content.length === 1) {
        dangerousCreattableDs.current.set('siteLov', res.content[0]);
      }
    });
  };

  const dangerousOpenMoreButtons = React.useCallback(() => {
    dangerousCreattableDs.loadData([]);
    dangerousCreattableDs.create({ status: 'add' });
    handleFetchDefaultSite()
    langModalCreateShipping = Modal.open({
      title: '危险固废创建按钮',
      maskClosable: true,
      destroyOnClose: true,
      resizable: true,
      style: {
        width: 1080,
      },
      drawer: true,
      children: (
        <Table
          dataSet={dangerousCreattableDs}
          columns={dangerousCreatecolumns}
          // buttons={buttons}
        ></Table>
      ),
      header: () => (
        <div>
          <p style={{ float: 'left', fontSize: '16px' }}>危险固废创建按钮</p>
          <Button
            style={{ float: 'right', marginLeft: '10px' }}
            onClick={() => dangerousCreattableHandlePrint()}
            // disabled={selectedMaterialLotList.length === 0}
          >
            {intl.get('tarzan.common.button.print').d('打印')}
          </Button>
          {/* <Button
            style={{ float: 'right' }}
            type="c7n-pro"
            icon="file_upload"
            onClick={dangerousGoImport}
          >
            {intl.get(`tarzan.common.button.import`).d('导入')}
          </Button> */}
          <div style={{ float: 'right' }}>
            <Button icon="file_upload" onClick={dangerousGoexport}>
              {intl.get(`tarzan.common.button.modelPrompta`).d('导出')}
            </Button>
          </div>
        </div>
      ),
      footer: (okBtn, cancelBtn) => (
        <div>
          {/* {okBtn} */}
          <Button onClick={dangerousCreacteSabe}>保存</Button>
          {cancelBtn}
        </div>
      ),
    });
  }, [Modal]);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.materialLotMaintenancse`).d('无价值标签管理平台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={openMoreButtons}
        >
          {intl.get('tarzan.common.button.creatae').d('一般固废创建按钮')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={dangerousOpenMoreButtons}
        >
          {intl.get('tarzan.common.button.creatae').d('危险固废创建按钮')}
        </PermissionButton>
        <Button disabled={!selectedMaterialLotList.length} onClick={handleQueryMaterialLotsHistory}>
          {intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}
        </Button>
        <Button icon="file_upload" onClick={goexportonder}>
          {intl.get(`tarzan.common.button.modelPrompta`).d('导出')}
        </Button>
        <Button
          onClick={() => handlePrint(selectedMaterialLotList)}
          disabled={selectedMaterialLotList.length === 0}
        >
          {intl.get('tarzan.common.button.print').d('打印')}
        </Button>
        <ExcelExport
          exportAsync
          method="POST"
          allBody
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-label-manage/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.button.materialLotExport`).d('物料批导出')}
        />
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${modelPrompt}.worthless.list.abandon`,
              type: 'button',
              meaning: '废弃',
            },
          ]}
          disabled={!selectedMaterialLotList?.length}
          onClick={handleAbandonTag}
        >
          {intl.get(`${modelPrompt}.button.abandon`).d('废弃')}
        </PermissionButton>
        {/* <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          // icon="add"
          disabled={!selectedKey.length || selectedKey.length !== freezeYList.length}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          onClick={thawSelectData}
        >
          {intl.get(`${modelPrompt}.button.unfreeze`).d('解冻')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          // icon="add"
          disabled={!selectedKey.length || selectedKey.length !== freezeNList.length}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
          onClick={deleteSelectData}
        >
          {intl.get(`${modelPrompt}.button.freeze`).d('冻结')}
        </PermissionButton> */}
        {/* <FRPrintButton
          kid="MATERIAL_LOT"
          queryParams={selectedMaterialLotList}
          disabled={!selectedMaterialLotList.length}
          printObjectType="MATERIAL_LOT"
        /> */}
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
          }}
          queryFieldsLimit={10}
          searchCode="wlpglpt"
          customizedCode="wlpglpt"
          dataSet={tableDs}
          columns={columns.concat(dynamicColumns)}
          style={{ height: TableHeight }}
          headerRowHeight={30}
          rowHeight={28}
          footerRowHeight={20}
          virtual
          virtualCell
          pagination={{
            showPager: true, // 显示数字按钮
            pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
          }}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.product.materialLotTrance', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(SubjectMaintainListWms),
);
