import React, { useEffect, useState, } from 'react';
import {
  Form,
  Row,
  Col,
  TextField,
  Button,
  Select,
  Table,
  Lov,
  DateTimePicker,
} from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { useDataSet, } from 'utils/hooks';
import { RouteComponentProps } from 'react-router';
import { Collapse, Popconfirm } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import detailPageFactory from '../stores/detailPageDs';
import detailTable from '../stores/detailTableDs';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import axios from 'axios';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
const { Panel } = Collapse;

const modelPrompt = 'tarzan.ass.demandManage';
interface RouterId {
  id: string;
}

const DetailPage: React.FC<RouteComponentProps<RouterId>> = ({ match: { params }, history }) => {

  const [edit, setEdit] = useState(false)

  const detailDs = useDataSet(detailPageFactory, 'demandManageDetail');

  const detailTableDs = useDataSet(detailTable, 'demandManageTable');

  useEffect(() => {
    if (params.id === 'create') {
      detailDs.create({ instructionDocStatus: 'RELEASED' }, 0)
      setEdit(true)
    } else {
      detailDs.setQueryParameter('instructionDocId', params.id);
      detailDs.query()
      detailTableDs.setQueryParameter('instructionDocId', params.id);
      detailTableDs.query()
    }
    return () => {
      detailDs.loadData([])
      detailTableDs.loadData([])
    }
  }, [params]);


  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={handleAdd}
          disabled={!edit}
          funcType="flat"
          shape="circle"
          size="small"
        />
      ),
      name: 'editColumn',
      align: ColumnAlign.center,
      lock: ColumnLock.left,
      width: 80,
      hideable: false,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            disabled={!(edit && !record?.get('instructionDocLineId'))}
            type="c7n-pro"
            icon="remove"
            funcType="flat"
            shape="circle"
            size="small"
          />
        </Popconfirm>
      ),
    },
    {
      name: 'lineNumber',
      width: 80,
    },
    {
      name: 'materialCode',
      editor: record => record.get('editing'),
      width: 150
    },
    {
      name: 'materialName',
      width: 230
    },
    {
      name: 'quantity',
      editor: edit
    },
    {
      name: 'uomCode',
    },
    {
      name: 'lineStatus',
    },
  ]



  const handleAdd = () => {
    let maxLineNumber = 0;
    detailTableDs.forEach(record => {
      if (record.get('lineNumber') > maxLineNumber) {
        maxLineNumber = record.get('lineNumber');
      }
    });
    detailTableDs.create({ editing: true, lineNumber: Number(maxLineNumber) + 10, }, 0);
  };
  // 删除表格某一行的回调
  const deleteRecord = async record => {
    if (record.get('logisticsRelationId')) {
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-logistics-orders/del/ui`;
      const res: any = await axios.post(url, {
        logisticsRelationId: record.get('logisticsRelationId')
      })
      if (res && res.success) {
        notification.success({});
        detailTableDs.remove(record);
      } else {
        notification.error({
          message: res.message,
        });
      }
    } else {
      detailTableDs.remove(record);
    }
  };

  const onHandleSave = async () => {
    const validate = await detailDs.validate()
    if (detailTableDs.toData().length > 0) {
      const validateTable = await detailTableDs.validate()
      if (validate && validateTable) {
        const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-demand-manage-platform/save`;
        const res: any = await axios.post(url,
          {
            ...detailDs.toData()[0],
            lineDTOList: detailTableDs.toData()
          }
        )
        if (res && res.success) {
          if (params.id === 'create') {
            history.push(`/ass/demandManage/detail/${res.rows}`)
          } else {
            detailDs.setQueryParameter('instructionDocId', res.rows);
            detailTableDs.setQueryParameter('instructionDocId', res.rows);
            detailDs.query()
            detailTableDs.query()
          }
          notification.success({});
          setEdit(false);

        } else {
          notification.error({
            message: res.message,
          });
        }
      }
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.title.requireLine`).d('请至少输入一条行信息')
      });
    }
  }

  const onHandleEdit = () => {
    setEdit(true)
  }
  const onHandleCancel = () => {
    if (params.id === 'create') {
      history.push(`/ass/demandManage/list`)
    } else {
      setEdit(false)
      detailDs.reset()
      detailTableDs.reset()
    }
  }
  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.demandManageDetail`).d('售后需求明细')}
        backPath="/ass/demandManage/list"
      >
        {edit ? (
          <div>
            <Button onClick={() => onHandleCancel()}>
              {intl.get(`${modelPrompt}.title.cancel`).d('取消')}
            </Button>
            <Button color={ButtonColor.primary} onClick={onHandleSave}>
              {intl.get(`${modelPrompt}.title.save`).d('保存')}
            </Button>
          </div>
        ) : (
          <>
            <Button color={ButtonColor.primary} onClick={() => onHandleEdit()}>
              {intl.get(`${modelPrompt}.title.edit`).d('编辑')}
            </Button>
          </>
        )}

      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['1']}>
          <Panel key="1" header={intl.get(`${modelPrompt}.title.demandManageInfo`).d('售后需求信息')}>
            <Row type="flex" align="top" justify="space-between">
              <Col span={20}>
                <Form dataSet={detailDs} columns={3} labelWidth={112}>
                  <TextField name="instructionDocNum" disabled={!edit} />
                  <Select name="instructionDocType" disabled={params.id !== 'create'} />
                  <Lov name="siteCode" disabled={params.id !== 'create'} />
                  <Lov name="locatorCode" disabled={!edit} />
                  <DateTimePicker name="demandTime" disabled={!edit} />
                  <Select name="instructionDocStatus" disabled={!edit} />
                </Form>
              </Col>
            </Row>
          </Panel>
        </Collapse>
        <Collapse bordered={false} defaultActiveKey={['2']}>
          <Panel key="2" header={intl.get(`${modelPrompt}.title.demandManageLineInfo`).d('售后需求行信息')}>
            <Table
              dataSet={detailTableDs}
              columns={columns}
              key="demandManageTable"
              rowHeight={35}
              queryBar={TableQueryBarType.none}
              queryFieldsLimit={4} // 头部显示的查询字段的数量
              searchCode="demandManageTable" // 动态筛选条后端接口唯一编码
              customizedCode="demandManageTable" // 个性化编码
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default DetailPage;
