/* * @Author: xu xiaoyu
* @Date: 2023-06-26 15:00:24
 * @Last Modified by: xu xiaoyu
 * @Last Modified time: 2023-06-26 15:04:098
*/
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

// const Host = `/yp-wms-33139`
const Host = `${BASIC.HMES_BASIC}`

const modelPrompt = 'tarzan.wms.DeliveryRoutesAndPersonnelRelationships';
const tenantId = getCurrentOrganizationId();

const headerTableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: 'multiple',
  transport: {
    read: ({ data }) => {
      return {
        url: `${Host}/v1/${tenantId}/wms-pick-persons/list`,
        method: 'GET',
        data: {
          ...data,
        },
      };
    },
  },
  queryFields: [
    {
      name: 'siteLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
      noCache: true,
    },
    {
      name: 'siteId',
      type: 'number',
      bind: 'siteLov.siteId',
    },
    {
      name: 'fromWarehouseLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.fromWarehouseLov`).d('来源仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: 'always',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            locatorCategory: 'AREA',
          };
        },
      },
    },
    {
      name: 'fromWareheouseId',
      type: 'number',
      bind: 'fromWarehouseLov.locatorId',
    },
    {
      name: 'toLocatorCodeLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.toLocatorCodeLov`).d('目标货位'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: 'always',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
      },
    },
    {
      name: 'toLocatorId',
      type: 'string',
      bind: 'toLocatorCodeLov.locatorId',
    },
  ],
  fields: [
    {
      name: 'siteLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: 'always',
      noCache: true,
    },
    {
      name: 'siteId',
      type: 'number',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: 'string',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'fromWarehouseLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.fromWarehouseLov`).d('来源仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: 'always',
      required: true,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            locatorCategory: 'AREA',
          };
        },
      },
    },
    {
      name: 'fromWareheouseId',
      type: 'number',
      bind: 'fromWarehouseLov.locatorId',
    },
    {
      name: 'fromWareheouseCode',
      type: 'string',
      bind: 'fromWarehouseLov.locatorCode',
      label: intl.get(`${modelPrompt}.fromWareheouseCode`).d('来源仓库'),
    },
    {
      name: 'fromWareheouseName',
      type: 'string',
      bind: 'fromWarehouseLov.locatorName',
      label: intl.get(`${modelPrompt}.fromWareheouseName`).d('仓库描述'),
    },
    {
      name: 'toLocatorCodeLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.toLocatorCodeLov`).d('目标货位'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      textField: 'locatorCode',
      valueField: 'locatorId',
      ignore: 'always',
      required: true,
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
          };
        },
      },
    },
    {
      name: 'toLocatorId',
      type: 'number',
      bind: 'toLocatorCodeLov.locatorId',
    },
    {
      name: 'toLocatorCode',
      type: 'string',
      bind: 'toLocatorCodeLov.locatorCode',
    },
    {
      name: 'toLocatorName',
      type: 'string',
      bind: 'toLocatorCodeLov.locatorName',
      label: intl.get(`${modelPrompt}.toLocatorName`).d('货位描述'),
    },
    {
      name: 'loginName',
      type: 'string',
      label: intl.get(`${modelPrompt}.loginName`).d('登陆账号'),
    },
    {
      name: 'userOrgLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.userOrgLov`).d('拣配人员'),
      lovCode: 'MT.USER.ORG',
      ignore: 'always',
      required: true,
      textField: 'realName',
      valueField: 'id',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId,
            locatorCategory: 'AREA',
          };
        },
      },
    },
    {
      name: 'personId',
      type: 'number',
      bind: 'userOrgLov.id',
    },
    {
      name: 'personName',
      type: 'string',
      bind: 'userOrgLov.loginName',
      label: intl.get(`${modelPrompt}.personName`).d('拣配人员'),
    },
    {
      name: 'enableFlag',
      type: 'boolean',
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'lastUpdatedByName',
      type: 'string',
      label: intl.get(`${modelPrompt}.lastUpdateByName`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: 'string',
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
});


export { headerTableDS };
