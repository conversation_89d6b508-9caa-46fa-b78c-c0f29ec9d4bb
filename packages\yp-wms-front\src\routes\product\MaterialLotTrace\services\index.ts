/**
 * @Description: 物料批管理平台-services
 * @Author: <<EMAIL>>
 * @Date: 2022-01-18 20:19:41
 * @LastEditTime: 2022-12-14 16:02:35
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 物料批管理平台-判断物料是否开启版本
export function FetchMaterialLotDetail() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/material-lot/detail/ui`,
    method: 'GET',
  };
}

// 物料批管理平台-获取到期日期
export function FetchExpirationDate() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/expiration-data/calculate/ui`,
    method: 'GET',
  };
}

// 物料批管理平台-保存
export function SaveMaterialLotDetail() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/material-lot/save/ui`,
    method: 'POST',
  };
}

// 物料批管理平台-保存
export function FetchDynamicColumn(tableName) {
  return {
    url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-extend-setting/limit-table/ui?mainTable=${tableName}`,
    method: 'GET',
  };
}
