/**
 * @Description: 物料批管理平台-详情页
 * @Author: <<EMAIL>>
 * @Date: 2021-03-01 10:47:34
 * @LastEditTime: 2022-12-14 16:03:33
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
  Button,
  Form,
  TextField,
  CheckBox,
  Table,
  DataSet,
  Lov,
  Select,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Spin, Collapse, Popconfirm, notification } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import uuid from 'uuid/v4';
import intl from 'utils/intl';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/lib/table';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import { queryMapIdpValue } from 'services/api';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import UploadModal from '../../Component/Upload';
import { formDS, packagingTypeDS, truckInfoDS, supplierPackageListDS } from '../../stores/DetailDs';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'hwms.packageProposalQuery';

const PackageProposalDetail = props => {
  const {
    history,
    match: { params: { id } },
  } = props;

  const [canEdit, setCanEdit] = useState(false); // 判断是否点击可编辑按钮
  const [statusEdit, setStatusEdit] = useState(false); // 根据状态判断是否可编辑
  const [conEdit, setConEdit] = useState(false); // 控制托盘外尺寸是否可编辑
  const [packageTypeList, setPackageTypeList] = useState([]);

  const fetchPackageDetail = useRequest({
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-package-proposals/query/headDetil/ui`,
    method: 'get',
  }, {
    manual: true,
  });

  const savePackageDetail = useRequest({
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-package-proposals/save`,
    method: 'POST',
  }, {
    manual: true,
  });

  const formDs = useMemo(() => new DataSet(formDS()), []);
  const packagingTypeDs = useMemo(() => new DataSet(packagingTypeDS()), []);
  const truckInfoDs = useMemo(() => new DataSet(truckInfoDS()), []);
  const supplierPackageListDs = useMemo(() => new DataSet(supplierPackageListDS()), []);

  useEffect(() => {
    if (id !== 'create') {
      handleFetchDetail(id);
    } else {
      formDs.create({
        picture1: uuid(),
        picture2: uuid(),
        picture3: uuid(),
        picture4: uuid(),
      });
      packagingTypeDs.create({});
      truckInfoDs.create({});
      supplierPackageListDs.create({
        picture: uuid(),
      });
      setCanEdit(true);
      setStatusEdit(true);
    }
  }, [id]);

  useEffect(() => {
    handleFetchPackageTypeList();
  }, []);

  const handleFetchDetail = (currentId) => {
    supplierPackageListDs.setQueryParameter('packageProposalId', currentId);
    supplierPackageListDs.query();
    return fetchPackageDetail.run({
      params: {
        packageProposalId: currentId,
      },
      onSuccess: (res) => {
        if(res) {
          setCanEdit(false);
          const {
            truckCode,
            axlesQty,
            ton,
            truckL,
            truckW,
            truckH,
            mantissa,
            palletQtyL,
            palletQtyW,
            palletQtyH,
            loadQty,
            packageType,
            boxL,
            boxW,
            boxH,
            capacity,
            materialWeight,
            boxWeight,
            sumWeight,
            labelPosition,
            boxQty,
            conL,
            conW,
            conH,
            eachPackage,
            dustCover,
            internalItem,
            ...formInfo
          } = res;
          formDs.loadData([formInfo]);
          packagingTypeDs.loadData([{
            packageType,
            boxL,
            boxW,
            boxH,
            capacity,
            materialWeight,
            boxWeight,
            sumWeight,
            labelPosition,
            boxQty,
            conL,
            conW,
            conH,
            eachPackage,
            dustCover,
            internalItem,
          }]);
          truckInfoDs.loadData([{
            truckCode,
            axlesQty,
            ton,
            truckL,
            truckW,
            truckH,
            mantissa,
            palletQtyL,
            palletQtyW,
            palletQtyH,
            loadQty,
          }]);
          handleFetchStatusList(res.status);
        } 
      },
    });
  };

  const handleFetchPackageTypeList = () => {
    queryMapIdpValue({
      packageTypeList: 'WMS.PACKAGE_TYPE',
    }).then(res => {
      if(res) {
        setPackageTypeList(res.packageTypeList);
      }
    });
  };

  const handleFetchStatusList = (status) => {
    queryMapIdpValue({
      statusList: 'WMS.PACKAGE_PROPOSAL_STATUS',
    }).then(res => {
      if(res) {
        setStatusEdit(res.statusList?.find(e => e.value === status)?.tag?.includes('E'));
      }
    });
  };

  const handleEdit = (flag) => {
    if(!flag) {
      handleFetchDetail(id);
    }
    setCanEdit(flag);
  }

  const handleValidateImage = () => {
    return new Promise((resolve, reject) => {
      if ((formDs.current?.get('file1ListLength') || 0) <= 0) {
        notification.warning({ description: intl.get(`${modelPrompt}.message.file1Message`).d('图一、零件图片请至少上传一张图片')});
        return reject(new Error(false));
      }
      if ((formDs.current?.get('file1ListLength') || 0) <= 0) {
        notification.warning({ description: intl.get(`${modelPrompt}.message.file1Message`).d('图二、零件在周转箱/料架摆放图片 (俯视图）请至少上传一张图片')});
        return reject(new Error(false));
      }
      if ((formDs.current?.get('file1ListLength') || 0) <= 0) {
        notification.warning({ description: intl.get(`${modelPrompt}.message.file1Message`).d('图三、周转箱或料架侧视图请至少上传一张图片')});
        return reject(new Error(false));
      }
      if ((formDs.current?.get('file1ListLength') || 0) <= 0) {
        notification.warning({ description: intl.get(`${modelPrompt}.message.file1Message`).d('图四、周转箱+托盘组托图片请至少上传一张图片')});
        return reject(new Error(false));
      }
      if(supplierPackageListDs.toJSONData().some(e => (e.fileListLength || 0) <= 0 )) {
        notification.warning({ description: intl.get(`${modelPrompt}.message.file1Message`).d('供应商包装清单请至少上传一张图片')});
        return reject(new Error(false));
      }
      return resolve(true);
    })
  };

  const handleSave = async () => {
    Promise.all([
      formDs.validate(),
      packagingTypeDs.validate(),
      truckInfoDs.validate(),
      supplierPackageListDs.validate(),
      handleValidateImage(),
    ]).then(res => {
      const [formDsValidate, packagingTypeDsValidate, truckInfoDsValidate, supplierPackageListDsValidate, imageValidate] = res;
      if(formDsValidate && packagingTypeDsValidate && truckInfoDsValidate && supplierPackageListDsValidate && imageValidate) {
        savePackageDetail.run({
          params: {
            ...formDs.toData()[0],
            ...packagingTypeDs.toData()[0],
            ...truckInfoDs.toData()[0],
            lineList: supplierPackageListDs.toData(),
          },
          onSuccess: (res) => {
            if(res) {
              formDs.loadData([]);
              if(id === 'create') {
                history.push(`/hwms/packaging-proposal/management/detail/${res}`);
              }
              handleFetchDetail(res);
            } 
          },
        });
      }
    })
  };

  const handleChangePackageType = useCallback((record) => {
    if(record.get('packageType') && packageTypeList?.find(e => e.value === record?.get('packageType'))?.tag?.includes('Y')) {
      packagingTypeDs.getField('conL')?.set('required', false);
      packagingTypeDs.getField('conH')?.set('required', false);
      packagingTypeDs.getField('conW')?.set('required', false);
      setConEdit(false);
    } else {
      packagingTypeDs.getField('conL')?.set('required', true);
      packagingTypeDs.getField('conH')?.set('required', true);
      packagingTypeDs.getField('conW')?.set('required', true);
      setConEdit(true);
    }
    record.set('conL', null);
    record.set('conH', null);
    record.set('conW', null);
  }, [packageTypeList]);

  const packageTypeColumns: ColumnProps = useMemo(() => [
    {
      name: 'packageType',
      editor: (record) => canEdit && (
        <Select onChange={() => handleChangePackageType(record)} />
      ),
    },
    {
      title: intl.get(`${modelPrompt}.boxSize`).d('箱外尺寸'),
      children: [
        {
          name: 'boxL',
          editor: canEdit,
        },
        {
          name: 'boxW',
          editor: canEdit,
        },
        {
          name: 'boxH',
          editor: canEdit,
        },
      ],
    },
    {
      name: 'capacity',
      editor: canEdit,
    },
    {
      title: intl.get(`${modelPrompt}.weight`).d('重量'),
      children: [
        {
          name: 'materialWeight',
          editor: canEdit,
        },
        {
          name: 'boxWeight',
          editor: canEdit,
        },
        {
          name: 'sumWeight',
          editor: canEdit,
        },
      ],
    },
    {
      name: 'labelPosition',
      editor: canEdit,
    },
    {
      name: 'boxQty',
      editor: canEdit,
    },
    {
      title: intl.get(`${modelPrompt}.containerSize`).d('托盘外尺寸'),
      children: [
        {
          name: 'conL',
          editor: conEdit && canEdit,
        },
        {
          name: 'conW',
          editor: conEdit && canEdit,
        },
        {
          name: 'conH',
          editor: conEdit && canEdit,
        },
      ],
    },
    {
      name: 'eachPackage',
      editor: canEdit && <CheckBox />,
    },
    {
      name: 'dustCover',
      editor: canEdit && <CheckBox />,
    },
    {
      name: 'internalItem',
      editor: canEdit && <CheckBox />,
    },
  ], [canEdit, conEdit]);

  const truckInfoColumns: ColumnProps = [
    {
      name: 'truckCode',
      editor: canEdit,
    },
    {
      name: 'axlesQty',
      editor: canEdit,
    },
    {
      name: 'ton',
      editor: canEdit,
    },
    {
      name: 'truckL',
      editor: canEdit,
    },
    {
      name: 'truckW',
      editor: canEdit,
    },
    {
      name: 'truckH',
      editor: canEdit,
    },
    {
      name: 'mantissa',
      editor: canEdit,
    },
    {
      title: intl.get(`${modelPrompt}.containerQty`).d('托盘放置数量[mm]'),
      children: [
        {
          name: 'palletQtyL',
          editor: canEdit,
        },
        {
          name: 'palletQtyW',
          editor: canEdit,
        },
        {
          name: 'palletQtyH',
          editor: canEdit,
        },
      ],
    },
    {
      name: 'loadQty',
      editor: canEdit,
    },
  ];

  const handleAdd = () => {
    supplierPackageListDs.create({
      picture: uuid(),
    });
  };

  const supplierPackageListColumns: ColumnProps = [
    {
      header: () => (
        <Button
          icon="add"
          disabled={!canEdit}
          funcType={FuncType.flat}
          onClick={handleAdd}
          size={Size.small}
        />
      ),
      align: ColumnAlign.center,
      width: 70,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => {
            supplierPackageListDs.remove(record);
          }}
        >
          <Button icon="remove" disabled={!canEdit} funcType={FuncType.flat} size={Size.small} />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'packageName',
      editor: canEdit,
    },
    {
      name: 'picture',
      renderer: ({ record }) => (
        <UploadModal
          bucketName="wms-package"
          viewOnly={!canEdit}
          btnText={intl.get('hzero.common.upload.modal.title').d('附件')}
          attachmentUUID={record.get('picture')}
          showReUploadIcon={false}
          onReUpload={false}
          onChangeFile={(attachmentUuid, fileList) => {
            record?.set('fileListLength', fileList.length);
          }}
          fileType="image/webp,image/svg,image/png,image/gif,image/jpg,image/jpeg,image/bmp"
        />
      ),
    },
    {
      name: 'emptyFlag',
      editor: canEdit && <CheckBox />,
    },
  ];

  return (
    <div className="hmes-style">
      <Spin spinning={fetchPackageDetail.loading || savePackageDetail.loading}>
        <Header
          title={intl.get(`${modelPrompt}.packageProposalDetail`).d('包装提案明细')}
          backPath="/hwms/packaging-proposal/management/list"
        >
          {canEdit && statusEdit && (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              {id !== 'create' && (
                <Button color={ButtonColor.primary} icon="cancel" onClick={() => handleEdit(false)}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </Button>
              )}
            </>
          )}
          {!canEdit && statusEdit && (
            <>
              <Button color={ButtonColor.primary} icon="bianji" onClick={() => handleEdit(true)}>
                {intl.get('tarzan.common.button.edit').d('编辑')}
              </Button>
            </>
          )}
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'baseInfo',
              'concatInfo',
              'packageTypeInfo',
              'truckInfo',
              'supplierPackageListInfo',
              'pictureInfo',
            ]}
          >
            <Panel
              header={intl.get(`${modelPrompt}.baseInfo`).d('包装提案')}
              key="baseInfo"
            >
              <Form labelWidth={112} disabled={!canEdit} dataSet={formDs} columns={4}>
                <TextField name="battery" />
                <Lov name="materialLov" disabled={id !== 'create'} />
                <TextField name="materialName" disabled />
                <Lov name="supplierLov" disabled={id !== 'create'} />
                <TextField name="supplierName" disabled />
                <Select name="materialOwnership" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.concatInfo`).d('联系方式')}
              key="concatInfo"
            >
              <Form labelWidth={112} disabled={!canEdit} dataSet={formDs} columns={4}>
                <TextField name="department" />
                <DateTimePicker name="startDate" />
                <TextField name="tel" />
                <TextField name="eml" />
                <TextField name="minister" />
                <TextField name="management" />
                <TextField name="engineer" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.packageTypeInfo`).d('包装种类')}
              key="packageTypeInfo"
            >
              <Table border columns={packageTypeColumns} dataSet={packagingTypeDs} />
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.truckInfo`).d('装车信息')}
              key="truckInfo"
            >
              <Table border columns={truckInfoColumns} dataSet={truckInfoDs} />
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.supplierPackageListInfo`).d('供应商包装清单与是否返空')}
              key="supplierPackageListInfo"
              dataSet={supplierPackageListDs}
            >
              <Table border columns={supplierPackageListColumns} dataSet={supplierPackageListDs} />
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.pictureInfo`).d('实物照片:[零件图片、零件在周转箱/料架摆放图片 (俯视图）、周转箱或料架侧视图、周转箱+托盘组托图片]')}
              key="pictureInfo"
            >
              <Form labelWidth={112} disabled={!canEdit} dataSet={formDs} columns={4}>
                <UploadModal
                  bucketName="wms-package"
                  viewOnly={!canEdit}
                  btnText={intl.get(`${modelPrompt}.button.attachmentBtn1`).d('图一、零件图片')}
                  attachmentUUID={formDs.current?.get('picture1')}
                  showReUploadIcon={false}
                  onReUpload={false}
                  onChangeFile={(attachmentUuid, fileList) => {
                    formDs.current?.set('file1ListLength', fileList.length);
                  }}
                  fileType="image/webp,image/svg,image/png,image/gif,image/jpg,image/jpeg,image/bmp"
                />
                <UploadModal
                  bucketName="wms-package"
                  viewOnly={!canEdit}
                  btnText={intl.get(`${modelPrompt}.button.attachmentBtn2`).d('图二、零件在周转箱/料架摆放图片 (俯视图）')}
                  attachmentUUID={formDs.current?.get('picture2')}
                  showReUploadIcon={false}
                  onReUpload={false}
                  onChangeFile={(attachmentUuid, fileList) => {
                    formDs.current?.set('file2ListLength', fileList.length);
                  }}
                  fileType="image/webp,image/svg,image/png,image/gif,image/jpg,image/jpeg,image/bmp"
                />
                <UploadModal
                  bucketName="wms-package"
                  viewOnly={!canEdit}
                  btnText={intl.get(`${modelPrompt}.button.attachmentBtn3`).d('图三、周转箱或料架侧视图')}
                  attachmentUUID={formDs.current?.get('picture3')}
                  showReUploadIcon={false}
                  onReUpload={false}
                  onChangeFile={(attachmentUuid, fileList) => {
                    formDs.current?.set('file3ListLength', fileList.length);
                  }}
                  fileType="image/webp,image/svg,image/png,image/gif,image/jpg,image/jpeg,image/bmp"
                />
                <UploadModal
                  bucketName="wms-package"
                  viewOnly={!canEdit}
                  btnText={intl.get(`${modelPrompt}.button.attachmentBtn4`).d('图四、周转箱+托盘组托图片')}
                  attachmentUUID={formDs.current?.get('picture4')}
                  showReUploadIcon={false}
                  onReUpload={false}
                  onChangeFile={(attachmentUuid, fileList) => {
                    formDs.current?.set('file4ListLength', fileList.length);
                  }}
                  fileType="image/webp,image/svg,image/png,image/gif,image/jpg,image/jpeg,image/bmp"
                />
                <TextField name="explain1" />
                <TextField name="explain2" />
                <TextField name="explain3" />
                <TextField name="explain4" />
              </Form>
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: ['hwms.packageProposalManagement', 'tarzan.common'],
})(PackageProposalDetail);
