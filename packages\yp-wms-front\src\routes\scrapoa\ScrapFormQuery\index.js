// 报废单查询
import React, { Component, Fragment } from 'react';
import { DataSet, Table, Modal, Button } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { isNil } from 'lodash';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { headTableDS, lineTableDS, detailTableDS } from './stores/ScrapFormQueryDS';

const Panel = Collapse.Panel;
const modelPrompt = 'tarzan.scrapoa.ScrapFormQuery';
let modalAssembly;

@formatterCollections({ code: ['tarzan.scrapoa.ScrapFormQuery', 'tarzan.common'] })
export default class ScrapFormQuery extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.headTableDs = new DataSet(headTableDS()); // 头表格dateSet
    this.lineTableDs = new DataSet(lineTableDS()); // 行表格dateSet
    this.detailTableDs = new DataSet(detailTableDS()); // 明细表格dateSet
  }

  componentDidMount() {
    const {
      match: { params },
    } = this.props;
    if (params && params.code) {
      this.headTableDs.setQueryParameter('instructionDocNum', params.code);
      // this.headTableDs.setQueryParameter('siteId', 62709001);
    }
    this.headTableDs.query().then(res => {
      if (res) {
        if (res.rows && res.rows.content && res.rows.content.length > 0) {
          const instructionDocId = res.rows.content[0].instructionDocId;
          this.lineTableDs.setQueryParameter('instructionDocId', instructionDocId);
          this.lineTableDs.query();
        }
      }
    });
  }

  // 查询行
  headerRowClick = record => {
    const instructionDocId = record.get('instructionDocId');
    this.lineTableDs.setQueryParameter('instructionDocId', instructionDocId);
    this.lineTableDs.query();
  };

  // 查询物料批明细
  handleMaterialLotDetail = record => {
    const instructionDocLineId = record.get('instructionDocLineId');
    this.detailTableDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
    this.detailTableDs.query();
    const detailColumns = [
      { name: 'materialLotCode', width: 150, align: 'center' },
      { name: 'materialLotStatusDesc', align: 'center' },
      { name: 'containerCode', align: 'center' },
      { name: 'primaryUomQty', align: 'center' },
      { name: 'uomCode', align: 'center' },
      { name: 'lot', align: 'center' },
    ];
    modalAssembly = Modal.open({
      title: intl.get(`${modelPrompt}.title.materialLot.detail`).d('物料批明细'),
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <Table
          dataSet={this.detailTableDs}
          columns={detailColumns}
          customizedCode="detailScrapFormQuery"
        />
      ),
      footer: <Button onClick={() => modalAssembly.close()}>{intl.get(`${modelPrompt}.button.back`).d('返回')}</Button>,
    });
  };

  // 操作列渲染
  optionRender = record => (
    <>
      <a
        style={{ marginRight: '8px' }}
        onClick={() => {
          this.handleMaterialLotDetail(record);
        }}
      >
       {intl.get(`${modelPrompt}.materialLot.detail`).d('物料批明细')}
      </a>
    </>
  );

  getExportQueryParams = () => {
    if (!this.headTableDs.queryDataSet || !this.headTableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = this.headTableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  render() {
    const headColumns = [
      { name: 'siteCode', align: 'center' },
      { name: 'instructionDocNum', align: 'center' },
      { name: 'instructionDocStatusDesc', align: 'center' },
      { name: 'costcenterCode', align: 'center' },
      { name: 'realName', align: 'center' },
      { name: 'creationDate', align: 'center' },
    ];
    const lineColumns = [
      { name: 'lineNumber', align: 'center' },
      { name: 'lineStatusDesc', align: 'center' },
      { name: 'materialCode', align: 'center' },
      { name: 'materialName', align: 'center' },
      { name: 'quantity', align: 'center' },
      { name: 'uomCode', align: 'center' },
      { name: 'sumActualQty', align: 'center' },
      { name: 'fromLocatorCode', align: 'center' },
      { name: 'toLocatorCode', align: 'center' },
      {
        name: 'option',
        fixed: 'right',
        lock: 'right',
        width: 120,
        align: 'center',
        renderer: ({ record }) => this.optionRender(record),
      },
    ];
    return (
      <Fragment>
        <Header title={intl.get(`${modelPrompt}.title.parentTable`).d("报废执行")}></Header>
        <Content>
          <Table
            dataSet={this.headTableDs}
            columns={headColumns}
            onRow={({ record }) => {
              return {
                onClick: () => {
                  this.headerRowClick(record);
                },
              };
            }}
          />
          <Collapse defaultActiveKey={['1']}>
            <Panel header={intl.get(`${modelPrompt}.title.lineTable`).d("行表格")} key="1">
              <Table
                dataSet={this.lineTableDs}
                columns={lineColumns}
                customizedCode="lineScrapFormQuery"
              />
            </Panel>
          </Collapse>
        </Content>
      </Fragment>
    );
  }
}
