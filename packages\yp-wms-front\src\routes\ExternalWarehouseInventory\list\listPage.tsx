import React, { FC, useEffect, useState } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, Modal, TextField, Select, Form } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { useDataSetEvent, } from 'utils/hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import ExcelExportPro from 'components/ExcelExportPro';
import ExcelExport from 'components/ExcelExport';
import { BASIC, API_HOST } from '@utils/config';
import listPageFactory from '../stores/listPageDs';
import outInPageFactory from '../stores/outInPageDs';
import historyFactory from '../stores/historyPageDs'
import axios from 'axios';
import outDeliverFactory from '../stores/outDeliverDs';

interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
  outInPageDs: DataSet;
  outDeliverDs: DataSet;
  historyDs: DataSet;
}

const tenantId = getCurrentOrganizationId()

const modelPrompt = 'tarzan.ass.externalWarehouseInventory';

const ListPageComponent: FC<ListPageProps> = ({ listDs, outInPageDs, outDeliverDs, historyDs, }) => {

  const [selectLength, setSelectLength] = useState(0)

  useEffect(() => {
    listDs.query();
  }, []);

  useDataSetEvent(listDs, 'load', () => {
    if (listDs.selected.length === 0) {
      setSelectLength(0)
    }
  });

  useDataSetEvent(listDs, 'select', () => {
    selectStatus()
  });

  useDataSetEvent(listDs, 'selectAll', () => {
    selectStatus()
  });

  useDataSetEvent(listDs, 'unSelectAll', () => {
    selectStatus()
  });

  useDataSetEvent(listDs, 'unselect', () => {
    selectStatus()
  });

  const selectStatus = () => {
    setSelectLength(listDs.selected.length)
  }

  const columns: ColumnProps[] = [
    {
      name: 'supplierCode',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'materialCode',
      width: 150
    },
    {
      name: 'materialName',
      width: 150
    },
    {
      name: 'warehouseCode',
    },
    {
      name: 'qualifiedQuantity',
      width: 80
    },
    {
      name: 'unQualifiedQuantity',
      width: 100
    },
    {
      name: 'lastUpdatedByName',
      width: 100
    },
    {
      name: 'lastUpdateDate',
      width: 150
    },
  ];

  const outInColumns: ColumnProps[] = [
    {
      name: 'supplierCode',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'warehouseCode',
    },
    {
      name: 'qualifiedQuantity',
      editor: true
    },
    {
      name: 'unQualifiedQuantity',
      editor: true
    },
  ];

  const outDeliverColumns: ColumnProps[] = [
    {
      name: 'instructionDocNum',
    },
    {
      name: 'lineNumber',
    },
    {
      name: 'supplierCode',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'qty',
    },
  ]

  const historyColumn: ColumnProps[] = [
    {
      name: 'eventTypeCode',
    },
    {
      name: 'eventTypeDesc',
    },
    {
      name: 'eventUserName',
    },
    {
      name: 'eventTime',
    },
    {
      name: 'supplierCode',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'materialCode',
      width: 150
    },
    {
      name: 'materialName',
      width: 150
    },
    {
      name: 'warehouseCode',
    },
    {
      name: 'inQualifiedQuantity',
    },
    {
      name: 'inUnQualifiedQuantity',
    },
    {
      name: 'outQualifiedQuantity',
    },
    {
      name: 'outUnQualifiedQuantity',
    },
  ]

  const onHandleInOut = async (type) => {
    if (type === 'IN') {
      outInPageDs.getField('qualifiedQuantity')?.set('label', intl.get(`${modelPrompt}.form.inQualifiedQuantity`).d('合格入库数'))
      outInPageDs.getField('unQualifiedQuantity')?.set('label', intl.get(`${modelPrompt}.form.inUnQualifiedQuantity`).d('不合格入库数'))
    } else {
      outInPageDs.getField('qualifiedQuantity')?.set('label', intl.get(`${modelPrompt}.form.outQualifiedQuantity`).d('合格出库数'))
      outInPageDs.getField('unQualifiedQuantity')?.set('label', intl.get(`${modelPrompt}.form.outUnQualifiedQuantity`).d('不合格出库数'))
    }
    outInPageDs.loadData(listDs.selected.map(item => ({ ...item.toData(), qualifiedQuantity: null, unQualifiedQuantity: null })))
    Modal.open({
      title: type === 'IN' ? intl.get(`${modelPrompt}.title.inWarehouse`).d('入库') : intl.get(`${modelPrompt}.title.outWarehouse`).d('出库'),
      destroyOnClose: true,
      drawer: false,
      style: { width: '1080px' },
      closable: true,
      children: <Table
        dataSet={outInPageDs}
        columns={outInColumns}
        key="qualifiedQuantity"
        queryBar={TableQueryBarType.none}
        searchCode="qualifiedQuantity" // 动态筛选条后端接口唯一编码
        customizedCode="qualifiedQuantity" // 个性化编码
      />,
      onOk: async () => {
        const validate = await outInPageDs.validate()
        if (validate) {
          const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-external-inventory-manage/inOrOut`;
          try {
            const res: any = await axios.post(url, {
              type,
              dtoList: outInPageDs.toData(),
            })
            if (res && res.success) {
              listDs.query()
              notification.success({});
            } else {
              notification.error({
                message: res.message,
              });
              return false
            }
          } catch (error) {
            notification.error({
              message: error,
            });
            return false
          }
        } else {
          return false
        }
      },
    })
  }

  const onHandleDeliverQuery = (instructionDocNum) => {
    outDeliverDs.setQueryParameter('instructionDocNum', instructionDocNum)
    outDeliverDs.query()
  }

  const onHandleDeliver = () => {
    Modal.open({
      title: intl.get(`${modelPrompt}.title.outDeliverWarehouse`).d('送货单出库'),
      destroyOnClose: true,
      drawer: false,
      style: { width: '1080px' },
      closable: true,
      children: <div>
        <Form columns={3} labelWidth={121} dataSet={outDeliverDs.queryDataSet}>
          <TextField name='instructionDocNum' onChange={(val) => onHandleDeliverQuery(val)} />
          <Select name='warehouseCode' />
        </Form>
        <Table
          dataSet={outDeliverDs}
          columns={outDeliverColumns}
          key="outDeliverWarehouse"
          queryBar={TableQueryBarType.none}
          searchCode="outDeliverWarehouse" // 动态筛选条后端接口唯一编码
          customizedCode="outDeliverWarehouse" // 个性化编码
        />
      </div>,
      onOk: async () => {
        const { warehouseCode }: any = outDeliverDs.queryDataSet?.toData()[0] || {}
        if (warehouseCode) {
          if (outDeliverDs.toData().length) {
            const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-external-inventory-manage/deliveryDocOut`;
            try {
              const res: any = await axios.post(url, {
                warehouseCode,
                dtoList: outDeliverDs.toData(),
              })
              if (res && res.success) {
                notification.success({});
                listDs.query()
              } else {
                notification.error({
                  message: res.message,
                });
                return false
              }
            } catch (error) {
              notification.error({
                message: error,
              });
              return false
            }
          } else {
            notification.error({
              message: intl.get(`${modelPrompt}.title.noData`).d('没有可操作的数据，请检查！')
            })
            return false
          }
        } else {
          notification.error({
            message: intl.get(`${modelPrompt}.title.selectWarehouse`).d('请选择出库仓库')
          })
          return false
        }
      },
    })
  }

  const getQueryParamsHistory = () => {
    return {
      idList: listDs.selected.map(item => item.get('manageId')).join(',')
    }
  }

  const buttons = [
    <ExcelExport
      method="GET"
      exportAsync
      requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/wms-external-inventory-manage/his/export`}
      queryParams={getQueryParamsHistory}
      buttonText={intl.get(`${modelPrompt}.`).d('导出')}
    />]

  const onHandleHistory = () => {
    const historyIds = listDs.selected.map(item => item.get('manageId'))
    historyDs.setQueryParameter('historyIds', historyIds)
    historyDs.query()
    Modal.open({
      title: intl.get(`${modelPrompt}.title.history`).d('历史查询'),
      destroyOnClose: true,
      drawer: true,
      style: { width: '1080px' },
      closable: true,
      okButton: false,
      children:
        <Table
          dataSet={historyDs}
          buttons={buttons}
          columns={historyColumn}
          key="outDeliverHistory"
          queryBar={TableQueryBarType.none}
          searchCode="outDeliverHistory" // 动态筛选条后端接口唯一编码
          customizedCode="outDeliverHistory" // 个性化编码
        />
      ,
    })
  }

  // 导入
  const handleImport = () => {
    openTab({
      key: '/himp/commentImport/WMS.EXTERNAL_INVENTORY_IMPORT',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId: tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const getQueryParams = () => {
    if (listDs.queryDataSet) {
      return listDs.queryDataSet?.toData()[0]
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.externalWarehouseInventory`).d('外仓库存管理')}>
        <Button disabled={selectLength === 0} color={ButtonColor.primary} onClick={() => onHandleHistory()}>
          {intl.get(`${modelPrompt}.title.history`).d('历史查询')}
        </Button>
        <Button onClick={() => onHandleDeliver()}>
          {intl.get(`${modelPrompt}.title.outDeliver`).d('送货单出库')}
        </Button>
        <Button disabled={selectLength === 0} onClick={() => onHandleInOut('OUT')}>
          {intl.get(`${modelPrompt}.title.outGeneral`).d('一般出库')}
        </Button>
        <Button disabled={selectLength === 0} onClick={() => onHandleInOut('IN')}>
          {intl.get(`${modelPrompt}.title.inWarehouse`).d('入库')}
        </Button>
        <Button
          icon="file_upload"
          onClick={handleImport}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </Button>
        <ExcelExportPro
          method="POST"
          allBody
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/wms-external-inventory-manage/list/export`}
          queryParams={getQueryParams}
          buttonText={intl.get(`${modelPrompt}.`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="externalWarehouseInventory"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={3} // 头部显示的查询字段的数量
          searchCode="externalWarehouseInventory" // 动态筛选条后端接口唯一编码
          customizedCode="externalWarehouseInventory" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const outInPageDs = outInPageFactory();
    const outDeliverDs = outDeliverFactory();
    const listDs = listPageFactory();
    const historyDs = historyFactory();
    return {
      listDs,
      outInPageDs,
      outDeliverDs,
      historyDs
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.ass.externalWarehouseInventory',],
})(ListPage);
