/**
 * @Description: 库存查询 - 列表页
 * @Author: <EMAIL>
 * @Date: 2022/7/6 15:40
 * @LastEditTime: 2022-11-21 13:37:41
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo } from 'react';
import { DataSet, Table, Modal, Button } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import { isNil } from 'lodash';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import { ColumnLock, TableQueryBarType, TableMode } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useRequest } from '@components/tarzan-hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC, API_HOST } from '@utils/config';
import { entranceDS } from '../stores/EntranceDS';
import { drawerHeadDS, drawerTableDS } from '../stores/ReserveDrawerDS';
import { FetchSubLocatorInfo, QueryChildList } from '../services';
import ReserveDrawer from './ReserveDrawer';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.inventory.query';
const Query = props => {
  const {
    dataSet,
  } = props;
  let _modal;

  const drawerHeadDs = useMemo(() => new DataSet(drawerHeadDS()), []);
  const drawerTableDs = useMemo(() => new DataSet(drawerTableDS()), []);
  // const [exportFlag, setExportFlag] = useState(null);

  
  // 查询库存日记账子节点数据
  const { run: queryChildList } = useRequest(QueryChildList(), {
    manual: true,
    needPromise: true,
  });
  // 查询库存日记账数据
  const { run: fetchSubLocatorInfo } = useRequest(FetchSubLocatorInfo(), {
    manual: true,
    needPromise: true,
  });

  useEffect(() => {
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    if (Object.keys(props?.location?.query).length === 0 || props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      // dataSet.query(dataSet.currentPage);
      return;
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const {
      siteId,
      siteCode,
      materialId,
      materialCode,
      revisionCode,
      lotCode,
      qualityStatus,
      ownerType,
      ownerId,
      ownerCode,
      locatorId,
      locatorCode,
    } = props?.location?.query || {};
    const queryParams = {
      siteLov: siteId ? { siteId, siteCode } : undefined, // 回显站点
      materialLov: materialId ? { materialId, materialCode } : undefined,
      revisionCodes: revisionCode && revisionCode.length ? [revisionCode] : undefined,
      lotCodes: lotCode ? [lotCode] : undefined,
      qualityStatus,
      ownerType,
      ownerLov: ownerId
        ? { soLineId: ownerId,
          customerId: ownerId,
          soNumContent: ownerCode,
          supplierCode: ownerCode,
          customerCode: ownerCode,
        } : undefined,
      locatorLov: locatorId ? { locatorId, locatorCode } : null,
    };
    // setExportFlag(siteId || null);
    setTimeout(() => {
      dataSet.queryDataSet.loadData([queryParams]);
      dataSet.query();
    }, 200);
  }, [props?.location?.query, props?.location?.state]);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (dataSet) {
      const handerQuery = flag
        ? dataSet.queryDataSet.addEventListener
        : dataSet.queryDataSet.removeEventListener;
      // 查询条件更新时操作
      handerQuery.call(dataSet.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ name, record }) => {
    // console.log('11', record.get('siteLov'));
    // if (record.get('siteLov')) {
    //   setExportFlag(record.get('siteLov').siteId)
      
    // }else{
    //   setExportFlag(null)

    // }
    if (name === 'ownerType') {
      record.set('ownerLov', null);
    }
    if (name === 'siteLov') {
      record.set('locatorLov', null);
      record.set('materialLov', null);
    }
  };

  // 跳转物料批追溯功能
  const jumpToMaterialLotTraceability = record => {
    const { locatorCategory, locatorId, ...others } = record.toData();
    return fetchSubLocatorInfo({
      params: {
        locatorCategory,
        locatorId,
      },
    }).then(res => {
      if (res && res.success) {
        const queryParameter = {
          ...others,
          loactorsInfo: JSON.stringify(res.rows),
        };
        props.history.push({
          key: new Date().getTime(),
          pathname: `/hwms/product/material-lot-traceability/list/${new Date().getTime()}`,
          query: queryParameter,
        });
      }
    });
  };

  // 查看预留库存详情抽屉
  const showReserveStockDetails = record => {
    drawerHeadDs.loadData([record.toData()]);
    drawerTableDs.queryDataSet?.loadData([{ ...record.toData() }]);
    drawerTableDs.query();
    _modal = Modal.open({
      title: intl.get('tarzan.inventory.query.title.details').d('预留详细信息'),
      destroyOnClose: true,
      drawer: true,
      closable: true,
      maskClosable: true,
      style: {
        width: 720,
      },
      className: 'hmes-style-modal',
      children: <ReserveDrawer headDs={drawerHeadDs} tableDs={drawerTableDs} />,
      footer: (
        <Button onClick={() => _modal.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 节点展开时查询库存日记账数据
  const handleLoadData = ({ record, dataSet }) => {
    if (record.get('hasGetSub')) {
      return;
    }
    const {
      subLocatorIds,
      ownerType,
      ownerId,
      lotCode,
      holdFlag,
      revisionCode,
      materialId,
    } = record.toData();
    if (record.get('childFlag') === 'Y' && !record.get('hasGetSub')) {
      return queryChildList({
        params: {
          locatorIds: [...subLocatorIds],
          siteId: dataSet.queryDataSet.current.get('siteId'),
          ownerType,
          ownerId,
          lotCodes: [lotCode],
          holdFlag,
          revisionCodes: [revisionCode],
          materialIds: [materialId],
        },
      }).then(res => {
        if (res && res.success) {
          record.set('hasGetSub', true);
          dataSet.appendData(
            res.rows.map(item => {
              return {
                ...item,
                uuid: uuid(),
                parentUuid: record.get('uuid'),
              };
            }),
          );
        }
      });
    }
  };

  // 这里面可以控制node结点的判断来实现是否展示为叶结点
  const nodeCover = ({ record }) => {
    const nodeProps = {
      ...record,
    };
    if (record.get('childFlag') !== 'Y') {
      nodeProps.isLeaf = true;
    }
    return nodeProps;
  };

  const columns: ColumnProps[] = [
    { name: 'warehouseCode', lock: ColumnLock.left, width: 160 },
    { name: 'warehouseName', lock: ColumnLock.left, width: 160 },
    { name: 'locatorCode', lock: ColumnLock.left, width: 160 },
    { name: 'locatorName', width: 160 },
    { name: 'locatorCategoryDesc', width: 160 },
    { name: 'materialCode', width: 150 },
    { name: 'materialDesc', width: 220 },
    { name: 'revisionCode', width: 120 },
    { name: 'lotCode', width: 90 },
    { name: 'qualityStatusDesc', width: 120 },
    { name: 'minPackageQty', width: 120 },
    { name: 'packageQty', width: 120 },
    {
      name: 'onhandQty',
      renderer: ({ value, record }) => (
        <a onClick={() => jumpToMaterialLotTraceability(record)}>{value}</a>
      ),
    },
    { name: 'availableQty' },
    {
      name: 'holdQty',
      renderer: ({ value, record }) =>
        value === 0 || record?.get('childFlag') === 'Y' ? (
          value
        ) : (
          <a onClick={() => showReserveStockDetails(record)}>{value}</a>
        ),
    },
    { name: 'uomCode' },
    {
      name: 'ownerTypeDesc',
      renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
    },
    {
      name: 'ownerCode',
      renderer: ({ value, record }) => {
        if (record?.get('ownerTypeDesc')) {
          return value || record?.get('ownerId');
        }
      },
    },
  ];

  const getExportQueryParams = () => {
    if (!dataSet.queryDataSet || !dataSet.queryDataSet.current) {
      return {};
    }
    const queryParams = dataSet.queryDataSet.current.toData();
    Object.keys(queryParams).forEach((i) => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
        
    return {
      ...queryParams,
    };
    

  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.inventory.query.view.title.query').d('库存查询')}>
        {/* {
          exportFlag!== null && */}
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inv-onhand-quantity/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.button.export`).d('导出')}
        />
        {/* // } */}

      </Header>
      <Content>
        <Table
          searchCode="kccx1"
          customizedCode="kccx1"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
          }}
          mode={TableMode.tree}
          treeLoadData={handleLoadData}
          onRow={nodeCover}
          // treeAsync
          dataSet={dataSet}
          columns={columns}
          highLightRow
        />
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.inventory.query', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({ ...entranceDS() });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Query),
);
