/**
 * @Description: 事件查询-父子事件抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-10-28 17:04:38
 * @LastEditTime: 2022-10-31 14:18:00
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useRef, useEffect, useState } from 'react';
import { Form, TextField } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { List, CellMeasurerCache, CellMeasurer } from "react-virtualized";

const modelPrompt = 'tarzan.event.eventQuery.model.eventQuery';
const { Panel } = Collapse;

const ParentEventDrawerWms = (props) => {
  const { ds } = props;

  const [listStyle, setListStyle] = useState<null | { width: number | undefined, height: number | undefined }>(null);
  const _cache = useMemo(() => new CellMeasurerCache({ fixedWidth: true, minHeight: 30 }), []);
  const divRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setListStyle({
      width: divRef.current?.offsetWidth,
      height: divRef.current?.offsetHeight,
    })
  }, []);

  const rowRenderer = ({ index, key, parent, style }) => {
    return (
      <CellMeasurer key={key} cache={_cache} columnIndex={0} rowIndex={index} parent={parent}>
        {({ measure, registerChild }) => (
          <div onLoad={measure} ref={registerChild} style={style}>
            <Collapse bordered={false} defaultActiveKey={['basicInfo']} trigger="icon">
              <Panel
                header={
                  ds.data[index].get('parentEventFlag')
                    ? intl.get(`${modelPrompt}.parentEvent`).d('父事件')
                    : intl.get(`${modelPrompt}.sonEvent`).d('子事件')
                }
                key="basicInfo"
                showArrow={false}
              >
                <Form disabled record={ds.data[index]} columns={2} labelWidth={112}>
                  <TextField name="eventId" />
                  <TextField name="eventTypeCode" />
                  <TextField name="eventTypeDescription" />
                  <TextField name="eventTime" />
                  <TextField name="eventTypeUserName" />
                  <TextField name="requestTypeCode" />
                  <TextField name="workcellCode" />
                  <TextField name="locatorCode" />
                  <TextField name="shiftDate" />
                  <TextField name="shiftCode" />
                </Form>
              </Panel>
            </Collapse>
          </div>
        )}
      </CellMeasurer>);
  };

  return (
    <div style={{ height: '100%', width: '100%' }} ref={divRef}>
      {
        listStyle && (
          <List
            width={listStyle.width}
            height={listStyle.height}
            rowCount={ds.length}
            rowHeight={_cache.rowHeight}
            rowRenderer={rowRenderer}
          />
        )
      }
    </div>
  );
}

export default ParentEventDrawerWms;
