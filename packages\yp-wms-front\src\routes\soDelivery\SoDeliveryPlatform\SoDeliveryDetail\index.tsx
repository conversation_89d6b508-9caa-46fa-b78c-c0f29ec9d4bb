/**
 * @Description: 销售发运平台 - 详情页面
 * @Author: <EMAIL>
 * @Date: 2022/2/10 17:33
 * @LastEditTime: 2023-05-18 16:14:31
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo, useState, useRef } from 'react';
import {
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  NumberField,
  Select,
  Switch,
  Table,
  TextField,
  Button,
} from 'choerodon-ui/pro';
import { Badge, Collapse, Popover, Icon } from 'choerodon-ui';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import moment from 'moment';
import axios from 'axios';
import { getCurrentOrganizationId, getCurrentUser } from 'utils/utils';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { detailHeaderDS, detailLineDS } from '../stories/DeliveryDetailDS';
import styles from '../SoDeliveryList/index.module.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.soDelivery.soDeliveryPlatform';

const deliveryDetail = props => {
  const {
    match: {
      path,
      params: { id },
    },
    location: {
      state: { executionType },
    },
    customizeForm,
    customizeTable,
  } = props;
  const tableDs = useMemo(() => new DataSet(detailLineDS()), []);
  const formDs: DataSet = useMemo(
    () =>
      new DataSet({
        ...detailHeaderDS(),
        children: {
          instructionDocLineList: tableDs,
        },
      }),
    [],
  );
  const emergentDs: DataSet = useMemo(
    () =>
      new DataSet({
        fields: [
          {
            name: 'siteObj',
            type: FieldType.object,
            label: intl.get(`${modelPrompt}.emergent`).d('紧急发运单'),
            lovCode: `${BASIC.LOV_CODE_BEFORE}.URGENT_DELIVERY_DOC`,
            lovPara: {
              tenantId,
            },
            required: true,
            ignore: FieldIgnore.always,
          },
          {
            name: 'instructionDocId',
            type: FieldType.number,
            bind: 'siteObj.instructionDocId',
          },
        ],
      }),
    [],
  );

  const [canEdit, setCanEdit] = useState(false); // "编辑"按钮标识
  const [createFlag, setCreateFlag] = useState(true); // “新建”标识
  const [lineAdd, setLineAdd] = useState(false); // 当头上必输项全部输入时行上可新增
  const [isSubmit, setIsSubmit] = useState(false); // 是否保存成功标识
  const [toleranceInfo, setToleranceInfo] = useState<any>({}); // 允差信息
  const [permissionFlag, setPermissionFlag] = useState<boolean>(true); // 用于判断当前界面是否有编辑权限
  const [excuteLoading, seTexcuteLoading] = useState<boolean>(false); // 执行按钮loading

  const { run: handleSaveInstruction, loading: changeSaveLoading } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-delivery-platform/instruction-doc/update/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_DETAIL.LINE`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: getToleranceInfo } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-delivery-platform/instruction/tolerance/get/for/ui`,
      method: 'GET',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: getHandleExcute } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wmsProductDelivery/supplement/execution`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  const fetchLovData = () => {
    axios({
      url: `/hpfm/v1/${tenantId}/lovs/value/batch?typeGroup=MT.SO_DELIVERY_RETURN_DOC_TYPE`,
      method: 'get',
    }).then(res => {
      if (res) {
        formDs.current?.set('instructionDocTypeList', res?.typeGroup);
      }
    });
  };

  useEffect(() => {
    if (id !== 'create') {
      setCreateFlag(false);
      queryDetail();
    } else {
      formDs.loadData([
        {
          sourceSystemDesc: 'MES',
          createdByName: getCurrentUser().realName,
        },
      ]);
      fetchLovData();
      setCanEdit(true);
      setCreateFlag(true);
    }
  }, [id]);
  const queryDetail = () => {
    formDs.setQueryParameter('instructionDocId', id);
    formDs.query().then(res => {
      if (res.success) {
        setLineAdd(true);
        const content = res.rows || {};
        const { instructionDocLineList } = content;
        // 当行上有某行没有库位权限时，界面不可编辑
        if (instructionDocLineList.length) {
          instructionDocLineList.forEach(item => {
            if (!item.permissionFlag || item.permissionFlag === 'N') {
              setPermissionFlag(false);
            }
          });
        }
        // 进入详情界面时查允差信息并存到state里面
        return getToleranceInfo({
          params: {
            instructionDocType: content.instructionDocType,
            lovCode: 'MT.SO_DELIVERY_RETURN_DOC_TYPE',
          },
        }).then(response => {
          if (res && res.success) {
            fetchLovData();
            setToleranceInfo({
              ...response.rows,
              businessType: response.rows.minBusinessType,
            });
            formDs.current!.init('businessType', response.rows.minBusinessType);
            formDs.current!.init('instructionDocTypeTag', response.rows.instructionDocTypeTag);
            formDs.current!.init('fromLocatorRequiredFlag', response.rows.fromLocatorRequiredFlag);
            formDs.current!.init('toLocatorRequiredFlag', response.rows.toLocatorRequiredFlag);
          }
        });
      }
    });
  };

  const handleBack = () => {
    props.history.push({
      pathname: `/hwms/so-delivery/so-delivery-platform-new/list`,
      state: {
        queryFlag: isSubmit,
      },
    });
  };

  // 头上“取消”按钮的回调
  const handleCancel = () => {
    if (id !== 'create') {
      queryDetail();
      setCanEdit(false);
    } else {
      props.history.push({
        pathname: `/hwms/so-delivery/so-delivery-platform-new/list`,
        state: {
          queryFlag: isSubmit,
        },
      });
    }
  };

  // 点击“保存”按钮的回调
  const handleSave = async () => {
    const headerData: any = formDs.toData()[0];
    const lines: any = tableDs.toData();
    if (lines.length === 0) {
      notification.warning({
        description: intl
          .get(`${modelPrompt}.notification.lineLengthIsZero`)
          .d('未创建单据行，请检查！'),
      });
      return;
    }
    formDs!.current!.set('newDate', new Date());
    tableDs!.current!.set('newDate', new Date());
    getResult().then(response => {
      if (response) {
        return handleSaveInstruction({
          params: {
            ...headerData,
            creationDate: headerData.creationDate
              ? headerData.creationDate
              : moment(moment().format('YYYY-MM-DD HH:mm:ss')),
          },
        }).then(res => {
          if (res && res.success) {
            notification.success({});
            setCreateFlag(false);
            setCanEdit(false);
            setLineAdd(false);
            setIsSubmit(true);
            if (id === 'create') {
              props.history.push({
                pathname: `/hwms/so-delivery/so-delivery-platform-new/detail/${res.rows}`,
                state: {
                  executionType: '',
                },
              });
            } else {
              queryDetail();
            }
          }
        });
      }
    });
  };

  // 保存时对头和行DS上必输性校验
  const getResult = async () => {
    const validate = await formDs.validate(false, true);
    const pointValidate = await tableDs.validate();
    return validate && pointValidate;
  };

  // 点击行上“新增行”按钮的回调
  const handleAddLine = () => {
    const formData: any = formDs.toData()[0];
    const record = tableDs.create(
      {
        targetSite: {
          siteId: formData.siteId,
          siteCode: formData.siteCode,
        },
        targetSiteId: formData.siteId,
        targetSiteCode: formData.siteCode,
        prepareQty: formData.instructionDocType === 'SO_DELIVERY_DOC' ? 0 : null,
        deliveryQty: formData.instructionDocType === 'SO_DELIVERY_DOC' ? 0 : null,
        receivingQty: formData.instructionDocType === 'SO_RETURN_DOC' ? 0 : null,
        toleranceFlag: toleranceInfo.toleranceFlag,
        toleranceType: toleranceInfo.toleranceType,
        toleranceTypeDesc: toleranceInfo.toleranceTypeDesc,
        toleranceMaxValue: toleranceInfo.toleranceMaxValue,
        toleranceMinValue: toleranceInfo.toleranceMinValue,
      },
      0,
    );
    const listData = tableDs.toData();
    let maxNumber = 0;
    listData.forEach(item => {
      const { lineNumber } = item as any;
      if (lineNumber) {
        if (lineNumber > maxNumber) {
          maxNumber = lineNumber;
        }
      }
    });
    record.set('lineNumber', parseInt(String(maxNumber / 10), 10) * 10 + 10);
  };

  // 点击行上“删除”按钮的回调
  const deleteRecord = record => {
    tableDs.remove(record);
  };

  // 当头上的单据类型变化时的回调
  const headerTypeChange = val => {
    const list: any = formDs.toData()[0];
    const { siteId, customerId, instructionDocType } = list;
    setLineAdd(siteId && customerId && instructionDocType);
    formDs!.current!.init('contactAddress');
    formDs!.current!.init('expectedArrivalTime');
    formDs!.current!.init('demandTime');
    formDs!.current!.init('contactPerson');
    formDs!.current!.init('contactTel');
    formDs!.current!.init('contactFax');
    formDs!.current!.init('logisticsMode');
    formDs!.current!.init('logisticsCompanyCode');
    formDs!.current!.init('logisticsCompanyDesc');
    formDs!.current!.init('paymentType');
    if (val && val !== '') {
      return getToleranceInfo({
        params: {
          instructionDocType: val,
          lovCode: 'MT.SO_DELIVERY_RETURN_DOC_TYPE',
        },
      }).then(res => {
        if (res && res.success) {
          setToleranceInfo({
            ...res.rows,
            businessType: res.rows.minBusinessType,
          });
          formDs.current!.set('businessType', res.rows.minBusinessType);
          formDs.current!.set('instructionDocTypeTag', res.rows.instructionDocTypeTag);
          formDs.current!.set('fromLocatorRequiredFlag', res.rows.fromLocatorRequiredFlag);
          formDs.current!.set('toLocatorRequiredFlag', res.rows.toLocatorRequiredFlag);
          // 新增界面--切换单据类型时需要更新行上的允差信息
          const lineList = tableDs.toData();
          if (lineList.length > 0) {
            const newLine: any = [];
            lineList.forEach((item: any) => {
              const _item = {
                ...item,
                // 变更单据类型时需要清空行上的执行仓库
                targetLocator: null,
                targetLocatorCode: null,
                targetLocatorId: null,
                prepareQty: val === 'SO_DELIVERY_DOC' ? 0 : null,
                deliveryQty: val === 'SO_DELIVERY_DOC' ? 0 : null,
                receivingQty: val === 'SO_RETURN_DOC' ? 0 : null,
                toleranceFlag: res.rows.toleranceFlag,
                toleranceType: res.rows.toleranceType,
                toleranceTypeDesc: res.rows.toleranceTypeDesc,
                toleranceMaxValue: res.rows.toleranceMaxValue,
                toleranceMinValue: res.rows.toleranceMinValue,
              };
              newLine.push(_item);
            });
            tableDs.loadData(newLine);
          }
        }
      });
    }
    tableDs.loadData([]);
  };

  // 当头上的客户变化的回调
  const headerHeadCustomerChange = val => {
    if (val) {
      const list: any = formDs.toData()[0];
      const { siteId, customerId, instructionDocType } = list;
      setLineAdd(siteId && customerId && instructionDocType);
    } else {
      tableDs.loadData([]);
    }
  };

  // 当头上的站点变化时的回调
  const headerHeadSiteChange = val => {
    if (val) {
      const list: any = formDs.toData()[0];
      const { siteId, siteCode, customerId, instructionDocType } = list;
      setLineAdd(siteId && customerId && instructionDocType);
      // 新增界面--切换站点时需要清空行上的物料信息
      const lineList = tableDs.toData();
      if (lineList.length > 0) {
        const newLine: any = [];
        lineList.forEach((item: any) => {
          const _item = {
            ...item,
            materialObj: null,
            materialId: null,
            materialCode: null,
            revisionCode: null,
            materialName: null,
            uomCode: null,
            targetSite: {
              siteId,
              siteCode,
            },
            targetSiteId: siteId,
            targetSiteCode: siteCode,
          };
          newLine.push(_item);
        });
        tableDs.loadData(newLine);
      }
    } else {
      tableDs.loadData([]);
    }
  };

  // 行上“物料”Lov变化的回调
  const materialChange = val => {
    if (val) {
      tableDs.current!.init('revisionCode', val.currentRevisionCode);
    } else {
      tableDs.current!.init('revisionCode');
    }
  };

  // 行上“销单”Lov变化的回调
  const handleSoNumberChange = () => {
    // tableDs!.current!.init('soLineObj');
    tableDs!.current!.init('orderFlag', 'N');
  };

  // 行上“销单行”Lov变化的回调
  // const handleSoLineNumChange = () => {
  //   tableDs!.current!.init('orderFlag', 'N');
  // };

  // 行上“执行站点”Lov变化的回调
  const handleTargetSite = () => {
    tableDs!.current!.init('targetLocator');
  };

  // 行上“允差标识”Switch变化的回调
  const handleToleranceChange = () => {
    tableDs!.current!.init('toleranceType');
    tableDs!.current!.init('toleranceMaxValue');
    tableDs!.current!.init('toleranceMinValue');
  };

  // 行上“允差类型”Select变化的回调
  const handleToleranceTypeChange = () => {
    tableDs!.current!.init('toleranceMaxValue');
    tableDs!.current!.init('toleranceMinValue');
  };

  const lineTableColumns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!(canEdit && lineAdd)}
          onClick={handleAddLine}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      name: 'name',
      renderer: ({ record }) => (
        <PermissionButton
          type="c7n-pro"
          icon="remove"
          disabled={record?.get('instructionDocLineId')}
          onClick={() => deleteRecord(record)}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'lineNumber',
      align: ColumnAlign.left,
      lock: ColumnLock.left,
      width: 100,
    },
    {
      name: 'identifyType',
      lock: ColumnLock.left,
      width: 120,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        }
        if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'materialObj',
      width: 180,
      editor: record => !record?.get('instructionDocLineId') && <Lov onChange={materialChange} />,
      renderer: ({ record }) => record?.get('materialCode'),
    },
    {
      name: 'revisionCode',
      width: 100,
      editor: record => record.get('revisionFlag') === 'Y' && <Select noCache />,
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'needQty',
      width: 140,
      align: ColumnAlign.right,
      editor: record => !record?.get('instructionDocLineId') && <NumberField />,
    },
    {
      name: 'uomCode',
      width: 100,
    },
    {
      name: 'statusDesc',
      width: 80,
    },
    {
      name: 'soNumberObj',
      width: 140,
      editor: record =>
        !record?.get('instructionDocLineId') && <Lov onChange={handleSoNumberChange} />,
      renderer: ({ record }) => record?.get('soNumber'),
    },
    // {
    //   name: 'soLineObj',
    //   width: 140,
    //   editor: record =>
    //     !record?.get('instructionDocLineId') && <Lov onChange={handleSoLineNumChange} />,
    //   renderer: ({ record }) => record?.get('soLineNumber'),
    // },
    {
      name: 'soLineNumber',
      width: 140,
    },
    {
      name: 'orderFlag',
      width: 100,
      align: ColumnAlign.center,
      editor: () => canEdit && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'targetSite',
      width: 140,
      editor: () => canEdit && <Lov onChange={handleTargetSite} />,
      renderer: ({ record }) => record?.get('targetSiteCode'),
    },
    {
      name: 'targetLocator',
      width: 180,
      editor: () => canEdit && <Lov />,
      renderer: ({ record }) => record?.get('targetLocatorCode'),
    },
    {
      name: 'prepareQty',
      align: ColumnAlign.right,
      width: 100,
    },
    {
      name: 'deliveryQty',
      align: ColumnAlign.right,
      width: 100,
    },
    {
      name: 'receivingQty',
      align: ColumnAlign.right,
      width: 100,
    },
    {
      name: 'remark',
      width: 140,
      editor: () => canEdit && <TextField />,
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: ColumnAlign.center,
      editor: record =>
        !record?.get('instructionDocLineId') && <Switch onChange={handleToleranceChange} />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      width: 140,
      editor: record =>
        !record?.get('instructionDocLineId') &&
        record?.get('toleranceFlag') === 'Y' && <Select onChange={handleToleranceTypeChange} />,
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
      align: ColumnAlign.right,
      editor: record =>
        !record?.get('instructionDocLineId') &&
        record?.get('toleranceFlag') === 'Y' && <NumberField />,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
      align: ColumnAlign.right,
      editor: record =>
        !record?.get('instructionDocLineId') &&
        record?.get('toleranceFlag') === 'Y' && <NumberField />,
    },
  ];

  const handleExcute = async () => {
    const validate = await emergentDs.current?.validate();
    if (!validate) {
      return notification.error({
        message: '请先选择紧急发运单',
      });
    }
    seTexcuteLoading(true);
    await getHandleExcute({
      params: {
        instructionDocId: id,
        urgentDeliveryDocId: emergentDs.current?.get('instructionDocId'),
      },
    }).then(res => {
      if (res && res.success) {
        props.history.push({
          pathname: `/hwms/so-delivery/so-delivery-platform-new/list`,
          state: {
            queryFlag: true,
          },
        });
      }
    });
    seTexcuteLoading(false);
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.soDeliveryPlatform`).d('成品发运平台')}
        backPath="/hwms/so-delivery/so-delivery-platform-new/list"
        onBack={handleBack}
      >
        {canEdit && executionType !== 'execution' && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
              loading={changeSaveLoading}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <PermissionButton
              type="c7n-pro"
              icon="close"
              onClick={handleCancel}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </PermissionButton>
          </>
        )}
        {!canEdit && executionType !== 'execution' && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            disabled={!permissionFlag}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        {executionType === 'execution' && (
          <>
            <Button color={ButtonColor.primary} loading={excuteLoading} onClick={handleExcute}>
              执行
            </Button>
            <Form dataSet={emergentDs} style={{ width: 400 }} className={styles['detail-excute']}>
              <Lov name="siteObj" />
            </Form>
          </>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.header`).d('头信息')}
            key="basicInfo"
            dataSet={formDs}
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_DETAIL.HEAD`,
              },
              <Form disabled={!canEdit} dataSet={formDs} columns={3} labelWidth={110}>
                <TextField disabled name="instructionDocNum" />
                <TextField disabled name="instructionDocStatus" />
                <Select
                  disabled={!createFlag}
                  name="instructionDocType"
                  onChange={headerTypeChange}
                />
                <TextField disabled name="sourceSystemDesc" />
                <Lov
                  name="customerObj"
                  disabled={!createFlag}
                  onChange={headerHeadCustomerChange}
                />
                <TextField name="contactAddress" />
                <Lov name="siteObj" disabled={!createFlag} onChange={headerHeadSiteChange} />
                <DateTimePicker name="demandTime" />
                <DateTimePicker name="expectedArrivalTime" />
                <TextField name="contactPerson" />
                <TextField name="contactTel" />
                <TextField name="contactFax" />
                <TextField name="logisticsMode" />
                <TextField name="logisticsCompanyCode" />
                <TextField name="logisticsCompanyDesc" />
                <TextField name="paymentType" />
                <TextField disabled name="createdByName" />
                <DateTimePicker disabled name="creationDate" />
                <TextField colSpan={2} name="remark" />
              </Form>,
            )}
          </Panel>
          <Panel
            header={
              <>
                {intl.get(`${modelPrompt}.title.line`).d('行信息')}
                <Popover
                  placement="topLeft"
                  content={intl
                    .get(`${modelPrompt}.line.tooltipMessage`)
                    .d(
                      '若您没有勾选的行上仓库的权限，该行将会为置灰状态，不能编辑，您可以前往人员仓库权限分配维护权限。',
                    )}
                >
                  <Icon
                    style={{ fontSize: 16, color: '#8c8c8c', marginBottom: 2, marginLeft: 2 }}
                    type="help"
                  />
                </Popover>
              </>
            }
            key="location"
            dataSet={tableDs}
          >
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_DETAIL.LINE`,
              },
              <Table
                filter={record => {
                  return record.status !== 'delete';
                }}
                dataSet={tableDs}
                highLightRow={false}
                columns={lineTableColumns}
              />,
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.soDelivery.soDeliveryPlatform', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_DETAIL.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_DETAIL.LINE`,
    ],
    // @ts-ignore
  })(deliveryDetail),
);
