/**
 * @Description: 创建送货单抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-12-13 10:31:13
 * @LastEditTime: 2023-05-18 15:33:33
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect,useState } from 'react';
import {  Table,NumberField } from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import notification from 'utils/notification';

const tenantId = getCurrentOrganizationId();
// ${BASIC.HMES_BASIC}
const endUrl = '';
const HMES_BASIC = BASIC.HMES_BASIC;

const AvgDrawer = props => {
  const { selectDocIds, customizeTable, tableDs } = props;

  const [pageInfo,setPageInfo]=useState({
    total:0,
    current:1,
    pageSize:10,
  })


  const changePageUnSelected=()=>{
    const list=tableDs.selected
    for(let i=0;i<list.length;i++){
      tableDs.unSelect(list[i])
    }
  }


  const queryData=async (page=1,size=10)=>{
    const res=await request(`${HMES_BASIC}${endUrl}/v1/${tenantId}/receive-return-bill/materialPickingOut/query?page=${page-1}&size=${size}`, {
      method: 'POST',
      data: selectDocIds,
    })
    if(res && res.rows){
      changePageUnSelected()
      tableDs.loadData((res.rows.content??[]).map(ele=>({
        ...ele,
        autoAllQtySelf:ele.autoAllQty,
      })))
      tableDs.selectAll()
      setPageInfo({
        total:res.rows.totalElements,
        current:page,
        pageSize:res.rows.size,
      })
    }else{
      notification.success({
        message: res.message,
      });
    }
  }


  useEffect(() => {
    queryData()
  }, []);

  const changeHandle=(page,pageSize)=>{
    queryData(page,pageSize)
  }


  const columns = [
    {
      name: 'materialCode',
      width: 180,
    },
    {
      name: 'materialName',
      width: 120,
    },
    {
      name: 'instructionDocNum',
      width: 160,
    },
    {
      name: 'actualQuantity',
      width: 80,
    },
    {
      name: 'instructionStatusDesc',
      width: 80,
      align: 'right',
    },
    {
      name: 'receivedQty',
      width: 120,
    },
    {
      name: 'signedQty',
      width: 120,
    },
    {
      name: 'quantity',
      width: 120,
    },
    {
      name: 'fromLocatorCode',
      width: 80,
    },
    {
      name: 'sourceSubLocatorCode',
      width: 120,
    },
    {
      name: 'toLocatorCodeLov',
      width: 200,
      align: 'center',
      editor: true,
    },
    {
      name: 'subLocatorCodeLov',
      width: 150,
      align: 'center',
      editor: true,
    },
    {
      name: 'specifiedLot',
      width: 120,
    },
    {
      name: 'specifiedLevel',
      width: 120,
      editor: true,
    },
    {
      name: 'autoAllQtySelf',
      width: 120,
      editor: () => <NumberField />,
      // renderer:({record})=>record.get('autoAllQty')
    },
  ];


  return customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_MATERIAL_LOT.QUERY`,
    },
    <Table
      dataSet={tableDs}
      columns={columns}
      pagination={{
        total:pageInfo.total,
        page:pageInfo.current,
        pageSize:pageInfo.pageSize,
        onChange:changeHandle,
      }}
    />,
  )
};

export default AvgDrawer;
