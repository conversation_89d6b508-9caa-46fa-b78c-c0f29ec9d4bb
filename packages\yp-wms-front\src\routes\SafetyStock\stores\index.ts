import intl from 'utils/intl';
import {  FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.safetyStock';
const tenantId = getCurrentOrganizationId();

// 行列表DS
const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: true,
  selection: false,
  forceValidate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'supMaterialId',
  modifiedCheck: false,
  transport: {
    read: () => ({
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-sup-material-safe-stocks/list/ui`,
      method: 'get',
    }),
  },
  queryFields:[
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.factory`).d('工厂'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.APS.SITE.URL',
      textField: 'siteCode',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },

    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商'),
      ignore: FieldIgnore.always,
      multiple: ',',
      lovCode: 'YP.SPC.SUPPLIER',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierIds',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierCode',
      bind: 'supplierLov.supplierCode',
    },

    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialName`).d('物料'),
      ignore: FieldIgnore.always,
      multiple: ',',
      lovCode: 'MT.MATERIAL.PERMISSION',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },

    {
      name: 'locator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locator`).d('库位'),
      lookupCode: 'WMS.SUPPLIER_LOCATOR_TYPE',
      lovPara: { tenantId },
    },
  ],
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.APS.SITE.URL',
      valueField: 'siteId',
      textField: 'siteCode',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'MT.MATERIAL.PERMISSION',
      valueField: 'materialId',
      textField: 'materialCode',
      dynamicProps: {
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
        }),
        disabled: ({ record }) => {
          return !record?.get('siteId')
        },
      },
      required: true,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      lovCode: 'YP.SPC.SUPPLIER',
      valueField: 'supplierId',
      textField: 'supplierCode',
      lovPara: {tenantId},
      required: true,
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierCode',
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'locator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locator`).d('库存种类'),
      lookupCode: 'WMS.SUPPLIER_LOCATOR_TYPE',
      lovPara: { tenantId },
      required: true,
    },
    {
      name: 'safeStockValue',
      label: intl.get(`${modelPrompt}.safeStockValue`).d('安全库存值'),
      type: FieldType.number,
      required: true,
      validator: (value) => {
        if (value > 0) {
          return true;
        }
        return `${intl.get(`${modelPrompt}.safeStockValue`).d('安全库存值')}需大于0`;
      },
    },
    {
      name: 'dailyConsum',
      label: intl.get(`${modelPrompt}.dailyConsum`).d('日消耗量'),
      type: FieldType.number,
      required: true,
      validator: (value) => {
        if (value > 0) {
          return true;
        }
        return `${intl.get(`${modelPrompt}.dailyConsum`).d('日消耗量')}需大于0`;
      },
    },
    {
      name: 'safeStockDay',
      label: intl.get(`${modelPrompt}.safeStockDay`).d('安全库存天数'),
      type: FieldType.number,
      required: true,
      validator: (value) => {
        if (value > 0) {
          return true;
        }
        return `${intl.get(`${modelPrompt}.safeStockDay`).d('安全库存天数')}需大于0`;
      },
    },
    {
      name: 'enableFlag',
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      type: FieldType.string,
      lookupCode: 'MT.APS.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      required: true,
    },
  
  ],
  events:{
    query: ({dataSet}) => {
      dataSet?.setState('editIndex', undefined);
      return true
    },
    update: ({record, name}) => {
      if (name === 'siteLov') {
        record.set('materialLov', null)
      }
    },
  },
});


export { tableDS };
