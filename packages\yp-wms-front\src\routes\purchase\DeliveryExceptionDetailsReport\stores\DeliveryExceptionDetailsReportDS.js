import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.deliveryExceptionDetailsReport';
const tenantId = getCurrentOrganizationId();

const headerTableDS = () => ({
  autoQuery: false,
  pageSize: 10,
  selection: 'multiple',
  dataKey: 'content',
  totalKey: 'totalElements',
  // cacheSelection: false,
  primaryKey: 'payId',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-pay/abnormal-pageCondition`,
        method: 'POST',
      };
    },
  },
  queryFields: [
    {
      name: 'startDate',
      label: intl.get(`${modelPrompt}.startDate`).d('发生日期从'),
      type: FieldType.date,
      max: 'endDate',
    },
    {
      name: 'endDate',
      label: intl.get(`${modelPrompt}.endDate`).d('发生日期至'),
      type: FieldType.date,
      min: 'startDate',
    },
    {
      name: 'instructionDocNum',
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('送货单号'),
      type: FieldType.string,
    },
    {
      name: 'poNumber',
      label: intl.get(`${modelPrompt}.poNumber`).d('采购订单号'),
      type: FieldType.string,
    },
    {
      name: 'supplier',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      multiple: true,
    },
    {
      name: 'supplierIds',
      bind: 'supplier.supplierId',
    },
    {
      name: 'supplierCodes',
      bind: 'supplier.supplierCode',
    },
    {
      name: 'problemType',
      type: FieldType.select,
      label: intl.get(`${modelPrompt}.problemType`).d('问题类型'),
      lookupCode: 'WMS.PROBLEM_TYPE',
    },
    {
      name: 'problemTerritory',
      type: FieldType.select,
      label: intl.get(`${modelPrompt}.problemTerritory`).d('责任领域'),
      lookupCode: 'WMS.PROBLEM_TERRITORY',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      textField: 'materialCode',
      valueField: 'materialId',
      multiple: true,
    },
    {
      name: 'materialIds',
      bind: 'material.materialId',
    },
    {
      name: 'materialCodes',
      bind: 'material.materialCode',
    },
  ],
  fields: [
    {
      name: 'happenDate',
      type: FieldType.date,
      label: intl.get(`${modelPrompt}.happenDate`).d('发生日期'),
    },
    {
      name: 'statusMeaning',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusMeaning`).d('进度跟踪状态'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('送货单号'),
    },
    {
      name: 'poNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.poNumber`).d('采购订单号'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料号'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'problemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemType`).d('问题类型'),
      lookupCode: 'WMS.PROBLEM_TYPE',
    },
    {
      name: 'problemDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.problemDescription`).d('问题描述'),
    },
    {
      name: 'affect',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.affect`).d('对生产或交付的影响'),
    },
    {
      name: 'downTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.downTime`).d('停线时长'),
    },
    {
      name: 'problemTerritory',
      type: FieldType.select,
      label: intl.get(`${modelPrompt}.problemTerritory`).d('责任领域'),
      lookupCode: 'WMS.PROBLEM_TERRITORY',
    },
    {
      name: 'solutionTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.solutionTime`).d('计划解决时间'),
    },
    {
      name: 'creator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creator`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'uuid',
      type: 'attachment',
      bucketName: 'wms-wjzck',
      label: intl.get(`${modelPrompt}.uuid`).d('附件'),
    },
  ],
  // record: {
  //   dynamicProps: {
  //     selectable: record => !['COMPLETED', 'CLOSED'].includes(record.get('status')),
  //   },
  // },
});

export { headerTableDS };
