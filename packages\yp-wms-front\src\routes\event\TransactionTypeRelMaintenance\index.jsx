import React, { useState, useMemo, useEffect } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Badge, Popconfirm } from 'choerodon-ui';
import { PageHeaderWrapper } from 'hzero-boot/lib/components/Page';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import notification from 'utils/notification';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { getUserRole } from '@services/api';
import { tableDS } from './stories/TableDS';
import { drawerDS } from './stories/DrawerDS';
import { AutoModal } from './AutoModal';
import DetailDrawer from './DetailDrawer';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.event.transaction.type.rel';
/**
 * 头行结构的表单示例
 */

const Initial = props => {
  const drawerDs = useMemo(() => new DataSet(drawerDS()), []);
  const [deleteLoading, setDeleteLoading] = useState(false); // 删除loading
  const [deleteFlag, setDeleteFlag] = useState(true);
  const [count, setCount] = useState(0);
  const [userRule, setUserRule] = useState('N');

  useEffect(() => {
    getUserRole().then(res => {
      if (res && res.success) {
        setUserRule(res.rows);
      }
    });
  }, []);

  useEffect(() => {
    function processDataSetListener(flag) {
      const handler = flag
        ? props.dataSet.queryDataSet.addEventListener
        : props.dataSet.queryDataSet.removeEventListener;
      handler.call(props.dataSet, 'load', handleQueryDataSetLoad);
    }
    processDataSetListener(true);
    return function clean() {
      processDataSetListener(false);
    };
  });

  // 初始化标志Y,没有权限的不可选
  const handleQueryDataSetLoad = ({ dataSet }) => {
    if (dataSet.records.length === 0) return;
    dataSet.records.forEach(i => {
      if (i.data.initialFlag === 'Y' && userRule === 'N') {
        // eslint-disable-next-line
        i.selectable = false;
      }
    });
  };

  const handleChange = () => {
    setDeleteFlag(props.dataSet.selected.length === 0);
    setCount(props.dataSet.selected.length);
  };
  // 保存
  const onSave = async () => {
    const validate = await drawerDs.validate();
    if (!validate) {
      return false;
    }
    const data = drawerDs.toData();
    const { businessTypeCode, eventTypeCode, initialFlag } = data[0];
    if (userRule === 'N' && initialFlag === 'Y') {
      return true;
    }
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-event-trans-type-rel/update/ui`, {
      method: 'POST',
      body: {
        ...data[0],
        businessTypeCode:
          Object.prototype.toString.call(businessTypeCode) === '[object Object]'
            ? businessTypeCode.typeCode
            : businessTypeCode,
        eventTypeCode:
          Object.prototype.toString.call(eventTypeCode) === '[object Object]'
            ? eventTypeCode.eventTypeCode
            : eventTypeCode,
        initialFlag: initialFlag || 'N',
      },
    }).then(res => {
      if (res && res.success) {
        props.dataSet.query();
        notification.success();
      } else {
        notification.error({
          message: res && res.message,
        });
      }
    });
  };

  // 删除,dataSet自带得删除问题太多
  const deleteRecord = () => {
    const arr = [];
    props.dataSet.selected.forEach(i => {
      arr.push(i.toData().eventTransTypeRelId);
    });
    if (arr.length === 0) {
      return;
    }
    setDeleteLoading(true);
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-event-trans-type-rel/delete/ui`, {
      method: 'POST',
      body: arr,
    }).then(res => {
      setDeleteLoading(false);
      if (res && res.success) {
        props.dataSet.query();
        setDeleteFlag(true);
        notification.success();
      } else {
        notification.error({
          message: res && res.message,
        });
      }
    });
  };

  /**
   * 新建编辑抽屉
   * @param {*} record
   */
  const onCreateAndEdit = (record, flag) => {
    if (!flag) {
      drawerDs.loadData([record.toData()]);
    } else {
      drawerDs.loadData([]);
    }
    AutoModal({
      title: flag
        ? intl.get(`${modelPrompt}.newDrawer`).d('新建事件事务转换关系')
        : intl.get(`${modelPrompt}.editDrawer`).d('编辑事件事务转换关系'),
      children: (
        <DetailDrawer ds={drawerDs} userRule={userRule} data={!flag ? record.toData() : {}} />
      ),
      width: 360,
      onOk: () => onSave(),
      cancelText: intl.get('tarzan.common.button.cancel').d('取消'),
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      footer: (okBtn, cancelBtn) => (
        <div style={{ float: 'right' }}>
          {cancelBtn}
          {okBtn}
        </div>
      ),
    });
  };

  const columns = [
    {
      name: 'eventTypeCode',
      width: 200,
      renderer: ({ value, record }) => {
        return (
          <a
            onClick={() => {
              onCreateAndEdit(record, false);
            }}
          >
            {value}
          </a>
        );
      },
    },
    {
      name: 'description',
    },
    {
      name: 'businessTypeCode',
    },
    {
      name: 'businessTypeDesc',
    },
    {
      name: 'transCategoryDesc',
    },
    {
      name: 'erpSystemType',
      align: 'center',
    },
    {
      name: 'transTypeCode',
    },
    {
      name: 'transCode',
      align: 'center',
    },
    {
      name: 'transferMethod',
      align: 'center',
    },
    {
      name: 'initialFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.yes').d('是')
              : intl.get('tarzan.common.label.no').d('否')
          }
        />
      ),
    },
  ];

  const {
    match: { path },
  } = props;
  return (
    <div className="hmes-style">
      <PageHeaderWrapper
        title={intl.get(`${modelPrompt}.title.list`).d('事件事务转换关系维护')}
        header={
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="add"
              onClick={() => onCreateAndEdit({}, true)}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.create').d('新建')}
            </PermissionButton>
            <Popconfirm
              title={intl
                .get(`${modelPrompt}.confirm.delete`, {
                  count,
                })
                .d(`总计${count}条数据，是否确认删除?`)}
              onConfirm={deleteRecord}
              okText={intl.get('tarzan.common.button.confirm').d('确认')}
              cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
            >
              <PermissionButton
                type="c7n-pro"
                icon="delete_black-o"
                loading={deleteLoading}
                disabled={deleteFlag}
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get('tarzan.common.button.delete').d('删除')}
              </PermissionButton>
            </Popconfirm>
          </>
        }
      >
        <Table
          queryBar='filterBar'
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={props.dataSet}
          columns={columns}
          searchCode="sjswzhgxwh"
          customizedCode="sjswzhgxwh"
          onChange={handleChange}
        />
      </PageHeaderWrapper>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.event.transaction.type.rel', 'tarzan.common'],
})(
  withProps(
    () => {
      const dataSet = new DataSet({
        ...tableDS(),
      });
      return {
        dataSet,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Initial),
);
