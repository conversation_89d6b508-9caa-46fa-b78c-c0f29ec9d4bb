/**
 * @Description: 区域维护详情
 * @Author: <<EMAIL>>
 * @Date: 2021-02-18 14:18:05
 * @LastEditTime: 2023-08-22 17:42:00
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId, getCurrentLanguage } from 'utils/utils';
// import { isUndefined } from 'lodash';
import { getResponse } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.model.org.locator';

const BasicsAtttribtesDS = () => ({
  autoQuery: false,
  autoQueryAfterSubmit: false,
  autoCreate: true,
  autoLocateFirst: true,
  lang: getCurrentLanguage(),
  dataKey: 'rows',
  transport: {
    tls: ({ record, name }) => {
      const fieldName = name;
      const className = 'org.tarzan.model.domain.entity.MtModLocator';
      return {
        data: { locatorId: record.data.locatorId },
        params: { fieldName, className },
        url: `${BASIC.TARZAN_MODEL}/v1/hidden/multi-language`,
        method: 'POST',
      };
    },
    read: () => {
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${getCurrentOrganizationId()}/mt-mod-locator/detail/ui`,
        method: 'GET',
        transformResponse: val => {
          const res = JSON.parse(val);
          return {
            ...res,
            rows: {
              ...res.rows,
              weightUomId: res.rows.weightUomId || null,
              sizeUomId: res.rows.sizeUomId || null,
              coordinateId: res.rows.coordinateId || null,
              parentLocatorId: res.rows.parentLocatorId || null,
            },
          }
        },
      };
    },
    submit: ({ dataSet }) => {
      let newData = dataSet.toData()[0];
      if (newData.locatorId) {
        if (newData.coordinateType === '1D') {
          if (newData.xValue === dataSet.current.getPristineValue('xValue')) {
            newData = {
              ...newData,
              xValue: undefined,
            };
          }
        }
        if (newData.coordinateType === '2D') {
          if (
            newData.xValue === dataSet.current.getPristineValue('xValue') &&
            newData.yValue === dataSet.current.getPristineValue('yValue')
          ) {
            newData = {
              ...newData,
              xValue: undefined,
              yValue: undefined,
            };
          }
        }
        if (newData.coordinateType === '3D') {
          if (
            newData.xValue === dataSet.current.getPristineValue('xValue') &&
            newData.yValue === dataSet.current.getPristineValue('yValue') &&
            newData.zValue === dataSet.current.getPristineValue('zValue')
          ) {
            newData = {
              ...newData,
              xValue: undefined,
              yValue: undefined,
              zValue: undefined,
            };
          }
        }
      }
      if (newData.coordinateType === '1D') {
        newData = {
          ...newData,
          yValue: undefined,
          zValue: undefined,
        };
      } else if (newData.coordinateType === '2D') {
        newData = {
          ...newData,
          zValue: undefined,
        };
      }
      return {
        url: `${BASIC.TARZAN_MODEL}/v1/${getCurrentOrganizationId()}/mt-mod-locator/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.LOCATOR_DETAIL.BASIC,${BASIC.CUSZ_CODE_BEFORE}.ORG_RELATION.LOACTOR`,
        data: newData,
        method: 'POST',
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData, false, true)];
          }
        },
      };
    },
  },

  fields: [
    {
      name: 'nowDate',
      type: FieldType.number,
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      required: true,
    },
    {
      name: 'locatorName',
      type: FieldType.intl,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
      required: true,
    },
    {
      name: 'locatorType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorType`).d('库位类型'),
      textField: 'description',
      valueField: 'typeCode',
      required: true,
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=LOCATOR_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteIds',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.siteIds`).d('分配站点'),
      required: true,
      multiple: true,
      /*  textField: 'displayName', // 因为在DS里面写的话，不会在非下拉框下拉时重新查询，所以先注释掉，自己调接口来查询
      valueField: 'siteId',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            locatorId: record.data.locatorId || 10,
          };
        },
      },
      lookupUrl: `${
        BASIC.TARZAN_MODEL
      }/v1/${getCurrentOrganizationId()}/mt-mod-locator/user/distribution/site/list/ui`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          if (!isUndefined(data)) {
            const { rows } = JSON.parse(data);
            return rows;
          }
        },
      }, */
    },
    {
      name: 'siteNames',
      type: FieldType.string,
    },
    {
      name: 'locatorCategory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCategory`).d('库位类别'),
      textField: 'description',
      valueField: 'typeCode',
      required: true,
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupUrl: `${
        BASIC.TARZAN_COMMON
      }/v1/${getCurrentOrganizationId()}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=LOCATOR_CATEGORY`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'parentLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.parentLocatorLov`).d('上层库位'),
      lovCode: 'MT.MODEL.PARENT_LOCATOR',
      ignore: 'always',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId: getCurrentOrganizationId(),
            currentLocatorId: record.data.locatorId,
            currentLocatorCategory: record.get('locatorCategory'),
            enableFlag: 'Y',
            assignSiteIds: (record.get('siteIds') || []).join(','),
          };
        },
      },
    },
    {
      name: 'parentLocatorId',
      type: FieldType.number,
      bind: 'parentLocatorLov.locatorId',
    },
    {
      name: 'parentLocatorCode',
      type: FieldType.string,
      bind: 'parentLocatorLov.locatorCode',
    },
    {
      name: 'parentLocatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.parentLocatorName`).d('上层库位描述'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('库位标识'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get('tarzan.common.label.enableFlag').d('启用状态'),
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupCode: 'MT.ENABLE_FLAG',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'Y',
    },
    {
      name: 'negativeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.negativeFlag`).d('是否允许负库存'),
      lovPara: {
        tenantId: getCurrentOrganizationId(),
      },
      lookupCode: 'MT.YES_NO',
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'subinv',
      required:true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.subinv`).d('ERP仓库'),
    },
    {
      name: 'coordinate',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.location`).d('位置坐标系'),
      lovCode: 'MT.MODEL.COORDINATE',
      ignore: 'always',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId: getCurrentOrganizationId(),
          };
        },
        disabled: ({ record }) => {
          return record.get('locatorCategory') !== 'AREA';
        },
      },
    },
    {
      name: 'coordinateId',
      type: FieldType.number,
      bind: 'coordinate.coordinateId',
    },
    {
      name: 'coordinateCode',
      type: FieldType.string,
      bind: 'coordinate.coordinateCode',
    },
    {
      name: 'coordinateType',
      type: FieldType.string,
      bind: 'coordinate.coordinateType',
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locationDesc`).d('位置坐标系描述'),
      disabled: true,
      bind: 'coordinate.description',
    },
    {
      name: 'xValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.locationValue`).d('位置坐标值'),
      ignore: 'clean',
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('locatorCategory') ||
            !record.get('coordinateType') ||
            record.get('locatorCategory') === 'AREA'
          );
        },
        required: ({ record }) => {
          if (record.get('coordinateType') === '2D') {
            if (record.get('xValue') || record.get('yValue')) {
              return true;
            }
          } else if (record.get('coordinateType') === '3D') {
            if (record.get('xValue') || record.get('yValue') || record.get('zValue')) {
              return true;
            }
          }
        },
      },
    },
    {
      name: 'yValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.locationValue`).d('位置坐标值'),
      ignore: 'clean',
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('locatorCategory') ||
            !record.get('coordinateType') ||
            record.get('locatorCategory') === 'AREA'
          );
        },
        required: ({ record }) => {
          if (record.get('coordinateType') === '2D') {
            if (record.get('xValue') || record.get('yValue')) {
              return true;
            }
          } else if (record.get('coordinateType') === '3D') {
            if (record.get('xValue') || record.get('yValue') || record.get('zValue')) {
              return true;
            }
          }
        },
      },
    },
    {
      name: 'zValue',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.locationValue`).d('位置坐标值'),
      ignore: 'clean',
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('locatorCategory') ||
            !record.get('coordinateType') ||
            record.get('locatorCategory') === 'AREA'
          );
        },
        required: ({ record }) => {
          if (record.get('coordinateType') === '3D') {
            if (record.get('xValue') || record.get('yValue') || record.get('zValue')) {
              return true;
            }
          }
        },
      },
    },
    {
      name: 'maxWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maxWeight`).d('最大承载重量'),
      min: 0,
    },
    {
      name: 'weightUomCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.weightUomCode`).d('重量单位'),
      lovCode: 'MT.COMMON.UOM',
      ignore: 'always',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId: getCurrentOrganizationId(),
            uomType: 'WEIGHT',
          };
        },
      },
    },
    {
      name: 'weightUomId',
      type: FieldType.number,
      bind: 'weightUomCodeLov.uomId',
    },
    {
      name: 'weightUomCode',
      type: FieldType.string,
      bind: 'weightUomCodeLov.uomCode',
    },
    {
      name: 'weightUomDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.weightUomDesc`).d('重量单位描述'),
    },
    {
      name: 'maxCapacity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.maxCapacity`).d('最大装载容量'),
      min: 0,
    },
    {
      name: 'sizeUomCodeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sizeUomCode`).d('尺寸单位'),
      lovCode: 'MT.COMMON.UOM',
      ignore: 'always',
      dynamicProps: {
        lovPara: () => {
          return {
            tenantId: getCurrentOrganizationId(),
            uomType: 'LENGTH',
          };
        },
      },
    },
    {
      name: 'sizeUomId',
      type: FieldType.number,
      bind: 'sizeUomCodeLov.uomId',
    },
    {
      name: 'sizeUomCode',
      type: FieldType.string,
      bind: 'sizeUomCodeLov.uomCode',
    },
    {
      name: 'sizeUomDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sizeUomDesc`).d('尺寸单位描述'),
    },
    {
      name: 'length',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.length`).d('库位长'),
      min: 0,
    },
    {
      name: 'width',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.width`).d('库存宽'),
      min: 0,
    },
    {
      name: 'height',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.height`).d('库位高'),
      min: 0,
    },
  ],
});

export { BasicsAtttribtesDS };
