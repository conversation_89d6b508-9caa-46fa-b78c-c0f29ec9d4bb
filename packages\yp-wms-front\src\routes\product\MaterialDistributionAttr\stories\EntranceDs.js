/**
 * @feature 物料配送属性维护-列表页DS
 * @date 2021-3-8
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.product.materialDistributionNew.model.materialDistributionNew';

const tenantId = getCurrentOrganizationId();

const entranceDS = () => ({
  primaryKey: 'assembleGroupId',
  queryUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-assemble-group/list/ui`,
  autoQuery: true,
  autoCreate: true,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'siteCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteCode.siteId',
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: 'always',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      bind: 'material.materialId',
    },
    {
      name: 'materialCategoryCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCategoryCode`).d('物料类别'),
      lovCode: 'MT.METHOD.MATERIAL_CATEGORY',
      ignore: 'always',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialCategoryId',
      bind: 'materialCategoryCode.materialCategoryId',
    },
    {
      name: 'areaCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaCode`).d('配送路线'),
      lovCode: 'MT.METHOD.DISTRIBUTION_ROUTE',
      ignore: 'always',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
      },
    },
    {
      name: 'distributionRouteId',
      bind: 'areaCode.distributionRouteId',
    },
    {
      name: 'sourceLocatorCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceLocator`).d('来源库位'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      ignore: 'always',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')].join(','),
          };
        },
      },
    },
    {
      name: 'sourceLocatorId',
      bind: 'sourceLocatorCode.locatorId',
    },
    {
      name: 'organizationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.patternType`).d('目标点位类型'),
      options: targetPointOptionDs,
      textField: 'description',
      valueField: 'typeCode',
    },
    {
      name: 'pfepDistributionOrganizationId',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.distributionCoverLocation`).d('目标点位'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      transformRequest: (val, record) => {
        if (record.get('organizationType') === 'LOCATOR') {
          return (record.get('pfepDistributionOrganizationId') || {}).locatorId;
        }
        return (record.get('pfepDistributionOrganizationId') || {}).workcellId;

      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('organizationType') || !record.get('siteCode');
        },
        lovPara: ({ record }) => {
          if (record.get('organizationType') === 'LOCATOR') {
            return {
              tenantId,
              siteIds: [record.get('siteId')].join(','),
            };
          }
          return {
            tenantId,
            siteId: record.get('siteId'),
          };

        },
        lovCode: ({ record }) => {
          if (record.get('organizationType') === 'LOCATOR') {
            return 'MT.MODEL.LOCATOR_BY_ORG';
          }
          return 'MT.MODEL.WORKCELL';

        },
        textField: ({ record }) => {
          switch (record.get('organizationType')) {
            case 'LOCATOR':
              return 'locatorCode';
            case 'WORKCELL':
              return 'workcellCode';
            default:
              return '';
          }
        },
        valueField: ({ record }) => {
          switch (record.get('organizationType')) {
            case 'LOCATOR':
              return 'locatorId';
            case 'WORKCELL':
              return 'workcellId';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.enable`).d('启用') },
          { value: 'N', key: intl.get(`tarzan.common.label.disable`).d('禁用') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
  fields: [
    {
      name: 'materialCateGoryName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCateGoryName`).d('所属类别'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.code`).d('编码'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('描述'),
    },
    {
      name: 'upperMaterialCateCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.upperMaterialCateCode`).d('上层组织'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'targetPlaceCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetPlaceCode`).d('目标地点'),
    },
    {
      name: 'distributionQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.distributionQty`).d('配送数量'),
    },
    {
      name: 'singleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.singleQty`).d('单包数量'),
    },
    {
      name: 'autoCallingFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoCallingFlag`).d('是否自动叫料'),
    },
    {
      name: 'distributionRouteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.distriButionRouteCode`).d('配送路线'),
    },
    {
      name: 'distributionCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.distributionCategory`).d('配送类别'),
    },
    {
      name: 'organizationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.organizationType`).d('目标点位类型'),
    },
    {
      name: 'organizationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.organizationCode`).d('目标点位'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`tarzan.common.label.enableFlag`).d('启用状态'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.enable`).d('启用') },
          { value: 'N', key: intl.get(`tarzan.common.label.disable`).d('禁用') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'packQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packQty`).d('配送包装数'),
    },
    {
      name: 'targetLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetLocator`).d('存储库位'),
    },
    {
      name: 'sourceLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceLocator`).d('来源库位'),
    },
    {
      name: 'multiplesOfPackFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.multiplesOfPackFlag`).d('整包配送'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'distributionModeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.distributionMode`).d('配送模式'),
    },
    {
      name: 'distributionCycle',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.distributionCycle`).d('配送周期'),
    },
    {
      name: 'materialConsumeRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialConsumeRate`).d('物料消耗速率'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('物料消耗速率单位'),
    },
    {
      name: 'bufferInventory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bufferInventory`).d('安全库存'),
    },
    {
      name: 'maxInventory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.maxInventory`).d('最大库存'),
    },
    {
      name: 'minInventory',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.minInventory`).d('最小库存'),
    },
    {
      name: 'bufferPeriod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bufferPeriod`).d('缓冲时间'),
    },
    {
      name: 'calendarCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.calendarCode`).d('时段日历'),
    },
    {
      name: 'instructionCreatedByEoFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionCreatedByEoFlag`).d('按作业拆分指令'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'fromScheduleRateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromScheduleRateFlag`).d('采用生产速率'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get('tarzan.common.label.yes').d('是') },
          { value: 'N', key: intl.get('tarzan.common.label.no').d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-pfep-distribution/pfep/query/ui`,
        method: 'GET',
      };
    },
  },
});

const targetPointOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui`,
        method: 'GET',
        params: { typeGroup: 'DISTRIBUTION_COVER_LOCATION_TYPE', tenantId },
      };
    },
  },
});

export { entranceDS, targetPointOptionDs };
