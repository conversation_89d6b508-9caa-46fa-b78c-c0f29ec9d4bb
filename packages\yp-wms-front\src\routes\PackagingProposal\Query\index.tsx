/**
 * @Description: 销售发运平台 - 入口页面
 * @Author: <EMAIL>
 * @Date: 2022/2/10 15:08
 */
import React from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
// import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { tableDS } from '../stores/ListDs';

const modelPrompt = 'hwms.packageProposalQuery';
const tenantId = getCurrentOrganizationId();

const queryFields = [
  {
    name: 'creationDateFrom',
    type: FieldType.dateTime,
    max: 'creationDateTo',
    label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
  },
  {
    name: 'creationDateTo',
    type: FieldType.dateTime,
    min: 'creationDateFrom',
    label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
  },
  {
    name: 'supplierLov',
    type: FieldType.object,
    label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    lovCode: 'MT.MODEL.SUPPLIER',
    multiple: true,
    lovPara: {
      tenantId,
    },
    ignore: FieldIgnore.always,
  },
  {
    name: 'supplierIds',
    bind: 'supplierLov.supplierId',
  },
  {
    name: 'materialLov',
    type: FieldType.object,
    label: intl.get(`${modelPrompt}.materialCode`).d('零件号'),
    lovCode: 'MT.METHOD.MATERIAL',
    ignore: FieldIgnore.always,
    multiple: true,
    lovPara: {
      tenantId,
    },
  },
  {
    name: 'materialIds',
    bind: 'materialLov.materialId',
  },
  {
    name: 'lastUpdateDateFrom',
    type: FieldType.dateTime,
    max: 'lastUpdateDateTo',
    label: intl.get(`${modelPrompt}.lastUpdateDateFrom`).d('最后更新时间从'),
  },
  {
    name: 'lastUpdateDateTo',
    type: FieldType.dateTime,
    min: 'lastUpdateDateFrom',
    label: intl.get(`${modelPrompt}.lastUpdateTo`).d('最后更新时间至'),
  },
  {
    name: 'packageType',
    type: FieldType.string,
    lookupCode: 'WMS.PACKAGE_TYPE',
    label: intl.get(`${modelPrompt}.packageType`).d('包装种类'),
  },
]

const packageProposalQuery = props => {
  const {
    tableDs,
    match: { path },
    customizeTable,
    history,
  } = props;

  const handleToDetail = (record) => {
    history.push(`/hwms/packaging-proposal/query/detail/${record.get('packageProposalId')}`);
  };

  const columns: ColumnProps[] = [
    { name: 'number', align: ColumnAlign.left },
    {
      name: 'statusDesc',
      lock: ColumnLock.left,
      width: 120,
    },
    { name: 'supplierCode' },
    { name: 'supplierName' },
    { name: 'materialCode', width: 150 },
    { name: 'materialName', align: ColumnAlign.right },
    { name: 'packageTypeDesc' },
    { name: 'creationDate' },
    { name: 'createByName' },
    { name: 'lastUpdateDate', width: 150 },
    { name: 'lastUpdateByName' },
    {
      header: intl.get(`${modelPrompt}.column.operation`).d('操作列'),
      align: ColumnAlign.center,
      lock: ColumnLock.right,
      width: 260,
      renderer: ({ record }) => {
        return (
          <span className="action-link">
            <a
              onClick={() => handleToDetail(record)}
            >
              {intl.get(`${modelPrompt}.create.materialBatch`).d('提案详细内容')}
            </a>
          </span>
        );
      },
    },
  ];

  const handleExport = async () => {
    const packageProposalIdList = tableDs.selected.map(item => item.get('packageProposalId'));
    // 请求后台
    const result = await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-package-proposals/packaging/export`, {
      method: 'POST',
      responseType: 'blob',
      body: {
        packageProposalIdList,
        ...tableDs.queryDataSet?.current?.toData(),
      },
    });
    const res = getResponse(result);
    if (res) {
      const file = new Blob([res], { type: 'application/vnd.ms-excel' });
      const fileURL = URL.createObjectURL(file);
      const fileName = '包装信息导出.xls';
      const elink = document.createElement('a');
      elink.download = fileName;
      elink.style.display = 'none';
      elink.href = fileURL;
      document.body.appendChild(elink);
      elink.click();
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink);
    }
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.packageProposalQuery`).d('包装信息查询')}>
        <PermissionButton
          type="c7n-pro"
          icon="file_download_black-o"
          onClick={() => handleExport()}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.export`).d('导出')}
        </PermissionButton>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.PACKAGING_PROPOSAL_QUERY_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.PACKAGING_PROPOSAL_QUERY_LIST.HEAD`,
          },
          <Table
            searchCode="xsfypt1"
            customizedCode="xsfypt1"
            dataSet={tableDs}
            columns={columns}
            highLightRow
            showCachedSelection={false}
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
              autoQuery: false,
            }}
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['hwms.packageProposalQuery', 'tarzan.common'] }),
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS('/wms-package-proposals/packaging/queryHead/ui'),
        queryFields,
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.PACKAGING_PROPOSAL_QUERY_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.PACKAGING_PROPOSAL_QUERY_LIST.HEAD`,
    ],
  }),
)(packageProposalQuery);
