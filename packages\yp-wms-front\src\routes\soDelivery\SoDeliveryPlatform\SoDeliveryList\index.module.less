.split-menu {
  :global {
    .c7n-menu-item > a,
    .c7n-menu-item > a:hover {
      color: #000;
    }
    .c7n-menu-item.c7n-menu-item-selected {
      background-color: inherit;
    }
  }
}
.icon-new {
  position: absolute;
  right: 0;
  top: 0;
  height: 24px;
  width: 24px;
  overflow: hidden;
  .icon-new-inner {
    position: absolute;
    bottom: 11px;
    left: 0;
    width: 34px;
    height: 12px;
    line-height: 14px;
    font-size: 10px;
    text-align: center;
    background-color: #29bece;
    color: #fff;
    font-weight: bolder;
    transform: rotate(45deg);
    .icon-new-inner-text {
      display: inline-block;
      transform: scale(0.65, 0.65);
    }
  }
}
.line-left {
  :global {
    .c7n-pro-input {
      border-radius: 4px 0 0 4px;
    }
  }
}
.line-right {
  :global {
    .c7n-pro-input {
      border-radius: 0 4px 4px 0;
      border-left: none;
    }
  }
}

.detail-excute{
  :global {
     table > tbody > tr > td{
      vertical-align: baseline !important;
    }
  }
}
