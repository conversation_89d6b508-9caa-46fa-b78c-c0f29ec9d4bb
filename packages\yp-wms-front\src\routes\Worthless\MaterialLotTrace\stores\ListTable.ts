/**
 * @Description: 物料批管理平台DS
 * @Author: <<EMAIL>>
 * @Date: 2022-01-20 17:28:53
 * @LastEditTime: 2022-05-17 10:17:10
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSet } from 'choerodon-ui/pro';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '../../../../../utils/config.js';
import { searchCopy } from '@utils/utils';
const modelPrompt = 'tarzan.hmes.product.materialLotTrance';

const Host = `${BASIC.HMES_BASIC}`;

const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  primaryKey: 'materialLotId',
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  pageSize: 20,
  queryDataSet: new DataSet({
    events: {
      update({ record, name, value }) {
        searchCopy(['identifications', 'materialLotCodes'], name, record, value);
      },
    },
    fields: [
      {
        name: 'identifications',
        type: FieldType.string,
        multiple: true,
        label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
      },
      {
        name: 'materialLotCodes',
        multiple: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      },
      {
        name: 'siteLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
        lovCode: 'WMS.NOWORTH_SITE',
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
        noCache: true,
      },
      {
        name: 'siteId',
        type: FieldType.number,
        bind: 'siteLov.siteId',
      },
      {
        name: 'materialLotStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
        textField: 'description',
        valueField: 'statusCode',
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'wareHouseLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
        lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
        multiple: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              locatorCategory: ['AREA'],
              siteId: record?.get('siteId') || null,
            };
          },
        },
      },
      {
        name: 'locatorLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
        lovCode: 'MT.MODEL.LOCATOR',
        multiple: true,
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              siteId: record?.get('siteId') || null,
            };
          },
        },
      },
      {
        name: 'materialLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        lovCode: 'MT.METHOD.MATERIAL',
        ignore: FieldIgnore.always,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'materialId',
        type: FieldType.number,
        bind: 'materialLov.materialId',
      },
      // {
      //   name: 'revisionCodes',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      //   multiple: true,
      // },
      {
        name: 'enableFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
        textField: 'meaning',
        valueField: 'value',
        lookupCode: 'MT.ENABLE_FLAG',
      },
      {
        name: 'wasteFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.wasteFlag`).d('固废属性'),
        textField: 'meaning',
        valueField: 'value',
        lookupCode: 'WMS.WASTE_FLAG',
      },
      // {
      //   name: 'qualityStatus',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      //   textField: 'description',
      //   valueField: 'statusCode',
      //   lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      //   lookupAxiosConfig: {
      //     transformResponse(data) {
      //       // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
      //       if (data instanceof Array) {
      //         return data;
      //       }
      //       const { rows } = JSON.parse(data);
      //       return rows;
      //     },
      //   },
      // },
      // {
      //   name: 'createReason',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.createReason`).d('创建原因'),
      //   textField: 'description',
      //   valueField: 'typeCode',
      //   lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=CREATE_REASON`,
      //   lookupAxiosConfig: {
      //     transformResponse(data) {
      //       // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
      //       if (data instanceof Array) {
      //         return data;
      //       }
      //       const { rows } = JSON.parse(data);
      //       return rows;
      //     },
      //   },
      // },
      {
        name: 'instructionDocLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.instructionDocNum`).d('指令单据编码'),
        lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.INSTRUCTION_DOC`,
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
        multiple: true,
      },
      {
        name: 'instructionDocIdList',
        bind: 'instructionDocLov.instructionDocId',
      },
      {
        name: 'instructionDocNumList',
        bind: 'instructionDocLov.instructionDocNum',
      },
      {
        name: 'produceLot',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.produceLot`).d('产生批次'),
      },
      {
        name: 'produceTimeStart',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.produceTimeStart`).d('产生时间从'),
        max: 'produceTimeEnd',
      },
      {
        name: 'produceTimeEnd',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.produceTimeEnd`).d('产生时间至'),
        min: 'produceTimeStart',
      },
      {
        name: 'unitName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.unitName`).d('行业俗称/单位内部名称'),
      },
      {
        name: 'directoryName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.directoryName`).d('国家危险废物名录名称'),
      },
      {
        name: 'containerCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.containerCode`).d('容器/包装编码'),
      },
      {
        name: 'containerType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.containerType`).d('容器/包装类型'),
        lookupCode: 'WMS.WXGF.MATERIAL_CONTAINER_TYPE',
      },
      {
        name: 'wasteMaterial',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.wasteMaterial`).d('容器/包装材质'),
        lookupCode: 'WMS.WXGF.MATERIAL_CONTAINER_MATERIAL',
      },
      {
        name: 'wasteSpecification',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.wasteSpecification`).d('容器/包装规格'),
        lookupCode: 'WMS.WXGF.MATERIAL_CONTAINER_SPEC',
      },
      {
        name: 'containerQty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.containerQty`).d('容器/包装数量'),
      },
      {
        name: 'wasteContainer',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.wasteContainer`).d('产生危险废物设施编码'),
      },
      {
        name: 'productAgent',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.productAgent`).d('产生部门经办人'),
      },
      {
        name: 'productAgentPhone',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.productAgentPhone`).d('产生部门经办人手机号'),
      },
      {
        name: 'transferAgent',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.transferAgent`).d('运送部门经办人'),
      },
      {
        name: 'trace',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.trace`).d('去向'),
        lookupCode: 'WMS.WXGF.MATERIAL_CONTAINER_TOGO',
      },
      // {
      //   name: 'ownerType',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
      //   options: new DataSet({
      //     autoQuery: true,
      //     dataKey: 'rows',
      //     paging: false,
      //     transport: {
      //       read: () => {
      //         return {
      //           url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=OWNER_TYPE`,
      //           method: 'GET',
      //           params: { tenantId },
      //           transformResponse: val => {
      //             const data = JSON.parse(val);
      //             data.rows.push({
      //               description: intl.get(`tarzan.common.ownerType`).d('自有'),
      //               typeCode: 'ALL',
      //               typeGroup: 'OWNER_TYPE',
      //             });
      //             return {
      //               ...data,
      //             };
      //           },
      //         };
      //       },
      //     },
      //   }),
      //   textField: 'description',
      //   valueField: 'typeCode',
      // },
      // {
      //   name: 'ownerLov',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
      //   lovCode: 'MT.MODEL.CUSTOMER',
      //   ignore: FieldIgnore.always,
      //   lovPara: {
      //     tenantId,
      //   },
      //   dynamicProps: {
      //     lovCode({ record }) {
      //       switch (record.get('ownerType')) {
      //         case 'CI':
      //         case 'IIC':
      //           return 'MT.MODEL.CUSTOMER';
      //         case 'SI':
      //         case 'IIS':
      //         case 'OD':
      //           return 'MT.MODEL.SUPPLIER';
      //         case 'OI':
      //           return `${BASIC.LOV_CODE_BEFORE}.MES.SO_LINE`;
      //         default:
      //           return 'MT.MES.EMPTY';
      //       }
      //     },
      //     textField({ record }) {
      //       switch (record.get('ownerType')) {
      //         case 'CI':
      //         case 'IIC':
      //           return 'customerCode';
      //         case 'SI':
      //         case 'IIS':
      //         case 'OD':
      //           return 'supplierCode';
      //         case 'OI':
      //           return 'soNumContent';
      //         default:
      //           return 'noData';
      //       }
      //     },
      //     disabled({ record }) {
      //       return !['CI', 'IIC', 'SI', 'IIS', 'OI', 'OD'].includes(record.get('ownerType'));
      //     },
      //   },
      // },
      // {
      //   name: 'ownerId',
      //   type: FieldType.number,
      //   bind: 'ownerLov.customerId',
      //   dynamicProps: {
      //     bind({ record }) {
      //       switch (record.get('ownerType')) {
      //         case 'CI':
      //         case 'IIC':
      //           return 'ownerLov.customerId';
      //         case 'SI':
      //         case 'IIS':
      //         case 'OD':
      //           return 'ownerLov.supplierId';
      //         case 'OI':
      //           return 'ownerLov.soLineId';
      //         default:
      //           return 'ownerLov.customerId';
      //       }
      //     },
      //   },
      // },
      // {
      //   name: 'reservedFlag',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.reservedFlag`).d('预留标识'),
      //   textField: 'meaning',
      //   valueField: 'value',
      //   lookupCode: 'MT.YES_NO',
      // },
      // {
      //   name: 'reservedObjectType',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.reservedObjectTypeDesc`).d('预留对象类型'),
      //   textField: 'description',
      //   valueField: 'typeCode',
      //   lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=RESERVE_OBJECT_TYPE`,
      //   lookupAxiosConfig: {
      //     transformResponse(data) {
      //       // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
      //       if (data instanceof Array) {
      //         return data;
      //       }
      //       const { rows } = JSON.parse(data);
      //       return rows;
      //     },
      //   },
      // },
      // {
      //   name: 'reservedObjectLov',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象编码'),
      //   lovCode: 'MT.MODEL.CUSTOMER',
      //   lovPara: {
      //     tenantId,
      //   },
      //   ignore: FieldIgnore.always,
      //   dynamicProps: {
      //     lovCode: ({ record }) => {
      //       switch (record.get('reservedObjectType')) {
      //         case 'CUSTOMER':
      //           return 'MT.MODEL.CUSTOMER';
      //         case 'WO':
      //           return `${BASIC.LOV_CODE_BEFORE}.WORK_ORDER`;
      //         case 'EO':
      //           return `${BASIC.LOV_CODE_BEFORE}.EO`;
      //         case 'PO_LINE':
      //           return `${BASIC.LOV_CODE_BEFORE}.MES.PO_LINE`;
      //         default:
      //           return 'MT.MES.EMPTY';
      //       }
      //     },
      //     textField: ({ record }) => {
      //       switch (record.get('reservedObjectType')) {
      //         case 'CUSTOMER':
      //           return 'customerCode';
      //         case 'WO':
      //           return 'workOrderNum';
      //         case 'EO':
      //           return 'eoNum';
      //         case 'PO_LINE':
      //           return 'poNumberAndLine';
      //         default:
      //           return 'noData';
      //       }
      //     },
      //     disabled: ({ record }) => {
      //       return (
      //         !record.get('reservedObjectType') ||
      //         ['OO', 'DRIVING'].includes(record.get('reservedObjectType'))
      //       );
      //     },
      //   },
      // },
      // {
      //   name: 'reservedObjectId',
      //   type: FieldType.number,
      //   bind: 'reservedObjectLov.customerId',
      //   dynamicProps: {
      //     bind({ record }) {
      //       switch (record.get('reservedObjectType')) {
      //         case 'CUSTOMER':
      //           return 'reservedObjectLov.customerId';
      //         case 'WO':
      //           return 'reservedObjectLov.workOrderId';
      //         case 'EO':
      //           return 'reservedObjectLov.eoId';
      //         case 'PO_LINE':
      //           return 'reservedObjectLov.poLineId';
      //         default:
      //           return 'reservedObjectLov.customerId';
      //       }
      //     },
      //   },
      // },
      // {
      //   name: 'eoLov',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      //   lovCode: `${BASIC.LOV_CODE_BEFORE}.EO`,
      //   lovPara: {
      //     tenantId,
      //   },
      //   ignore: FieldIgnore.always,
      // },
      // {
      //   name: 'eoId',
      //   bind: 'eoLov.eoId',
      // },
      // {
      //   name: 'supplierLov',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      //   lovCode: 'MT.MODEL.SUPPLIER',
      //   lovPara: {
      //     tenantId,
      //   },
      //   ignore: FieldIgnore.always,
      // },
      // {
      //   name: 'supplierId',
      //   bind: 'supplierLov.supplierId',
      // },
      // {
      //   name: 'customerLov',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.customerCode`).d('客户编码'),
      //   lovCode: 'MT.MODEL.CUSTOMER',
      //   lovPara: {
      //     tenantId,
      //   },
      //   ignore: FieldIgnore.always,
      // },
      // {
      //   name: 'customerId',
      //   bind: 'customerLov.customerId',
      // },
      // {
      //   name: 'materialLotStatus',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
      //   textField: 'description',
      //   valueField: 'statusCode',
      //   lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
      //   lookupAxiosConfig: {
      //     transformResponse(data) {
      //       if (data instanceof Array) {
      //         return data;
      //       }
      //       const { rows } = JSON.parse(data);
      //       return rows;
      //     },
      //   },
      // },
      // {
      //   name: 'stocktakeFlag',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.stocktakeFlag`).d('盘点停用标识'),
      //   lovPara: { tenantId },
      //   textField: 'meaning',
      //   valueField: 'value',
      //   lookupCode: 'MT.YES_NO',
      // },
      // {
      //   name: 'inSiteTimeStart',
      //   type: FieldType.dateTime,
      //   label: intl.get(`${modelPrompt}.inSiteTimeStart`).d('入站时间从'),
      //   max: 'inSiteTimeEnd',
      // },
      // {
      //   name: 'inSiteTimeEnd',
      //   type: FieldType.dateTime,
      //   label: intl.get(`${modelPrompt}.inSiteTimeEnd`).d('出站时间至'),
      //   min: 'inSiteTimeStart',
      // },
      // {
      //   name: 'freezeFlag',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.freezeFlag`).d('冻结标识'),
      //   lovPara: { tenantId },
      //   textField: 'meaning',
      //   valueField: 'value',
      //   lookupCode: 'MT.YES_NO',
      // },
      // {
      //   name: 'currentContainerLov',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.currentContainerCode`).d('装载容器编码'),
      //   lovCode: `${BASIC.LOV_CODE_BEFORE}.CONTAINER`,
      //   ignore: FieldIgnore.always,
      //   lovPara: {
      //     tenantId,
      //   },
      // },
      // {
      //   name: 'currentContainerId',
      //   bind: 'currentContainerLov.containerId',
      // },
      // {
      //   name: 'topContainerLov',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.topContainerCode`).d('顶层容器编码'),
      //   lovCode: `${BASIC.LOV_CODE_BEFORE}.CONTAINER`,
      //   ignore: FieldIgnore.always,
      //   lovPara: {
      //     tenantId,
      //   },
      // },
      // {
      //   name: 'topContainerId',
      //   bind: 'topContainerLov.containerId',
      // },
      // {
      //   name: 'ovenNumber',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.ovenNumber`).d('炉号'),
      // },
      // {
      //   name: 'assembleToolLov',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.assembleToolCode`).d('装配器具编码'),
      //   lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.ASSEMBLE_TOOL`,
      //   lovPara: {
      //     tenantId,
      //   },
      //   ignore: FieldIgnore.always,
      // },
      // {
      //   name: 'assembleToolId',
      //   bind: 'assembleToolLov.assembleToolId',
      // },
      // {
      //   name: 'assemblePointLov',
      //   type: FieldType.object,
      //   label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
      //   lovCode: `${BASIC.LOV_CODE_BEFORE}.ASSEMBLE_POINT`,
      //   ignore: FieldIgnore.always,
      //   dynamicProps: {
      //     disabled: ({ record }) => {
      //       return !record.get('assembleToolId');
      //     },
      //     lovPara: ({ record }) => {
      //       return {
      //         assembleToolId: record.get('assembleToolId'),
      //         tenantId,
      //       };
      //     },
      //   },
      // },
      // {
      //   name: 'assemblePointId',
      //   bind: 'assemblePointLov.assemblePointId',
      // },
      // {
      //   name: 'supplierLotList',
      //   type: FieldType.string,
      //   label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
      //   multiple: true,
      // },
    ],
  }),
  fields: [
    {
      name: 'materialLotId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialLotId`).d('物料批ID'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatusDesc`).d('物料批状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('主单位数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('主单位'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('指令单据编码'),
    },
    {
      name: 'produceLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.produceLot`).d('产生批次'),
    },
    {
      name: 'produceTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.produceTime`).d('产生时间'),
    },
    {
      name: 'unitName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unitName`).d('行业俗称/单位内部名称'),
    },
    {
      name: 'directoryName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.directoryName`).d('国家危险废物名录名称'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器/包装编码'),
    },
    {
      name: 'containerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerType`).d('容器/包装类型'),
    },
    {
      name: 'containerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeDesc`).d('容器/包装类型描述'),
    },
    {
      name: 'wasteMaterial',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wasteMaterial`).d('容器/包装材质'),
    },
    {
      name: 'wasteMaterialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wasteMaterialDesc`).d('容器/包装材质描述'),
    },
    {
      name: 'wasteSpecification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wasteSpecification`).d('容器/包装规格'),
    },
    {
      name: 'wasteSpecificationDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wasteSpecificationDesc`).d('容器/包装规格描述'),
    },
    {
      name: 'containerQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerQty`).d('容器/包装数量'),
    },
    {
      name: 'wasteContainer',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wasteContainer`).d('产生危险废物设施编码'),
    },
    {
      name: 'productAgent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productAgent`).d('产生部门经办人'),
    },
    {
      name: 'productAgentPhone',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productAgentPhone`).d('产生部门经办人手机号'),
    },
    {
      name: 'transferAgent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferAgent`).d('运送部门经办人'),
    },
    {
      name: 'trace',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trace`).d('去向'),
    },
    {
      name: 'traceDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.traceDesc`).d('去向描述'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'mainIngredients',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mainIngredients`).d('主要成分'),
    },
    {
      name: 'attention',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attention`).d('注意事项'),
    },
    {
      name: 'attentionDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attentionDesc`).d('注意事项描述'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  transport: {
    read: config => {
      const { data } = config;
      // 查询请求的 axios 配置或 url 字符串
      return {
        ...config,
        data: {
          ...data,
          ownerType: data.ownerType === 'ALL' ? '' : data.ownerType,
          locatorIds: (data.locatorLov || []).map(item => item.locatorId),
          wareHouseIds: (data.wareHouseLov || []).map(item => item.locatorId),
          identifyType: 'MATERIAL_LOT',
        },
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-label-manage/material-lot/ui`,
        // url: `http://172.22.5.151:30080/yp-mes-40066/v1/${tenantId}/wms-worthless-label-manage/material-lot/ui`,
        method: 'POST',
      };
    },
  },
});

const editTableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'materialLotId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialLotId`).d('物料批ID'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatusDesc`).d('物料批状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('主单位数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('主单位'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('指令单据编码'),
    },
    {
      name: 'produceLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.produceLot`).d('产生批次'),
    },
    {
      name: 'produceTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.produceTime`).d('产生时间'),
    },
    {
      name: 'unitName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unitName`).d('行业俗称/单位内部名称'),
    },
    {
      name: 'directoryName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.directoryName`).d('国家危险废物名录名称'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器/包装编码'),
    },
    {
      name: 'containerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerType`).d('容器/包装类型'),
    },
    {
      name: 'containerQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerQty`).d('容器/包装数量'),
    },
    {
      name: 'wasteContainer',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wasteContainer`).d('产生危险废物设施编码'),
    },
    {
      name: 'productAgent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productAgent`).d('产生部门经办人'),
    },
    {
      name: 'transferAgent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferAgent`).d('运送部门经办人'),
    },
    {
      name: 'trace',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trace`).d('去向'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
  ],
  transport: {
    read: config => {
      const { data } = config;
      // 查询请求的 axios 配置或 url 字符串
      return {
        ...config,
        data: {
          ...data,
          ownerType: data.ownerType === 'ALL' ? '' : data.ownerType,
          locatorIds: (data.locatorLov || []).map(item => item.locatorId),
          wareHouseIds: (data.wareHouseLov || []).map(item => item.locatorId),
          identifyType: 'MATERIAL_LOT',
        },
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-label-manage/material-lot/ui`,
        // url: `http://172.22.5.151:30080/yp-mes-40066/v1/${tenantId}/wms-worthless-label-manage/material-lot/ui`,
        method: 'POST',
      };
    },
  },
});

const creattableDS: () => DataSetProps = () => ({
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  pageSize: 10,

  fields: [
    {
      name: 'materialLotId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialLotId`).d('物料批ID'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      // dynamicProps: {
      //   disabled: ({ record }) => {
      //     return true
      //   }
      // }
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'WMS.NOWORTH_SITE',
      lovPara: {
        tenantId,
      },

      ignore: FieldIgnore.always,
      noCache: true,
      required: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'WMS.WASTE_MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            wasteFlag: 'N',
            siteId: record?.get('siteId') || null,
          };
        },
      },
      required: true,
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialLov.materialCode',
    },
    {
      name: 'wareHouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      // multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorCategory: ['AREA'],
            siteId: record?.get('siteId') || null,
          };
        },
      },
      required: true,
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'wareHouseLov.locatorId',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'wareHouseLov.locatorCode',
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
      min: 0,
      required: true,
    },
    {
      name: 'produceArea',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.produceArea`).d('产生车间/区域'),
      required: true,
    },
    {
      name: 'produceProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.produceProcess`).d('产生工序'),
      required: true,
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${Host}/v1/${tenantId}/wms-worthless-label-manage/material-lot/ui`,
        method: 'POST',
        data: {
          ...data,
        },
      };
    },
  },
});

const dangerousCreattableDS: () => DataSetProps = () => ({
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  pageSize: 10,
  autoCreate: true,
  fields: [
    {
      name: 'materialLotId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialLotId`).d('物料批ID'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'WMS.NOWORTH_SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      noCache: true,
      required: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'WMS.WASTE_MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            wasteFlag: 'H',
            siteId: record?.get('siteId') || null,
          };
        },
      },
      required: true,
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialLov.materialCode',
    },
    {
      name: 'wareHouseLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      // multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            locatorCategory: ['AREA'],
            siteId: record?.get('siteId') || null,
          };
        },
      },
      required: true,
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'wareHouseLov.locatorId',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      bind: 'wareHouseLov.locatorCode',
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
      min: 0,
      required: true,
    },
    {
      name: 'produceLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.produceLot`).d('产生批次'),
      disabled: true,
    },
    {
      name: 'produceTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.produceTime`).d('产生时间'),
      required: true,
    },
    {
      name: 'unitName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unitName`).d('行业俗称'),
    },
    // {
    //   name: 'directoryName',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.directoryName`).d('国家危险废物名录名称'),
    //   required: true,
    // },
    // {
    //   name: 'containerCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.containerCode`).d('容器/包装编码'),

    //   required: true
    // },
    {
      name: 'containerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerType`).d('容器/包装类型'),
      lookupCode: 'WMS.WXGF.MATERIAL_CONTAINER_TYPE',
      required: true,
    },
    {
      name: 'wasteMaterial',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wasteMaterial`).d('容器/包装材质'),
      lookupCode: 'WMS.WXGF.MATERIAL_CONTAINER_MATERIAL',
      required: true,
    },
    {
      name: 'wasteSpecification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wasteSpecification`).d('容器/包装规格'),
      lookupCode: 'WMS.WXGF.MATERIAL_CONTAINER_SPEC',
      required: true,
    },
    {
      name: 'containerQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerQty`).d('容器/包装数量'),

      required: true,
    },
    {
      name: 'wasteContainer',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wasteContainer`).d('产生危险废物设施编码'),
    },
    {
      name: 'productAgent',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productAgent`).d('产生部门经办人'),
      required: true,
    },
    {
      name: 'productAgentPhone',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productAgentPhone`).d('产生部门经办人手机号'),
      required: true,
    },
    {
      name: 'transferAgent',
      required:true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferAgent`).d('运送部门经办人'),
    },
    {
      name: 'trace',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.trace`).d('去向'),
      lookupCode: 'WMS.WXGF.MATERIAL_CONTAINER_TOGO',
      required: true,
    },
    {
      name: 'produceArea',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.produceArea`).d('产生车间/区域'),
      required: true,
    },
    // {
    //   name: 'produceProcess',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.produceProcess`).d('产生工序'),
    //   required: true,
    // },
    {
      name: 'mainIngredients',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.mainIngredients`).d('主要成分'),
      required: true,
    },
    {
      name: 'attention',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attention`).d('注意事项'),
      lookupCode: 'WMS.WXGF.MATERIAL_CONTAINER_PREC',
      required: true,
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${Host}/v1/${tenantId}/wms-worthless-label-manage/material-lot/ui`,
        method: 'POST',
        data: {
          ...data,
        },
      };
    },
  },
});

const historyDS: () => DataSetProps = () => ({
  primaryKey: 'materialLotHisId',
  selection: false,
  autoQuery: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'eventId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型编码'),
    },
    {
      name: 'eventTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件类型描述'),
    },
    {
      name: 'eventRequestId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventRequestId`).d('事件请求ID'),
    },
    {
      name: 'requestTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeCode`).d('事件请求类型编码'),
    },
    {
      name: 'requestTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requestTypeDesc`).d('事件请求类型描述'),
    },
    {
      name: 'eventUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventUserName`).d('操作人'),
    },
    {
      name: 'eventTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTime`).d('操作时间'),
    },
    {
      name: 'materialLotId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialLotId`).d('物料批ID'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'productionDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
    },
    {
      name: 'expirationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.expirationDate`).d('到期日期'),
    },
    {
      name: 'extendedShelfLifeTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.extendedShelfLifeTimes`).d('延保次数'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('主单位数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('主单位'),
    },
    {
      name: 'secondaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondaryUomQty`).d('辅单位数量'),
    },
    {
      name: 'secondaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondaryUomCode`).d('辅单位'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'ownerTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
    },
    {
      name: 'ownerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerDesc`).d('所有者描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierDesc`).d('供应商描述'),
    },
    {
      name: 'supplierSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSiteCode`).d('供应商地点'),
    },
    {
      name: 'supplierSiteDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierSiteDesc`).d('供应商地点描述'),
    },
    {
      name: 'customerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户编码'),
    },
    {
      name: 'customerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerDesc`).d('客户描述'),
    },
    {
      name: 'customerSiteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerSiteCode`).d('客户地点'),
    },
    {
      name: 'customerSiteDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerSiteDesc`).d('客户地点描述'),
    },
    {
      name: 'reservedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedFlag`).d('预留标识'),
    },
    {
      name: 'reservedObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectTypeDesc`).d('预留对象类型'),
    },
    {
      name: 'reservedObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象编码'),
    },
    {
      name: 'assembleToolCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleToolCode`).d('装配器具编码'),
    },
    {
      name: 'assemblePointCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
    },
    {
      name: 'createReasonDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createReasonDesc`).d('创建原因'),
    },
    {
      name: 'unloadTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unloadTime`).d('卸载时间'),
    },
    {
      name: 'loadTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadTime`).d('装载时间'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    },
    {
      name: 'ovenNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ovenNumber`).d('炉号'),
    },
    {
      name: 'inLocatorTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTime`).d('入库时间'),
    },
    {
      name: 'inSiteTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inSiteTime`).d('入站时间'),
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('冻结标识'),
    },
    {
      name: 'stocktakeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeFlag`).d('盘点停用标识'),
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('指令单据编码'),
    },
    {
      name: 'instructionNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionNum`).d('指令编码'),
    },
    {
      name: 'currentContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentContainerCode`).d('装载容器编码'),
    },
    {
      name: 'topContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerCode`).d('顶层容器编码'),
    },
    {
      name: 'printTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdUsername',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdUsername`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedUsername',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedUsername`).d('最后更新人'),
    },
    {
      name: 'trxPrimaryQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.trxPrimaryQty`).d('主单位变更数量'),
    },
    {
      name: 'trxSecondaryQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.trxSecondaryQty`).d('辅单位变更数量'),
    },
    {
      name: 'overOrderInterceptionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overOrderInterceptionFlag`).d('跨订单拦截标识'),
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('冻结/解冻原因'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('冻结/解冻备注'),
    },
  ],
  transport: {
    read: config => {
      const { data } = config;
      // 查询请求的 axios 配置或 url 字符串
      return {
        ...config,
        data: data.selectedMaterialLotList,
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/material-lot/his/ui`,
        method: 'POST',
      };
    },
  },
});

const freezeDetailDS = params => ({
  autoQuery: false,
  dataKey: 'rows',
  paging: false,
  autoCreate: true,
  fields: [
    {
      name: 'reason',
      label: intl.get(`${modelPrompt}.selectReason`).d('选择原因'),
      lookupCode: 'WMS.INVENTORY_FREEZE_REASON',
      type: 'object',
      required: params,
      textField: 'meaning',
      valueField: 'meaning',
    },
    {
      name: 'Remark',
      type: 'string',
      label: intl.get(`${modelPrompt}.remark`).d('备注信息'),
      dynamicProps: {
        disabled: ({ record }) => {
          const data = record.get('reason');
          return data?.value !== 'OTHER';
        },
        required: ({ record }) => {
          const data = record.get('reason');
          return data?.value === 'OTHER';
        },
      },
    },
  ],
});

export { tableDS, historyDS, freezeDetailDS, creattableDS, dangerousCreattableDS, editTableDS };
