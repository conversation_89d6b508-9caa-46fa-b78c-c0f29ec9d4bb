/**
 * @Description: 事务明细报表-主表格-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-25 14:54:49
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.wms.supplier.monthlyTimelinessReport';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'dimension',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dimension`).d('汇总维度'),
      lookupCode: 'WMS.SUMMARY_TYPE',
      required: true,
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      multiple: true,
    },
    {
      name: 'supplierIds',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'receiveDoodsTimeFrom',
      label: intl.get(`${modelPrompt}.receiveDoodsTimeFrom`).d('收货日期从'),
      max: 'receiveDoodsTimeTo',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('dimension');
        },
        type: ({ record }) => {
          return record.get('dimension') === 'month' ? FieldType.month : FieldType.date;
        },
      },
    },
    {
      name: 'receiveDoodsTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.receiveDoodsTimeTo`).d('收货日期至'),
      min: 'receiveDoodsTimeFrom',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('dimension');
        },
        type: ({ record }) => {
          return record.get('dimension') === 'month' ? FieldType.month : FieldType.date;
        },
      },
    },
  ],
  fields: [
    {
      name: 'receiveDoodsTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveDoodsTime`).d('月份/日期'),
    },
    {
      name: 'planQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planQty`).d('计划到货单数'),
    },
    {
      name: 'punctualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.punctualQty`).d('准时到货单数'),
    },
    {
      name: 'noPunctualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.noPunctualQty`).d('未及时到货单数'),
    },
    {
      name: 'punctualRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.punctualRate`).d('到货及时率'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-month-delivery-timely/list/get/ui`,
        method: 'GET',
      };
    },
  },
});

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  fields: [
    {
      name: 'receiveDoodsTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receiveDoodsTime`).d('月份/日期'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'planQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planQty`).d('计划到货单数'),
    },
    {
      name: 'punctualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.punctualQty`).d('准时到货单数'),
    },
    {
      name: 'noPunctualQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.noPunctualQty`).d('未及时到货单数'),
    },
    {
      name: 'punctualRate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.punctualRate`).d('到货及时率'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-month-delivery-timely/detail/list/get/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS, detailDS };
