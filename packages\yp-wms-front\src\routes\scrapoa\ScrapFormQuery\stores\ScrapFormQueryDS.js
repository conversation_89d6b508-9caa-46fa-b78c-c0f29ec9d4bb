// 报废单查询
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.scrapoa.ScrapFormQuery';
const tenantId = getCurrentOrganizationId();

// 头表格ds
const headTableDS = () => {
  return {
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
      },
      {
        name: 'instructionDocNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.instructionDocNum`).d('报废单号'),
      },
      {
        name: 'instructionDocStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.instructionDocStatusDesc`).d('单据状态'),
      },
      {
        name: 'costcenterCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.costcenterCode`).d('成本中心'),
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('创建人'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-scrap-docs/head/list/ui`,
          method: 'GET',
          data,
        };
      },
    },
  };
};

// 行表格ds
const lineTableDS = () => {
  return {
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'lineNumber',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      },
      {
        name: 'lineStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineStatusDesc`).d('行状态'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'quantity',
        type: 'string',
        label: intl.get(`${modelPrompt}.quantity`).d('需执行数量'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'sumActualQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.sumActualQty`).d('执行数量'),
      },
      {
        name: 'fromLocatorCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.fromLocatorCode`).d('来源仓库'),
      },
      {
        name: 'toLocatorCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.toLocatorCode`).d('目标仓库'),
      },
      {
        name: 'option',
        type: 'string',
        label: intl.get(`${modelPrompt}.option`).d('操作'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-scrap-docs/line/list/ui`,
          method: 'GET',
          data,
        };
      },
    },
  };
};

// 明细表格ds
const detailTableDS = () => {
  return {
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批标识'),
      },
      {
        name: 'materialLotStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotStatusDesc`).d('物料批状态'),
      },
      {
        name: 'containerCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
      },
      {
        name: 'primaryUomQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'lot',
        type: 'string',
        label: intl.get(`${modelPrompt}.lot`).d('批次'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-scrap-docs/detail/list/ui`,
          method: 'GET',
          data,
        };
      },
    },
  };
};

export { headTableDS, lineTableDS, detailTableDS };
