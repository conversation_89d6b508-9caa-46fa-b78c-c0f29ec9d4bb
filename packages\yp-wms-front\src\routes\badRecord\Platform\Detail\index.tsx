import React, { useEffect, useMemo, useState } from 'react';
import {
  Button,
  DataSet,
  Form,
  Lov,
  Modal,
  Select,
  Table,
  TextArea,
  TextField,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react';
import { Collapse, Popconfirm, Tag } from 'choerodon-ui';
import { ButtonColor, FuncType } from 'choerodon-ui/pro/lib/button/enum';
// import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { Content, Header } from 'components/Page';
import intl from 'utils/intl';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { useDataSetEvent } from 'utils/hooks';
import {
  // AttributeDrawer,
  C7nFormItemSort,
  TarzanSpin,
  // drawerPropsC7n,
} from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { getCurrentOrganizationId } from 'utils/utils';
// import uuid from 'uuid/v4';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import {
  detailDS,
  // ncDetailLineDS,
  scanFormDS,
} from '../stores/detailDS';
import scanImg from '@/assets/scan-o.svg';
import { ncRecordDS } from '../stores/detailRecordTypeDS';
import openBatchBarCodeModal from './BatchBarCodeModal';
import InputLovDS from '../stores/InputLovDS';

import {
  LineDelete,
  FetchEoRelatedInfo,
  FetchMaterialLotRelatedInfo,
  SaveNcRecord,
  ScanMaterialLotRelatedInfo,
  ScanEoRelatedInfo,
  QueryRevision,
  FetchOperation,
  InitiateReview,
  QuickScrapping,
  ScrappingConfirm,
  RecordCancel,
} from '../services';
import { fetchDefaultSite } from '../services/api';

const { Option } = Select;
const { Panel } = Collapse;
const modelPrompt = 'tarzan.mes.event.badRecordPlatform';

const BadRecordDetail = observer(props => {
  const {
    history,
    match: { params },
    // customizeForm,
    // customizeTable,
    // custConfig,
  } = props;
  const { id } = params as any;
  // 不良对象类型
  const [ncRecordType, setNcRecordType] = useState<string>('');
  // 版本
  const [revisionList, setRevisionList] = useState<any>([]);
  // 是否能编辑
  const [canEdit, setCanEdit] = useState<boolean>(id === 'create');
  // 折叠activeKey
  const [activeKey, setActiveKey] = useState<any>([
    'badRecordFormInfo',
    'badRecordLineInfo',
    'badRecordLineDetail',
  ]);
  const inputLovDS = new DataSet({ ...InputLovDS() });

  const ncRecordDs = useMemo(() => new DataSet(ncRecordDS()), []); // 库存不良
  const scanFormDs = useMemo(() => new DataSet(scanFormDS()), []);
  const modalDs = useMemo(
    () =>
      new DataSet({
        fields: [
          {
            name: 'intendedDisposal',
            required: true,
            type: FieldType.string,
            lookupCode: 'MT.MES.NC_DISPOSAL_METHOD',
            label: intl.get(`${modelPrompt}.intendedDisposal`).d('欲处置结果'),
          },
          // {
          //   name: 'costCenterLov',
          //   type: FieldType.object,
          //   label: intl.get(`${modelPrompt}.costCenterLov`).d('成本中心'),
          //   lovCode: 'YP_WMS.MES.COST_CENTER',
          //   ignore: FieldIgnore.always,
          //   textField: 'description',
          //   required: true,
          // },
          // {
          //   name: 'costCenter',
          //   bind: 'costCenterLov.costcenterCode',
          // },
          // {
          //   name: 'description',
          //   bind: 'costCenterLov.description',
          // },
        ],
      }),
    [],
  );

  // 详情界面Ds
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  const { run: scanMaterialLotRelatedInfo, loading: scanMaterialLotLoading } = useRequest(
    ScanMaterialLotRelatedInfo(),
    {
      manual: true,
      needPromise: true,
    },
  );
  const { run: scanEoRelatedInfo, loading: scanEoLoading } = useRequest(ScanEoRelatedInfo(), {
    manual: true,
  });
  // 获取物料批关联信息
  const { run: fetchMaterialLotRelatedInfo, loading: fetchMaterialLotLoading } = useRequest(
    FetchMaterialLotRelatedInfo(),
    {
      needPromise: true,
      manual: true,
    },
  );
  // 获取EO关联信息
  const { run: fetchEoRelatedInfo, loading: fetchEoLoading } = useRequest(FetchEoRelatedInfo(), {
    needPromise: true,
    manual: true,
  });
  // 保存
  const { run: saveNcRecord, loading: saveLoading } = useRequest(SaveNcRecord(), {
    manual: true,
  });

  // 记录行删除
  const { run: lineDelete, loading: lineDeleteLoading } = useRequest(LineDelete(), {
    manual: true,
    needPromise: true,
  });
  // 查物料版本
  const { run: queryRevision, loading: queryRevisionLoading } = useRequest(QueryRevision(), {
    manual: true,
    needPromise: true,
  });
  // 状态取消
  const { run: cancel, loading: cancelLoading } = useRequest(RecordCancel(), {
    manual: true,
    needPromise: true,
  });
  // 根据工位查询工艺和设备
  const { run: fetchOperation, loading: fetchOperationLoading } = useRequest(FetchOperation(), {
    manual: true,
    needPromise: true,
  });
  // 发起评审
  const { run: initiateReview, loading: initiateReviewLoading } = useRequest(InitiateReview(), {
    manual: true,
    needPromise: true,
  });
  // 快速报废
  const { run: quickScrapping, loading: quickScrappingLoading } = useRequest(QuickScrapping(), {
    manual: true,
    needPromise: true,
  });
  //  报废确认

  const { run: scrappingConfirm, loading: scrappingConfirmLoading } = useRequest(
    ScrappingConfirm(),
    {
      manual: true,
      needPromise: true,
    },
  );
  useEffect(() => {
    if (id === 'create') {
      fetchDefaultSite().then(res => {
        if (res && res.success) {
          detailDs.current?.set('siteLov', res.rows);
          detailDs.current?.set('siteId', res.rows?.siteId);
          detailDs.current?.set('siteName', res.rows?.siteName);
          detailDs.current?.set('siteCode', res.rows?.siteCode);
        }
      });
      return;
    }
    handleQueryDetail(id);
  }, [id]);

  const handleQueryDetail = id => {
    // detailDs.setQueryParameter(
    //   'customizeUnitCode',
    //   `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.HEAD`,
    // );
    detailDs.setQueryParameter('ncIncidentId', id);
    detailDs.query().then(res => {
      const { ncRecordType, ncRecordList } = res || {};
      setNcRecordType(ncRecordType);
      ncRecordDs.loadData(ncRecordList);
      scanFormDs.loadData([res]);
      ncRecordDs.current = ncRecordList[0];

    });
  };

  // 监听不良记录明细表格勾选数据
  const handleUpdate = ({ name }) => {
    scanFormDs.loadData([{ ...detailDs.current?.toJSONData() }]);
    if (name === 'materialObj') {
      ncRecordDs.loadData([]);
    }
    if (name === 'ncRecordType') {
      ncRecordDs.loadData([]);
      detailDs.current?.set('operationLov', undefined);
      detailDs.current?.set('operationId', undefined);
      detailDs.current?.set('operationName', undefined);
    }
    if (
      (['ncRecordType', 'siteLov', 'materialObj', 'operationLov'].includes(name) &&
        detailDs.current?.get('ncRecordType') === 'EO_ALL_NC') ||
      (['ncRecordType', 'siteLov', 'materialObj'].includes(name) &&
        detailDs.current?.get('ncRecordType') === 'RM_NC')
    ) {
      scanFormDs.current?.set('materialLots', undefined);
      scanFormDs.current?.set('materialLotLov', undefined);
      scanFormDs.current?.set('eos', undefined);
      scanFormDs.current?.set('eoLov', undefined);
    }
    if (name === 'siteLov') {
      detailDs.current?.init('materialObj', undefined);
      detailDs.current?.init('revisionCode', undefined);
      ncRecordDs.loadData([]);
    }
  };
  useDataSetEvent(detailDs, 'update', handleUpdate);

  // 头保存
  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    if (!validateFlag) {
      return false;
    }
    if(ncRecordDs.toData().length < 1){
      notification.warning({
        message: `当前不良事故下无不良信息，无法执行！`,
      });
      return;
    }
    const params = {
      ...detailDs.toData()[0],
      ncRecordList: ncRecordDs.toData(),
    };
    saveNcRecord({
      params,
      onSuccess: res => {
        notification.success({});
        setCanEdit(false);
        history.push(`/wms/bad-record/platform/detail/${res}`);
        handleQueryDetail(res);
      },
    });
  };
  const handleChangeNcRecordType = (value, oldVal) => {
    if (oldVal && ncRecordDs?.toData().length) {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {intl
              .get(`${modelPrompt}.info.clearData`)
              .d('不良记录信息/不良记录明细会清空，确定更换不良记录类型？')}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          setNcRecordType(value);
          ncRecordDs.loadData([]);
          scanFormDs.loadData([]);
        } else {
          setNcRecordType(oldVal);
          detailDs.current?.set('ncRecordType', oldVal);
        }
      });
    }
    setNcRecordType(value);
  };

  const renderTag = (value, record) => {
    switch (record.get('ncRecordStatus')) {
      case 'NEW':
        return <Tag color="green">{value}</Tag>;
      case 'RELEASED':
        return <Tag color="blue">{value}</Tag>;
      case 'COMPLETED':
        return <Tag color="red">{value}</Tag>;
      case 'CANCEL':
        return <Tag color="gray">{value}</Tag>;
      case 'WORKING':
        return <Tag color="volcano">{value}</Tag>;
      default:
        return null;
    }
  };

  // 库存品材料不良/库存品自制件不良
  const inventoryTableColumns: Array<any> = [
    {
      name: 'materialLotCode',
      width: 200,
      lock: 'left',
    },
    // ncRecordType === 'EO_ALL_NC' && {
    //   name: 'identification',
    //   width: 200,
    //   lock: 'left',
    // },
    {
      name: 'ncRecordStatusDesc',
      width: 150,
      renderer: ({ value, record }) => renderTag(value, record),
    },
    {
      name: 'qty',
    },
    {
      name: 'uomName',
    },
    {
      name: 'dispositionFunction',
      width: 200,
      // editor: () => {
      //   return canEdit && <Select />;
      // },
    },
    // {
    //   name: 'costCenterLov',
    //   width: 150,
    //   renderer: ({ record }) => record.get('costCenter'),
    //   editor: record => {
    //     return (
    //       detailDs.current?.get('ncIncidentStatus') === 'WORKING' &&
    //       record?.get('ncRecordStatus') === 'WORKING' && <Lov />
    //     );
    //   },
    // },
    // {
    //   name: 'workNumOrderLov',
    //   width: 150,
    //   editor: record => {
    //     return (
    //       detailDs.current?.get('ncIncidentStatus') === 'WORKING' && detailDs.current?.get('ncRecordType') === 'RM_NC' &&
    //       record?.get('ncRecordStatus') === 'WORKING' && <Lov />
    //     );
    //   },
    // },
    {
      name: 'scrapReason',
      width: 150,
      // editor: record => {
      //   return (
      //     detailDs.current?.get('ncIncidentStatus') === 'WORKING' && detailDs.current?.get('ncRecordType') === 'RM_NC' &&
      //     record?.get('ncRecordStatus') === 'WORKING' && <Select />
      //   );
      // },
    },
    // {
    //   name: 'workOrderNum',
    //   width: 150,
    // },
    // {
    //   name: 'workOrderQty',
    //   width: 150,
    // },
    // {
    //   name: 'eoNum',
    //   width: 180,
    // },
    {
      name: 'locatorName',
    },
    {
      name: 'containerCode',
      width: 180,
    },
    // {
    //   name: 'prodLineName',
    //   width: 150,
    // },
    // {
    //   name: 'routerName',
    //   width: 150,
    // },
    // {
    //   name: 'operationName',
    //   width: 150,
    // },
    // {
    //   name: 'equipmentCode',
    //   width: 150,
    // },
    // {
    //   name: 'workCellName',
    //   width: 150,
    // },
    // {
    //   name: 'intendedDisposalName',
    //   width: 150,
    // },
    // {
    //   name: 'intendedDisposalTime',
    //   width: 150,
    // },
    // {
    //   name: 'disposalWay',
    //   width: 150,
    // },

    {
      name: 'reviewResult',
      width: 150,
    },
    // {
    //   name: 'relatedUnitName',
    //   width: 150,
    // },
    {
      name: 'reviewName',
      width: 150,
    },
    {
      name: 'reviewTime',
      width: 150,
    },
    {
      name: 'reviewRemark',
      width: 150,
    },
  ];

  const handleLineDelete = () => {
    const deleteArr=[] as any;
    ncRecordDs.selected.forEach(item => {
      if(item.get('ncRecordId')){
        deleteArr.push(item.get("ncRecordId"))
      }else{
        ncRecordDs.remove(item);
      }
    })
    if(deleteArr.length>0){
      lineDelete({
        params: deleteArr,
      }).then(res => {
        if (res && res.success) {
          ncRecordDs.remove(ncRecordDs.selected);
          // if (ncRecordDs.current) {
          // }
        }
      });
    }
  };
  // 批量选择物料批
  const handleChangeMaterialLot = value => {
    if (!value) {
      return;
    }
    // fetchMaterialLotRelatedInfo({
    //   params: scanFormDs?.current?.get('ncRecordIdMaterialLot'),
    // }).then(res => {
    //   if (res && res.success) {
    //     // 清空数据
    //     scanFormDs.current?.init('materialLotLov', undefined);
    //     scanFormDs.current?.init('ncRecordIdMaterialLot', undefined);
    const temp = ncRecordDs.toData();
    ncRecordDs.loadData(
      temp.concat(uniqueArray(value.map(ele=>({
        ...ele,
        ncRecordStatus:'NEW',
        ncRecordStatusDesc:'新建',
        // uomName:ele.uomCode
      })), ncRecordDs.toData(), 'materialLotCode')),
    );
    // }
    // });
  };

  // 去重
  const uniqueArray = (res, tableList, field) => {
    // tableList为原值 res为新增数据 field为去重字段
    const temp: any = [];
    res?.forEach(i => {
      if (tableList.every((j: any) => j[field] !== i[field])) {
        temp.push(i);
      }
    });
    return temp;
  };
  // 取消-状态变更
  const clickMenu = async () => {
    cancel({
      params: [detailDs.current?.get('ncIncidentId')],
    }).then(res => {
      if (res && res.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
        history.push('/wms/bad-record/platform/list');
      }
    });
  };

  const cancelEdit = () => {
    if (id === 'create') {
      history.push('/wms/bad-record/platform/list');
    } else {
      setCanEdit(false);
      handleQueryDetail(id);
    }
  };
  // 批量输入物料批&eo弹框
  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    openBatchBarCodeModal({
      inputLovDS,
      inputLovFlag,
      inputLovTitle,
      inputLovVisible,
      targetDS: detailDs,
      submit: handleScan,
    });
    inputLovDS.queryDataSet?.current?.set('code', '');
    inputLovDS.data = [];
    inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
  };
  // 物料批&eo扫描
  const handleScan = inputLovFlag => {
    if (inputLovFlag === 'materialLots') {
      scanMaterialLot();
    } else if (inputLovFlag === 'eos') {
      scanEo();
    }
  };
  const scanMaterialLot = () => {
    scanMaterialLotRelatedInfo({
      params: {
        ncRecordType: detailDs.current?.get('ncRecordType'),
        materialId: detailDs.current?.get('materialId'),
        revisionCode: detailDs.current?.get('revisionCode'),
        materialLots: scanFormDs.current?.get('materialLots'),
        siteId: detailDs.current?.get('siteId'),
      },
    }).then(res => {
      if (res && res.success) {
        scanFormDs.current?.init('materialLots', null);
        const temp = ncRecordDs.toData();
        ncRecordDs.loadData(
          temp.concat(uniqueArray(res.rows.map(ele=>({
            ...ele,
            ncRecordStatus:'NEW',
            ncRecordStatusDesc:'新建',
            // uomName:ele.uomCode
          })), ncRecordDs.toData(), 'materialLotCode')),
        );

      }
    });
  };
  const scanEo = () => {
    scanEoRelatedInfo({
      params: {
        identifications: scanFormDs.current?.get('eos'),
        ncRecordType: detailDs.current?.get('ncRecordType'),
        materialId: detailDs.current?.get('materialId'),
        revisionCode: detailDs.current?.get('revisionCode'),
        siteId: detailDs.current?.get('siteId'),
        operationId: detailDs.current?.get('operationId'),
      },
      onSuccess: res => {
        if (res && res.success) {
          scanFormDs.current?.init('eos', null);
          const temp = ncRecordDs.toData();
          ncRecordDs.loadData(
            temp.concat(uniqueArray(res.rows, ncRecordDs.toData(), 'identification')),
          );

        }
      },
    });
  };
  // const handleBatchInputCostCenter = () => {
  //   modalDs.current?.getField('intendedDisposal')?.set('required', false);
  //   modalDs.current?.getField('costCenterLov')?.set('required', true);
  //   Modal.open({
  //     ...drawerPropsC7n({ modalDs }),
  //     drawer: false,
  //     title: intl.get(`${modelPrompt}.title.batchInputCostCenter`).d('批量录入成本中心'),
  //     style: {
  //       width: 500,
  //     },
  //     children: (
  //       <Form labelWidth={112} dataSet={modalDs} columns={1}>
  //         <Lov name="costCenterLov" />
  //       </Form>
  //     ),
  //     afterClose: () => {
  //       modalDs.loadData([]);
  //     },
  //     onOk: async () => handleDrawerConfirm('costCenterLov'),
  //   });
  // };

  const handleMaterialClick = async val => {
    setRevisionList([]);
    detailDs.current?.init('revisionCode', null);
    if (val) {
      queryRevision({
        params: {
          tenantId: getCurrentOrganizationId(),
          siteIds: detailDs.current?.get('siteId'),
          materialId: detailDs.current?.get('materialId'),
        },
      }).then(res => {
        if (res) {
          detailDs.getField('revisionCode')?.set('required', res?.rows.length > 0);
          setRevisionList(res?.rows);
        }
      });
    }
  };
  // 发起评审
  const handleInitiateReview = async () => {
    if (await detailDs.validate()) {
      // 校验欲处置结果
      if (ncRecordDs.toData().length < 1) {
        notification.warning({
          message: `当前不良事故下无不良信息，无法执行！`,
        });
        return;
      }
      // const validateFlag = ncRecordDs.toData().every((item: any) => item?.intendedDisposal);
      initiateReview({
        params: {
          ...detailDs.toData()[0],
          // ncRecordList: ncRecordDs.selected?.map(item => item.data),
          ncRecordList: ncRecordDs.toData(),
        },
      }).then(res => {
        if (res && res.success) {
          history.push('/wms/bad-record/platform/list');
        }
      });
    }
  };
  // 快速报废

  // 报废确认
  const handleScrappingConfirm = async () => {
    if (await detailDs.validate()) {
      // 校验不良事故关联的所有WORKING不良记录是否均有“成本中心”

      // if (ncRecordDs.toData().length < 1) {
      //   return notification.warning({
      //     message: `当前不良事故下无不良信息，无法执行！`,
      //   });
      // }
      // if (ncRecordDs.selected.length < 1) {
      //   return notification.warning({
      //     message: `请至少选中一行运行状态的行执行！`,
      //   });
      // }
      // const selectList: any = ncRecordDs.selected?.map(item => item.data);
      // const filterList = selectList.filter(item => item.ncRecordStatus !== 'WORKING');
      // if (filterList.length > 0) {
      //   return notification.warning({
      //     message: `请选中全部为运行状态的行执行！`,
      //   });
      // }
      // const filterData = ncRecordDs
      //   .toData()
      //   .some((item: any) => (!item?.workOrderId || item?.scrapReason) && item?.ncRecordStatus === 'WORKING');
      // const validateFlag = ncRecordDs
      //   .toData()
      //   .every((item: any) => item?.costCenter && item?.ncRecordStatus === 'WORKING');
      if (ncRecordDs.selected.some((item: any) => !item.get('workOrderId') || !item?.get('scrapReason'))) {
        return notification.warning({
          message: '部分不良记录内未填入工单和报废原因，请检查！',
        });
        // return;
      }
      // const finalData = selectList.map(item => {
      //   return {
      //     ...item,
      //     costCenter: item.costCenterLov.costcenterCode,
      //   };
      // });
      scrappingConfirm({
        params: {
          ...detailDs.toData()[0],
          ncRecordList: ncRecordDs.selected?.map(item => item.toJSONData()),
        },
      }).then(res => {
        if (res && res.success) {
          history.push('/wms/bad-record/platform/list');
        }
      });
    }
  };

  return (
    <div className="hmes-style" style={{ height: '95%', overflow: 'auto' }}>
      <TarzanSpin
        dataSet={detailDs}
        spinning={
          saveLoading ||
          fetchMaterialLotLoading ||
          fetchEoLoading ||
          lineDeleteLoading ||
          queryRevisionLoading ||
          scanMaterialLotLoading ||
          scanEoLoading ||
          fetchOperationLoading ||
          initiateReviewLoading ||
          quickScrappingLoading ||
          scrappingConfirmLoading ||
          cancelLoading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('不良记录平台')}
          backPath="/wms/bad-record/platform/list"
        >
          {detailDs.current?.get('ncIncidentStatus') === 'NEW' && (
            <>
              {canEdit ? (
                <>
                  <Button
                    color={ButtonColor.primary}
                    icon="save"
                    loading={saveLoading}
                    onClick={() => handleSave()}
                  >
                    {intl.get('tarzan.common.button.save').d('保存')}
                  </Button>
                  <Button color={ButtonColor.default} onClick={cancelEdit}>
                    {intl.get('tarzan.common.button.cancel').d('取消')}
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    color={ButtonColor.primary}
                    onClick={() => setCanEdit(true)}
                    loading={saveLoading}
                    icon="edit-o"
                  >
                    {intl.get('tarzan.common.button.edit').d('编辑')}
                  </Button>
                  {/* 取消状态 */}
                  <Button
                    disabled={detailDs.current?.get('ncIncidentStatus') !== 'NEW'}
                    onClick={clickMenu}
                    icon="cached"
                    color={ButtonColor.primary}
                  >
                    {intl.get(`tarzan.common.button.cancel`).d('取消')}
                  </Button>
                </>
              )}
              <Button
                color={ButtonColor.primary}
                onClick={handleInitiateReview}
                disabled={detailDs.current?.get('ncIncidentStatus') !== 'NEW'}
              >
                {intl.get(`${modelPrompt}.button.initiateReview`).d('发起评审')}
              </Button>
              {/* <Button
                color={ButtonColor.primary}
                disabled={detailDs.current?.get('ncIncidentStatus') !== 'NEW'}
                onClick={handleQuickScrapping}
              >
                {intl.get(`${modelPrompt}.button.quickScrapping`).d('快速报废')}
              </Button> */}
            </>
          )}

          {/* {detailDs.current?.get('ncIncidentStatus') === 'WORKING' && detailDs.current?.get('ncRecordType') === 'RM_NC' && (
            <Button disabled={ncRecordDs.selected.length === 0
              || ncRecordDs.selected.some(item => item.get('ncRecordStatus') !== 'WORKING')} color={ButtonColor.primary} onClick={handleScrappingConfirm}>
              {intl.get(`${modelPrompt}.button.scrappingConfirm`).d('报废确认')}
            </Button>
          )} */}

          {/* <AttributeDrawer
            serverCode={BASIC.HMES_BASIC}
            className="org.tarzan.mes.domain.entity.MtNcRecord"
            kid={id}
            canEdit={canEdit}
            disabled={id === 'create'}
            custBtnCode={`${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.ATTR`}
            custConfig={custConfig}
          /> */}
        </Header>
        <Content>
          <Collapse bordered={false} activeKey={activeKey} onChange={value => setActiveKey(value)}>
            <Panel
              header={intl.get(`${modelPrompt}.title.badRecordFormInfo`).d('不良单据信息')}
              key="badRecordFormInfo"
            >
              {/* {customizeForm(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.HEAD`,
                },

              )} */}
              <Form columns={3} labelWidth={112} dataSet={detailDs} disabled={!canEdit}>
                <TextField name="ncIncidentNum" />
                <Select
                  name="ncRecordType"
                  disabled={id !== 'create'}
                  onChange={handleChangeNcRecordType}
                />
                <Select name="ncIncidentStatus" disabled />
                <Lov name="siteLov" disabled={id !== 'create'} />
                <C7nFormItemSort
                  disabled={id !== 'create'}
                  name="materialObj"
                  itemWidth={['70%', '30%']}
                >
                  <Lov
                    name="materialObj"
                    onChange={handleMaterialClick}
                    disabled={id !== 'create'}
                  />
                  <Select
                    name="revisionCode"
                    required={revisionList.length > 0}
                    disabled={id !== 'create'}
                  >
                    {revisionList.map(item => {
                      return <Option value={item}>{item}</Option>;
                    })}
                  </Select>
                </C7nFormItemSort>
                {ncRecordType === 'EO_ALL_NC' && <Lov name="operationLov" />}
                {/* <Select name="stage" /> */}
                {/* <Select name="quantityCharacter" /> */}
                <TextArea name="remark" newLine colSpan={3} />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.title.badRecordLineInfo`).d('不良记录信息')}
              key="badRecordLineInfo"
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ width: '60%' }}>
                  {detailDs.current?.get('ncIncidentStatus') === 'NEW' && (
                    <Form
                      columns={2}
                      labelWidth={112}
                      dataSet={scanFormDs}
                      disabled={!canEdit}
                    >


                      <TextField
                        name="materialLots"
                        readOnly
                        clearButton={false}
                        placeholder="请扫描物料批"
                        suffix={
                          <img
                            alt=""
                            style={{ width: '20px', paddingRight: '5px' }}
                            src={scanImg}
                            onClick={() => onOpenInputModal(true, 'materialLots', '物料批编码')}
                          />
                        }
                      />
                      <Lov name="materialLotLov" onChange={handleChangeMaterialLot} />

                      {/* {ncRecordType === 'EO_ALL_NC' && (
                        <>
                          <TextField
                            name="eos"
                            readOnly
                            clearButton={false}
                            placeholder="请扫描执行作业"
                            suffix={
                              <img
                                alt=""
                                style={{ width: '20px', paddingRight: '5px' }}
                                src={scanImg}
                                onClick={() => onOpenInputModal(true, 'eos', '执行作业编码')}
                              />
                            }
                          />
                          <Lov name="eoLov" onChange={handleChangeEo} />
                        </>
                      )} */}
                    </Form>
                  )}
                </div>
                <div>
                  {/* {detailDs.current?.get('ncIncidentStatus') === 'WORKING' && (
                    <Button
                      funcType={FuncType.flat}
                      onClick={handleBatchInputCostCenter}
                      disabled={
                        ncRecordDs.selected.length === 0 ||
                        ncRecordDs.selected.some(item => item.get('ncRecordStatus') !== 'WORKING')
                      }
                    >
                      {intl.get(`${modelPrompt}.title.batchInputCostCenter`).d('批量录入成本中心')}
                    </Button>
                  )} */}
                  {/* {detailDs.current?.get('ncIncidentStatus') === 'NEW' && ( */}
                  <>
                    {/* <Button
                        funcType={FuncType.flat}
                        onClick={handleBatchPreDisposal}
                        disabled={!canEdit || !ncRecordDs.selected.length}
                      >
                        {intl.get(`${modelPrompt}.title.batchPreDisposal`).d('批量欲处置')}
                      </Button> */}
                    <Popconfirm
                      title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
                      onConfirm={() => handleLineDelete()}
                      okText={intl.get('tarzan.common.button.confirm').d('确认')}
                      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
                    >
                      <Button
                        icon="delete"
                        funcType={FuncType.flat}
                        disabled={!canEdit || !ncRecordDs.selected.length}
                      >
                        {intl.get(`tarzan.common.button.delete`).d('删除')}
                      </Button>
                    </Popconfirm>
                  </>

                </div>
              </div>

              {!!ncRecordType && (
                <Table
                  dataSet={ncRecordDs}
                  columns={inventoryTableColumns}
                  highLightRow
                />
              )}
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.HEAD`,
      // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.DETAIL`,
      // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.ATTR`,
      // `${BASIC.CUSZ_CODE_BEFORE}.NC_RECORD_DETAIL.LINE.ATTR`,
    ],
  })(BadRecordDetail as any),
);
