import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { getResponse } from '@utils/utils';
import notification from 'utils/notification';

const modelPrompt = 'tarzan.event.transaction.type.rel';

const tenantId = getCurrentOrganizationId();

const tableDS = () => ({
  primaryKey: 'eventTransTypeRelId',
  queryUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-event-trans-type-rel/page/ui`,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  autoCreate: true,
  autoQuery: true,
  autoQueryAfterSubmit: false,
  queryFields: [
    {
      name: 'eventTypeCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.EVENT_TYPE`,
      lovPara: {
        tenantId,
      },
      textField: 'eventTypeCode',
      valueField: 'eventTypeId',
    },
    {
      name: 'eventTypeId',
      bind: 'eventTypeCode.eventTypeId',
    },
    {
      name: 'businessTypeCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.businessTypeCode`).d('业务类型'),
      lovCode: 'MT.COMMON.BUSINESS_TYPE',
      lovPara: {
        tenantId,
      },
      textField: 'typeCode',
      valueField: 'genTypeId',
    },
    {
      name: 'genTypeId',
      bind: 'businessTypeCode.genTypeId',
    },
    {
      name: 'transTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transTypeCode`).d('ERP系统事务类型'),
    },
    {
      name: 'erpSystemType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.erpSystemType`).d('ERP系统名称'),
      lookupCode: 'MT.EVENT_TRANS_ERP_NAME',
      lovPara: { tenantId },
      textField: 'value',
      valueField: 'value',
    },
    {
      name: 'transCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transCode`).d('SAP事务代码'),
      lookupCode: 'MT.EVENT_TRANS_ERP_TRANS_CODE',
      lovPara: { tenantId },
      textField: 'value',
      valueField: 'value',
    },
    {
      name: 'initialFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialFlag`).d('初始化标识'),
      textField: 'meaning',
      valueField: 'value',
      lookupCode: 'MT.YES_NO',
      lovPara: { tenantId },
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  fields: [
    {
      name: 'eventTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型'),
    },
    {
      name: 'description',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.description`).d('事件类型描述'),
    },
    {
      name: 'businessTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessTypeCode`).d('业务类型'),
    },
    {
      name: 'businessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessTypeDesc`).d('业务类型描述'),
    },
    {
      name: 'transCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transCategory`).d('事务类别'),
    },
    {
      name: 'erpSystemType',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.erpSystemType`).d('ERP系统名称'),
    },
    {
      name: 'transTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transTypeCode`).d('ERP系统事务类型'),
    },
    {
      name: 'transCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transCode`).d('SAP事务代码'),
    },
    {
      name: 'initialFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.initialFlag`).d('初始化标识'),
    },
    {
      name: 'transferMethod',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.transferMethod`).d('事务传输方式'),
      required: true,
      lovPara: { tenantId },
      lookupCode: 'MT.MES.TRANSFER_METHOD',
    },
  ],
  transport: {
    read: ({ data }) => {
      const { businessTypeCode } = data;
      return {
        method: 'POST',
        data: {
          ...data,
          businessTypeCode: businessTypeCode ? businessTypeCode.typeCode : null,
        },
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-event-trans-type-rel/page/ui`,
        transformResponse: val => {
          const datas = JSON.parse(val);
          if (datas && !datas.success && datas.message) {
            notification.error({ message: datas.message });
          }
          return {
            ...datas,
          };
        },
      };
    },
    submit: config => {
      return {
        ...config,
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-event-trans-type-rel/update/ui`,
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return getResponse(parsedData);
          }
        },
      };
    },
  },
  events: {},
});

export { tableDS };
