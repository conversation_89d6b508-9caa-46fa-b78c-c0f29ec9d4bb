/**
 * 杂项工作台-入口文件
 * @date 2023-1-3
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState, useMemo } from 'react';
import { DataSet, Table, Dropdown, Modal, Button } from 'choerodon-ui/pro';
import { Badge, Menu, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import { flow, isNil } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import request from 'utils/request';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import FRPrintButton from '@components/tarzan-ui/FRPrintButton';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC, API_HOST } from '@utils/config';
import notification from 'utils/notification';
import ExcelExport from 'components/ExcelExport';
import { headerTableDS, lineTableDS } from './stores/ListDS';
import { drawerTableDS } from './stores/DrawerDs';
import styles from './index.module.less';
import MaterialLotDrawer from './MaterialLotDrawer';
import AvgDrawer from './AgvDrawer';
import AppointMaterialLotPage from './AppointMaterialLot';
import queryString from 'querystring';
import { openTab } from 'utils/menuTab';
import axios from 'axios'

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.receive.miscellaneous';
let modalAssembly;
let avgModal;

// const lugeUrl = '-24175';
const lugeUrl = '';

const Order = props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
  } = props;
  // 判断头搜索条件切换
  const [siteId, setSiteId] = useState();
  const [typeList, setTypeList] = useState([]);
  const [selectedStatus, setSelectedStatus] = useState(undefined);
  const [printIds, setPrintIds] = useState([]); // 头表格选择的id
  const drawerTableDs = useMemo(() => new DataSet(drawerTableDS()), []);
  const { run: materialPickingOut } = useRequest({
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/receive-return-bill/materialPickingOut`,
    method: 'POST',
  }, { manual: true, needPromise: true });


  // 头选中行instructionDocType
  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 返回页面时恢复选中项和当前项状态
  useEffect(() => {
    getType()
    if (props?.history?.action === 'PUSH') {
      headerTableDs.query(props.headerTableDs.currentPage);
      handleLineTableChange({
        dataSet: headerTableDs,
      });
    }
  }, []);

  const getType = async () => {
    const url = `/hpfm/v1/${tenantId}/lovs/data?lovCode=MT.MISCELLANEOUS`
    const res = await axios.get(url)
    console.log(res)
    if (res && res.length > 0) {
      const arr = res.filter(item => item.tag === 'RETURN').map(item => item.value)
      console.log(arr)
      setTypeList(arr)
    }
  }

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag
        ? headerTableDs.queryDataSet.addEventListener
        : headerTableDs.queryDataSet.removeEventListener;
      handler.call(headerTableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
      handler.call(headerTableDs, 'batchSelect', handleLineTableChange);
      handler.call(headerTableDs, 'batchUnSelect', handleLineTableChange);
    }
  };

  // 头搜索条件切换清空供应商地点
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (data.siteId !== siteId) {
      setSiteId(data.siteId);
    }
    if (!data.instructionDocTypeObj && !data.miscellaneousType) {
      record.set('instructionDocStatus', null);
    }
    if (!data.site) {
      record.set('warehouse', null);
    }
    if (!data.warehouse) {
      record.set('locator', null);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 列表刷新清除头单选状态
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable();
    }
  };

  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    const _selectedStatus = [];
    const _printIds = [];
    const completedList = ['1_PROCESSING', '1_COMPLETED', '2_PROCESSING', 'COMPLETED'];
    dataSet.selected.forEach(item => {
      const instructionDocStatus = item?.data?.instructionDocStatus;
      _printIds.push(item?.data?.instructionDocId);
      if (completedList.indexOf(instructionDocStatus) > -1) {
        if (_selectedStatus.indexOf('COMPLETED') === -1) {
          _selectedStatus.push('COMPLETED');
        }
      } else if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
        _selectedStatus.push(instructionDocStatus);
      }
    });
    setSelectedStatus(_selectedStatus.length === 1 ? _selectedStatus[0] : undefined);
    setPrintIds(_printIds);
  };

  // 行列表数据查询
  const queryLineTable = data => {
    lineTableDs.setQueryParameter('instructionDocId', data?.instructionDocId);
    lineTableDs.setQueryParameter('instructionDocType', data?.instructionDocType);
    lineTableDs.setQueryParameter('workOrderId', data?.workOrderId);
    lineTableDs.setQueryParameter('prodLineId', data?.prodLineId);
    lineTableDs.setQueryParameter('materialId', data?.materialId);
    lineTableDs.setQueryParameter('revisionCode', data?.revisionCode);
    lineTableDs.query();
  };

  // 打开指定物料批页面
  const handleAppointMaterialLot = async record => {
    const lineIndex = record.index;
    lineTableDs.setQueryParameter('instructionDocId', headerTableDs?.current?.toData()?.instructionDocId);
    lineTableDs.setQueryParameter('instructionDocType', headerTableDs?.current?.toData()?.instructionDocType);
    lineTableDs.setQueryParameter('workOrderId', headerTableDs?.current?.toData()?.workOrderId);
    lineTableDs.setQueryParameter('prodLineId', headerTableDs?.current?.toData()?.prodLineId);
    lineTableDs.setQueryParameter('materialId', headerTableDs?.current?.toData()?.materialId);
    lineTableDs.setQueryParameter('revisionCode', headerTableDs?.current?.toData()?.revisionCode);
    let data = []
    lineTableDs.query().then(res => {
      data = res.rows.content
      lineTableDs.loadData(data);
      if (!(!['CANCEL', 'CLOSED', 'COMPLETED'].includes(data[lineIndex].instructionStatus) && ["MATERIAL_LOT", ''].includes(data[lineIndex].identifyType))) {
        return false;
      }
      const recordData = record.toData();
      console.log(typeList, headerTableDs?.current?.toData().instructionDocType)
      const appointProps = {
        instructionDocId: headerTableDs?.current?.toData().instructionDocId,
        instructionDocType: headerTableDs?.current?.toData().instructionDocType,
        instructionId: recordData.instructionId,
        instructionDocLineId: recordData.instructionDocLineId,
        materialId: recordData.materialId,
        revisionCode: recordData.revisionCode,
        siteId: headerTableDs.current.toData().siteId,
        locatorId: recordData.locatorId || recordData.warehouseId,
        ownerType: recordData.fromOwnerType,
        ownerId: recordData.fromOwnerId,
        toleranceFlag: recordData.toleranceFlag,
        toleranceType: recordData.toleranceType,
        toleranceMaxValue: recordData.toleranceMaxValue,
        toleranceMinValue: recordData.toleranceMinValue,
        quantity: recordData.quantity,
        importFlag: typeList.includes(headerTableDs?.current?.toData().instructionDocType)
      }
      if (appointProps.instructionId) {
        Modal.open({
          title: intl.get(`${modelPrompt}.AppointMaterialLotPage`).d('指定物料批'),
          key: Modal.key(),
          className: 'hmes-style-modal',
          style: {
            width: 1000,
          },
          maskClosable: true,
          destroyOnClose: true,
          drawer: true,
          closable: true,
          okButton: false,
          cancelButton: false,
          children: (
            <AppointMaterialLotPage
              appointProps={appointProps}
            // customizeTable={customizeTable}
            />
          ),
        });
      }
    })
  };

  // 操作列渲染
  const optionRender = record => (
    <>
      <Button
        funcType="flat"
        onClick={() => {
          handleMaterialLotDetail(record);
        }}
      >
        {intl.get(`${modelPrompt}.detail`).d('明细')}
      </Button>
      <Button
        funcType="flat"
        disabled={!(!['CANCEL', 'CLOSED', 'COMPLETED', '1_COMPLETED'].includes(record?.toData().instructionStatus) && ["MATERIAL_LOT", ''].includes(record?.toData().identifyType))}
        onClick={() => handleAppointMaterialLot(record)}
      >
        {intl.get(`${modelPrompt}.create.materialLotAppoint`).d('指定')}
      </Button>
      <PermissionButton
        type="text"
        disabled={
          record.get('instructionStatus') !== 'RELEASED' || record.get('permissionFlag') !== 'Y'
        }
        onClick={() => handleCancel(record)}
        permissionList={[
          {
            code: `${path}.button.edit`,
            type: 'button',
            meaning: '列表页-编辑新建删除复制按钮',
          },
        ]}
      >
        {intl.get(`${modelPrompt}.line.button.cancel`).d('行取消')}
      </PermissionButton>
    </>
  );

  // 组件信息
  const handleMaterialLotDetail = async record => {
    const recordDetail = record?.toData() || {};
    modalAssembly = Modal.open({
      title:
        recordDetail.identifyType === 'MATERIAL_LOT'
          ? intl.get(`${modelPrompt}.materialLotDetail`).d('物料批明细')
          : intl.get(`${modelPrompt}.materialDetail`).d('物料明细'),
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <MaterialLotDrawer
          record={{
            identifyType: recordDetail.identifyType,
            instructionId: recordDetail.instructionId,
            instructionDocType: headerTableDs.current.get('instructionDocType'),
          }}
          customizeTable={customizeTable}
        />
      ),
      footer: (
        <Button onClick={() => modalAssembly.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 行取消
  const handleCancel = async record => {
    const _index = headerTableDs.currentIndex;
    return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/miscellaneous/line/cancel/ui`, {
      method: 'POST',
      body: {
        instructionId: record.get('instructionId'),
        businessType: 'CANCEL',
      },
    }).then(res => {
      if (res?.success) {
        headerTableDs.query(props.headerTableDs.currentPage).then(() => {
          headerTableDs.locate(_index);
        });
        lineTableDs.query(props.lineTableDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'instructionDocNum',
      width: 150,
      renderer: ({ record, value }) => {
        if (record.data.instructionDocStatus === 'RELEASED') {
          return (
            <a
              onClick={() => {
                props.history.push(
                  `/hwms/in-library/miscellaneous-new/detail/${record.data.instructionDocId}`,
                );
              }}
            >
              {value}
            </a>
          );
        }
        return value;
      },
      lock: 'left',
    },
    {
      name: 'instructionDocTypeDesc',
      width: 150,
    },
    {
      name: 'instructionDocStatusDesc',
      width: 80,
    },
    {
      name: 'siteCode',
      width: 120,
    },
    {
      name: 'accountTypeDesc',
      width: 80,
    },
    {
      name: 'costcenterCode',
      width: 150,
    },
    {
      name: 'costcenterDesc',
      width: 150,
    },
    {
      name: 'costcenterCategoryDesc',
      width: 100,
    },
    {
      name: 'internalOrderCode',
      width: 150,
    },
    {
      name: 'internalOrderDesc',
      width: 150,
    },
    {
      name: 'internalOrderCategoryDesc',
      width: 100,
    },
    {
      name: 'evaluationType',
    },
    {
      name: 'remark',
      width: 120,
    },
    {
      name: 'printTimes',
      width: 100,
    },
    {
      name: 'realName',
      width: 120,
    },
    {
      name: 'creationDate',
      align: 'center',
      width: 150,
    },
  ];

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'lineNumber',
      width: 60,
      lock: 'left',
    },
    {
      name: 'identifyType',
      width: 120,
      lock: 'left',
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        } if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'materialCode',
      width: 140,
      lock: 'left',
    },
    {
      name: 'revisionCode',
      width: 80,
      lock: 'left',
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'quantity',
      width: 100,
    },
    {
      name: 'executedQty',
      width: 80,
    },
    {
      name: 'minPackageQty',
      width: 100,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'instructionStatusDesc',
      width: 80,
    },
    {
      name: 'soNumber',
      width: 140,
    },
    {
      name: 'soLineNum',
      width: 100,
    },
    {
      name: 'warehouseCode',
      width: 140,
    },
    {
      name: 'locatorCode',
      width: 140,
    },
    {
      name: 'toWarehouseCode',
      width: 140,
    },
    {
      name: 'toLocatorCode',
      width: 140,
    },
    {
      name: 'toleranceFlag',
      width: 80,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceTypeDesc',
      width: 100,
    },
    {
      name: 'toleranceMaxValue',
      width: 80,
    },
    {
      name: 'toleranceMinValue',
      width: 80,
    },
    {
      name: 'remark',
      width: 140,
    },
    {
      name: 'option',
      fixed: 'right',
      lock: 'right',
      width: 180,
      align: 'center',
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      renderer: ({ record }) => optionRender(record),
    },
  ];

  const headerRowClick = record => {
    queryLineTable(record?.toData());
  };

  const clickMenu = async ({ key }) => {
    const instructionDocIds =
      headerTableDs?.selected?.map(item => {
        return item?.get('instructionDocId');
      }) || [];

    return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/miscellaneous/state-change/ui`, {
      method: 'POST',
      body: {
        instructionDocIds,
        instructionDocStatus: key,
      },
    }).then(res => {
      if (res?.success) {
        headerTableDs.batchUnSelect(headerTableDs.selected);
        headerTableDs.clearCachedSelected();
        setSelectedStatus(undefined);
        setPrintIds([]);
        headerTableDs.query(props.headerTableDs.currentPage);
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  };

  const createDelivery = () => {
    props.history.push(`/hwms/in-library/miscellaneous-new/detail/create`);
  };

  const handlePrint = () => {
    const instructionDocIds =
      headerTableDs?.selected?.map(item => {
        return item?.get('instructionDocId');
      }) || [];

    return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/wmsCostCenterPicking/instruction/doc/print`, {
      method: 'POST',
      body: instructionDocIds,
      responseType: 'blob',
    }).then(res => {
      if (res.type === 'application/json') {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: '打印成功',
          });
        } else {
          notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
        }
      }
    });
  };
  const getExportQueryParams = () => {
    if (!headerTableDs.queryDataSet || !headerTableDs.queryDataSet.current) {
      return {};
    }
    const instructionDocId = []
    headerTableDs?.selected.map((item) => {
      instructionDocId.push(item.data.instructionDocId)
    })
    const instructionDocIds = instructionDocId.join(',')
    const queryParmas = {
      ...headerTableDs.queryDataSet.current.toData(),
      instructionDocIds,
    }
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  };

  const submitData = () => {
    return new Promise(async (resolve) => {
      const validate = await drawerTableDs.validate();
      if (!validate) {
        return resolve(false);
      }
      const rows = drawerTableDs.selected.map(record => ({
        ...record.toJSONData(),
        autoQty: record?.get('autoAllQtySelf'),
      }));
      const res = await materialPickingOut({ params: rows });
      if (res?.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.successfulDelivery`).d('出库成功'),
        });
        avgModal.close();
        return resolve(true);
      }
      return resolve(false);
    })
  };

  const toAgv = () => {
    avgModal = Modal.open({
      ...drawerPropsC7n({
        // ds: containerLoadDetailDs,
        canEdit: true,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.avgDetail`).d('领料出库'),
      style: {
        width: 1080,
      },
      children: (
        <AvgDrawer
          customizeTable={customizeTable}
          selectDocIds={printIds}
          tableDs={drawerTableDs}
        />
      ),
      footer: () => (
        <Button
          color='primary'
          disabled={!printIds}
          onClick={submitData}
        >
          {intl.get(`${modelPrompt}.button.avgDetail`).d('出库')}
        </Button>
      ),
    });
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/COST_ENTER_WITHDRAWAL',
      title: '成本中心领退平台导出',
      search: queryString.stringify({
        title: '成本中心领退平台导出',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
      }),
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.parentTable`).d('成本中心领退平台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={createDelivery}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.button.createDelivery`).d('创建单据')}
        </PermissionButton>
        <Dropdown
          overlay={
            <Menu onClick={clickMenu} className={styles['split-menu']}>
              {selectedStatus === 'RELEASED' && (
                <Menu.Item key="CANCEL">
                  <a target="_blank" rel="noopener noreferrer">
                    {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
                  </a>
                </Menu.Item>
              )}
              {(selectedStatus === 'COMPLETED' ||
                selectedStatus === 'PROCESSING' ||
                selectedStatus === '1_COMPLETED' ||
                selectedStatus === '1_PROCESSING' ||
                selectedStatus === '2_PROCESSING')
                && (
                  <Menu.Item key="CLOSED">
                    <a target="_blank" rel="noopener noreferrer">
                      {intl.get(`${modelPrompt}.button.close`).d('关闭')}
                    </a>
                  </Menu.Item>
                )}
            </Menu>
          }
          trigger={['click']}
          disabled={
            [
              'RELEASED',
              'COMPLETED',
              'PROCESSING',
              '1_COMPLETED',
              '1_PROCESSING',
              '2_PROCESSING',
            ].indexOf(selectedStatus) === -1
          }
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={
              [
                'RELEASED',
                'COMPLETED',
                'PROCESSING',
                '1_COMPLETED',
                '1_PROCESSING',
                '2_PROCESSING',
              ].indexOf(selectedStatus) === -1
            }
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/miscellaneous/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        <Button
          color='primary'
          disabled={!printIds.length}
          onClick={toAgv}
        >
          {intl.get(`${modelPrompt}.makeAndLeaveWarehouse`).d('领料出库')}
        </Button>
        <Button
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="print"
          onClick={handlePrint}
        >
          {intl.get(`${modelPrompt}.button.print`).d('打印')}
        </Button>
        <Button
          type="c7n-pro"
          icon="file_upload"
          onClick={goImport}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </Button>
        {/* <FRPrintButton
          kid="MISCELLANEOUS_WORKBENCH"
          queryParams={printIds}
          disabled={!(printIds.length > 0)}
          printObjectType="INSTRUCTION_DOC"
        /> */}
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.HEAD`,
          },
          <Table
            searchCode="zxgzt1"
            customizedCode="zxgzt1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            queryFieldsLimit={10} // 头部显示的查询字段的数量
            dataSet={headerTableDs}
            columns={headerTableColumns}
            highLightRow
            onRow={({ record }) => {
              return {
                onClick: () => {
                  headerRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineTableDs}
          >
            {lineTableDs && (
              customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.LINE`,
                },
                <Table
                  customizedCode="zxgzt2"
                  className={styles['expand-table']}
                  dataSet={lineTableDs}
                  highLightRow={false}
                  columns={lineTableColumns}
                />,
              )
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(

  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_MATERIAL_LOT.QUERY`,
    ],
  }),
  formatterCollections({ code: ['tarzan.receive.miscellaneous', 'tarzan.common'] }),
)(Order);
