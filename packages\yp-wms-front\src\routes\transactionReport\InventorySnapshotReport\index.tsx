/*
 * @Author: cqy <EMAIL>
 * @Date: 2023-11-16 09:47:13
 * @LastEditors: cqy <EMAIL>
 * @LastEditTime: 2023-11-29 10:40:06
 * @FilePath: \yp-wms-front\packages\yp-wms-front\src\routes\transactionReport\InventorySnapshotReport\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import { isNil } from 'lodash';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@utils/config';
import { tableDS } from './stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.wms.event.inventorySnapshotReport';

const inventorySnapshotReport = props => {
  const { tableDs } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'warehouseCode',
        width: 150,
      },
      {
        name: 'reservoirAreaCode',
        width: 150,
      },
      {
        name: 'locatorCode',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'materialName',
        width: 150,
      },
      {
        name: 'supplierCode',
        width: 150,
      },
      {
        name: 'supplierName',
        width: 150,
      },
      {
        name: 'qualityStatusMeaning',
        width: 150,
      },
      {
        name: 'onhandQty',
        width: 150,
      },
      {
        name: 'uomCode',
        width: 150,
      },
      {
        name: 'creationDate',
        width: 150,
      },
    ];
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParams = tableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach((i) => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
        
    return {
      ...queryParams,
      mltIds: tableDs.selected.map((item: any) => {
        return item.data.mltId;
      }),
    };
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get('tarzan.mes.event.inventorySnapshotReport.title.New.list')
          .d('库存快照报表')}
      >
        <ExcelExport
          method="GET"
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-mlt-onhands/list/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="inventorySnapshotReport"
          customizedCode="inventorySnapshotReport"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.mes.event.inventorySnapshotReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(inventorySnapshotReport),
);
