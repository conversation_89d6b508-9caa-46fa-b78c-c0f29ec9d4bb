import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.ass.contractsMaterial';
const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'contractLineId',
    selection: false,
    paging: true,
    pageSize: 10,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'materialLov',
          type: FieldType.object,
          label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
          lovCode: 'WMS.CONTRACT_MATERIAL',
          ignore: FieldIgnore.always,
          multiple: true,
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'materialCodes',
          bind: 'materialLov.materialCode',
        },
        {
          name: 'materialName',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
        },
        {
          name: 'brand',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.brand`).d('品牌'),
        },
        {
          name: 'model',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.model`).d('型号'),
        },
        {
          name: 'supplierLov',
          ignore: FieldIgnore.always,
          type: FieldType.object,
          multiple: true,
          label: intl.get(`${modelPrompt}.form.supplierCode`).d('供应商编码'),
          lovCode: 'WMS.CONTRACT_SUPPLIER',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'supplierCodes',
          bind: 'supplierLov.supplierCode',
        },
        {
          name: 'contractLov',
          label: intl.get(`${modelPrompt}.form.contractLov`).d('合同号'),
          ignore: FieldIgnore.always,
          type: FieldType.object,
          lovCode: 'WMS.CONTRACT_NUMBER',
          multiple: true,
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'contractNumbers',
          bind: 'contractLov.contractNumber',
        },
        {
          name: 'priceDateFrom',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.priceDateFrom`).d('价格有效期从'),
        },
        {
          name: 'priceDateTo',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.priceDateTo`).d('价格有效期至'),
        },
      ],
    }),
    fields: [
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialCode`).d('资材物料编码'),
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
      },
      {
        name: 'brand',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.brand`).d('品牌'),
      },
      {
        name: 'model',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.model`).d('型号'),
      },
      {
        name: 'minPackQty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.minPackQty`).d('最小包装量'),
      },
      {
        name: 'minPoQty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.minPoQty`).d('最小采购量'),
      },
      {
        name: 'attributeVarchar15',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.attributeVarchar15`).d('交货期'),
      },
      {
        name: 'shelfDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.shelfDate`).d('质保期（月）'),
      },
      {
        name: 'supplierCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.supplierCode`).d('供应商编码'),
      },
      {
        name: 'supplierName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.supplierName`).d('供应商描述'),
      },
      {
        name: 'contractNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.contractNumber`).d('合同号'),
      },
      {
        name: 'lineNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.lineNum`).d('合同行号'),
      },
      {
        name: 'priceDateFrom',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.form.priceDateFrom`).d('价格有效期从'),
      },
      {
        name: 'priceDateTo',
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.form.priceDateTo`).d('价格有效期至'),
      },
      {
        name: 'qty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.qty`).d('库存现有量'),
      },
      {
        name: 'minStockQty',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.minStockQty`).d('安全库存量'),
      },
      {
        name: 'effectiveDays',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.effectiveDays`).d('剩余有效天数'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          data: {
            ...data,
            materialCodes: data.materialCodes.join(','),
            supplierCodes: data.supplierCodes.join(','),
            contractNumbers: data.contractNumbers.join(',')
          },
          method: 'GET',
          url: `${BASIC.TARZAN_REPORT}/v1/${getCurrentOrganizationId()}/wms-contracts/list/ui`,
        };
      },
    },
  });

export default listPageFactory;
