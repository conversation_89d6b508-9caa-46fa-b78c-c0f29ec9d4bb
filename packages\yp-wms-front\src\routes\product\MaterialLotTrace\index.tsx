/**
 * @Description: 物料批管理平台-列表页
 * @Author: <<EMAIL>>
 * @Date: 2022-1-25 14:38:56
 * @LastEditTime: 2022-11-21 13:42:12
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Table, Button, Modal, Select, Form, TextArea, Dropdown, Menu } from 'choerodon-ui/pro';
import myInstance from '@utils/myAxios';
import { Button as PermissionButton } from 'components/Permission';
import ExcelExport from 'components/ExcelExport';
import ExcelExportPro from 'components/ExcelExportPro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Badge } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
// import { FRPrintButton } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { API_HOST, BASIC } from '@utils/config';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { isNil } from 'lodash';
import request from 'utils/request';
import { getResponse, getCurrentSiteCode, getCurrentSiteId } from '@utils/utils';
import notification from 'utils/notification';
import { tableDS, historyDS, freezeDetailDS } from './stores/ListTable';
import HistoryDrawer from './HistoryDrawer';
import { FetchDynamicColumn } from './services';

const modelPrompt = 'tarzan.hmes.product.materialLotTrance';
const tenantId = getCurrentOrganizationId();

let TableHeight = 600;

const SubjectMaintainListWms = (props: any) => {
  const {
    tableDs,
    match: { path },
    history: { action }
  } = props;
  // 存储选中数据的key
  const [selectedKey, setSelectedKey] = useState([]);
  const [freezeYList, setFreezeYList] = useState([]);
  const [freezeNList, setFreezeNList] = useState([]);

  useEffect(() => {
    TableHeight = window.screen.height - 450;
  }, []);


  const historyDs = useMemo(() => new DataSet(historyDS()), []);

  const fetchDynamicColumn = useRequest(FetchDynamicColumn('mt_material_lot_attr'));

  const [selectedMaterialLotList, setSelectedMaterialLotList] = useState<any[]>([]);

  useEffect(() => {
    const _siteId = getCurrentSiteId();
    const _siteCode = getCurrentSiteCode();
    // 进入页面，进行数据查询时，有两种不同查询情况
    // 1.从新建/详情页返回到列表页
    // 2.从其他功能页面跳转到列表页
    if (Object.keys(props?.location?.query).length === 0 || props?.location?.state?._back) {
      // 1.  第一种情况，只需要使用缓存的ds查询数据来使用
      // 详情页点取消跳转回来，query为空对象，但返回图标跳转，会有state._back = -1
      if (action === "POP" || action === 'REPLACE') {
        tableDs.queryDataSet.loadData([{ siteId: _siteId || null, siteCode: _siteCode || null, enableFlag: 'Y' }]);
      }
      tableDs.query(props.tableDs.currentPage);
      return;
    }
    // 2。   第二种情况，需使用路由中的传参，来设置表格查询参数
    const {
      siteId,
      siteCode,
      materialId,
      materialCode,
      revisionCode,
      lotCode,
      qualityStatus,
      ownerType,
      ownerId,
      ownerCode,
      loactorsInfo,
      supplierId,
      supplierCode,
      supplierName,
      supplierLot,
      freezeFlag,
      enableFlag,
      // inLocatorTime,
      specifiedLevel,
      warehouseInFo,
      level,
    } = props?.location?.query || {};
    const queryParams = {
      enableFlag: enableFlag ? enableFlag : 'Y',
      siteLov: siteId ? { siteId, siteCode } : { siteId: _siteId || null, siteCode: _siteCode || null, enableFlag: 'Y' },
      materialLov: materialId ? { materialId, materialCode } : undefined,
      revisionCodes: revisionCode && revisionCode.length ? [revisionCode] : undefined,
      lotList: lotCode && lotCode.length ? [lotCode] : undefined,
      qualityStatus,
      ownerType,
      ownerLov: ownerId
        ? { soLineId: ownerId, customerId: ownerId, soNumContent: ownerCode }
        : undefined,
      locatorLov: loactorsInfo && !level ? JSON.parse(loactorsInfo) : null,
      wareHouseLov: level ? JSON.parse(warehouseInFo) : null,
      supplierLov: supplierId ? { supplierId, supplierCode, supplierName } : undefined,
      supplierLotList: supplierLot ? [supplierLot] : undefined,
      freezeFlag,
      // inLocatorTimeStart: moment(inLocatorTime).subtract(5, 'seconds').format ('yyyy-MM-DD hh:mm:ss'),
      // inLocatorTimeEnd: moment(inLocatorTime).add(5, 'seconds').format ('yyyy-MM-DD hh:mm:ss'),
      specifiedLevelLov: specifiedLevel ? { specifiedLevel } : undefined,
    };
    tableDs.queryDataSet.loadData([queryParams]);
    tableDs.queryDataSet.current.set(queryParams);
    setTimeout(() => {
      tableDs.query();
    }, 200);
  }, [props?.location?.query, props?.location?.state, action]);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
      tableDs.clearCachedRecords();
    };
  });

  // 处理选中条状态
  const handleDataSetSelectUpdateFun = () => {
    if (tableDs && tableDs.selected) {
      const selectList = tableDs.selected;
      if (selectList && selectList.length) {
        const arr = [];
        const freezeYArr = [];
        const freezeNArr = [];
        selectList.forEach((i: any) => {
          // @ts-ignore
          arr.push(i.data.materialLotId);
          if (i.data.freezeFlag === 'Y') {
            // @ts-ignore
            freezeYArr.push(i.data.freezeFlag)
          }
          if (i.data.freezeFlag === 'N') {
            // @ts-ignore
            freezeNArr.push(i.data.freezeFlag)
          }
        });
        setSelectedKey(arr);
        setFreezeYList(freezeYArr)
        setFreezeNList(freezeNArr)
      } else {
        setSelectedKey([]);
      }
    } else {
      setSelectedKey([]);
    }
  };

  // 解冻选中的数据
  const thawSelectData = () => {
    // @ts-ignore
    const freezeDetailDs = new DataSet(freezeDetailDS());
    Modal.open({
      destroyOnClose: true,
      closable: true,
      title: intl.get(`${prompt}.modal.selectReason`).d('选择原因'),
      children: <Form dataSet={freezeDetailDs} columns={1} labelWidth={112}>
        <Select name='reason' />
        <TextArea name="Remark" rowSpan={2} />
      </Form>,
      onOk: async () => {
        const validate = await freezeDetailDs.validate();
        if (validate) {
          const resultData = freezeDetailDs.current?.toData();
          const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/unfreeze/ui`;
          myInstance.post(url, {
            materialLotIds: [...selectedKey],
            reason: resultData.reason?.meaning,
            remark: resultData.Remark,
          }).then(res => {
            if (res.data.success) {
              notification.success({});
              setSelectedKey([]);
              setFreezeYList([]);
              setFreezeNList([]);
              // 解冻成功后重查列表
              tableDs.query();
            } else if (res.data.message) {
              notification.error({
                description: res.data.message,
              });
            }
          });
        } else {
          return false;
        }
      },
    });
  };
  // 冻结选中的数据
  const deleteSelectData = () => {
    // @ts-ignore
    const freezeDetailDs = new DataSet(freezeDetailDS(true));
    Modal.open({
      destroyOnClose: true,
      closable: true,
      title: intl.get(`${prompt}.modal.selectReason`).d('选择原因'),
      children: <Form dataSet={freezeDetailDs} columns={1} labelWidth={112}>
        <Select name='reason' />
        <TextArea name="Remark" rowSpan={2} />
      </Form>,
      onOk: async () => {
        const validate = await freezeDetailDs.validate();
        if (validate) {
          const resultData = freezeDetailDs.current?.toData();
          const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/freeze/ui`;
          myInstance.post(url, {
            materialLotIds: [...selectedKey],
            reason: resultData.reason.meaning,
            remark: resultData.Remark,
          }).then(res => {
            if (res.data.success) {
              notification.success({});
              setSelectedKey([]);
              setFreezeYList([]);
              setFreezeNList([]);
              // 冻结成功后重查列表
              tableDs.query();
            } else if (res.data.message) {
              notification.error({
                description: res.data.message,
              });
            }
          });
        } else {
          return false;
        }
      },
    });
  };

  const listener = flag => {
    // 列表交互监听
    if (tableDs) {
      const handerQuery = flag
        ? tableDs.queryDataSet.addEventListener
        : tableDs.queryDataSet.removeEventListener;
      const handler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // 查询条件更新时操作
      handerQuery.call(tableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
      handler.call(tableDs, 'load', handleDataSetSelectUpdate);
      // 头选中和撤销选中事件
      handler.call(tableDs, 'select', handleDataSetSelectUpdate);
      handler.call(tableDs, 'unSelect', handleDataSetSelectUpdate);
      handler.call(tableDs, 'selectAll', handleDataSetSelectUpdate);
      handler.call(tableDs, 'unSelectAll', handleDataSetSelectUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ name, record }) => {
    if (name === 'ownerType') {
      record.set('ownerLov', {});
    }
    if (name === 'reservedObjectType') {
      record.set('reservedObjectLov', {});
    }
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    const _materialLotList: string[] = [];
    tableDs.selected.forEach(item => {
      const { materialLotId } = item.toData();
      _materialLotList.push(materialLotId);
    });
    setSelectedMaterialLotList(_materialLotList);
    handleDataSetSelectUpdateFun();
  };

  const [dynamicColumns, setDynamicColumns] = useState<ColumnProps[]>([]);

  useEffect(() => {
    const dynamicColumn: ColumnProps[] = [];
    ((fetchDynamicColumn.data || {}).content || []).forEach(item => {
      if (item.enableFlag === 'Y') {
        dynamicColumn.push({
          header: item.attrMeaning,
          name: item.attrName,
          align: ColumnAlign.left,
          width: 150,
          renderer: ({ record }) => {
            return (record?.get('attrMap') || {})[item.attrName];
          },
        });
      }
    });
    setDynamicColumns(dynamicColumn);
  }, [fetchDynamicColumn.data]);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'identification',
        align: ColumnAlign.left,
        width: 250,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                props.history.push(
                  `/hwms/product/material-lot-traceability/detail/${record?.get('materialLotId')}`,
                );
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'materialLotCode',
        width: 250,
      },
      {
        name: 'materialCode',
        width: 250,
      },
      {
        name: 'revisionCode',
        width: 100,
      },
      {
        name: 'materialDesc',
        width: 250,
      },
      {
        name: 'primaryUomQty',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'primaryUomCode',
        width: 120,
      },
      {
        name: 'wareHouseCode',
        width: 250,
      },
      {
        name: 'deliverDocNum',
        width: 250,
      },
      {
        name: 'locatorCode',
        width: 250,
      },
      {
        name: 'lot',
        width: 100,
      },
      {
        name: 'specifiedLevel',
      },
      {
        name: 'productionDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'expirationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'stockAge',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'extendedShelfLifeTimes',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'supplierLot',
        width: 100,
      },
      {
        name: 'secondaryUomQty',
        align: ColumnAlign.right,
        width: 100,
      },
      {
        name: 'secondaryUomCode',
        width: 120,
      },
      {
        name: 'enableFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.enable`).d('启用')
                : intl.get(`tarzan.common.label.disable`).d('禁用')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'qualityStatusDesc',
        width: 120,
      },
      {
        name: 'createReasonDesc',
        width: 150,
      },
      {
        name: 'inLocatorTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'deliverName',
        width: 250,
      },
      {
        name: 'inSiteTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'siteCode',
        width: 250,
      },
      {
        name: 'eoNum',
        width: 150,
      },
      {
        name: 'ownerTypeDesc',
        width: 150,
        renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
      },
      {
        name: 'ownerCode',
        width: 250,
      },
      {
        name: 'ownerDesc',
        width: 250,
      },
      {
        name: 'supplierCode',
        width: 250,
      },
      {
        name: 'supplierDesc',
        width: 250,
      },
      {
        name: 'supplierSiteCode',
        width: 250,
      },
      {
        name: 'supplierSiteDesc',
        width: 250,
      },
      {
        name: 'customerCode',
        width: 250,
      },
      {
        name: 'customerDesc',
        width: 250,
      },
      {
        name: 'customerSiteCode',
        width: 250,
      },
      {
        name: 'customerSiteDesc',
        width: 250,
      },
      {
        name: 'reservedFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'returnFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'reservedObjectTypeDesc',
        width: 120,
      },
      {
        name: 'packingNumber',
        width: 250,
      },
      {
        name: 'reservedObjectCode',
        width: 250,
      },
      {
        name: 'assembleToolCode',
        width: 250,
      },
      {
        name: 'assemblePointCode',
        width: 250,
      },
      {
        name: 'unloadTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'loadTime',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'ovenNumber',
        width: 250,
      },
      {
        name: 'overOrderInterceptionFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'freezeFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'freezeReason',
      },
      {
        name: 'freezeRemark',
      },
      {
        name: 'unfreezeNumFlag',
      },
      {
        name: 'stocktakeFlag',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'materialLotStatus',
        width: 150,
      },
      {
        name: 'instructionDocNum',
        width: 150,
      },
      {
        name: 'instructionNum',
        width: 250,
      },
      {
        name: 'currentContainerCode',
        width: 250,
      },
      {
        name: 'topContainerCode',
        width: 250,
      },
      {
        name: 'printTimes',
        width: 250,
        renderer: ({ value }) => value || 0,
      },
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'createdUsername',
        width: 250,
      },
      {
        name: 'lastUpdateDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'lastUpdatedUsername',
        width: 250,
      },
    ];
  }, []);

  const handleQueryMaterialLotsHistory = () => {
    historyDs.setQueryParameter('selectedMaterialLotList', selectedMaterialLotList);
    historyDs.query();
    Modal.open({
      className: 'hmes-style-modal',
      closable: true,
      drawer: true,
      maskClosable: false,
      style: {
        width: 1080,
      },
      okText: intl.get('tarzan.common.button.confirm').d('确定'),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
      key: Modal.key(),
      title: (
        <div
          style={{
            width: 'calc(100% - 20px)',
            display: 'inline-flex',
            justifyContent: 'space-between',
            alignContent: 'center',
          }}
        >
          <div>{intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}</div>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-lot-trace/export/his/ui`}
            queryParams={{
              materialLotIds: selectedMaterialLotList,
            }}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
        </div>
      ),
      destroyOnClose: true,
      children: (
        <HistoryDrawer ds={historyDs} />
      ),
    });
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    queryParmas.wareHouseIds = (queryParmas.wareHouseLov ?? []).map(ele => ele.locatorId).join(',')
    queryParmas.locatorIds = (queryParmas.locatorLov ?? []).map(ele => ele.locatorId).join(',')
    queryParmas.materialLotIds = tableDs.selected.map(item => item.data.materialLotId).join(',')
    return queryParmas;
  };

  const handlePrint = async (selected, type) => {
    const result = await request(
      `${BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/wmsMaterialLot/print/pdf/${type}`,
      {
        method: 'POST',
        responseType: 'blob',
        body: selected,
      },
    );
    const res = getResponse(result);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader: any = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: intl.get(`${modelPrompt}.notification.print.success`).d('打印成功'),
          });
        } else {
          notification.error({ message: intl.get(`${modelPrompt}.notification.browser.config`).d('当前窗口已被浏览器拦截，请手动设置浏览器！') });
        }
      }
    }
  };

  const menu = (
    <Menu style={{ width: '100px' }}>
      <Menu.Item key="a4">
        <a target="_blank" rel="noopener noreferrer" onClick={() => handlePrint(selectedMaterialLotList, 'a4')}>
          {intl.get(`${modelPrompt}.button.print.a4`).d('A4打印')}
        </a>
      </Menu.Item>
      <Menu.Item key="barcode">
        <a target="_blank" rel="noopener noreferrer" onClick={() => handlePrint(selectedMaterialLotList, 'barcode')}>
          {intl.get(`${modelPrompt}.button.print.tag`).d('标签打印')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/WMS_MATERIAL_MANAGEMENT',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const onHandelPrint = async type => {
    const url = type === 'random' ? 'wmsMaterialLot/print/random/pdf' : 'wmsMaterialLot/print/circle/pdf'
    const result = await request(
      `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/${url}`,
      {
        method: 'POST',
        responseType: 'blob',
        body: selectedMaterialLotList,
      },
    );
    const res = getResponse(result);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader: any = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: intl.get(`${modelPrompt}.notification.print.success`).d('打印成功'),
          });
        } else {
          notification.error({ message: intl.get(`${modelPrompt}.notification.browser.config`).d('当前窗口已被浏览器拦截，请手动设置浏览器！') });
        }
      }
    }
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.materialLotMaintenance`).d('物料批查询')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => {
            props.history.push('/hwms/product/material-lot-traceability/detail/create');
          }}
          permissionList={[
            {
              code: `/hwms/product/material-lot-traceability-news/list.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Button disabled={!selectedMaterialLotList.length} onClick={handleQueryMaterialLotsHistory}>
          {intl.get(`${modelPrompt}.queryHistory`).d('历史查询')}
        </Button>
        <Button
          type="c7n-pro"
          icon="file_upload"
          onClick={goImport}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </Button>
        <ExcelExportPro
          method="POST"
          allBody
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/mt-material-lot-trace/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />

        <Dropdown overlay={menu} disabled={selectedMaterialLotList.length === 0}>
          <Button
            disabled={selectedMaterialLotList.length === 0}
          >
            {intl.get('tarzan.common.button.print').d('打印')}
          </Button>
        </Dropdown>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          // icon="add"
          disabled={!selectedKey.length || selectedKey.length !== freezeYList.length}
          permissionList={[
            {
              code: `hwms.product.material-lot-traceability-news.list.button.unfreeze`,
              type: 'button',
              meaning: '列表页-解冻按钮',
            },
          ]}
          onClick={thawSelectData}
        >
          {intl.get(`${modelPrompt}.button.unfreeze`).d('解冻')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          // icon="add"
          disabled={!selectedKey.length || selectedKey.length !== freezeNList.length}
          permissionList={[
            {
              code: `hwms.product.material-lot-traceability-news.list.button.freeze`,
              type: 'button',
              meaning: '列表页-冻结按钮',
            },
          ]}
          onClick={deleteSelectData}
        >
          {intl.get(`${modelPrompt}.button.freeze`).d('冻结')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          disabled={!selectedMaterialLotList.length}
          onClick={() => onHandelPrint('circle')}
          permissionList={[
            {
              code: `${path}.button.circle`,
              type: 'button',
              meaning: '列表页-安全库存/周转物料标签打印',
            },
          ]}
        >
          {intl.get('tarzan.common.button.circle').d('安全库存/周转物料标签打印')}
        </PermissionButton>
        <PermissionButton
          disabled={!selectedMaterialLotList.length}
          type="c7n-pro"
          color={ButtonColor.primary}
          onClick={() => onHandelPrint('random')}
          permissionList={[
            {
              code: `${path}.button.random`,
              type: 'button',
              meaning: '列表页-随机备件箱码打印',
            },
          ]}
        >
          {intl.get('tarzan.common.button.random').d('随机备件箱码打印')}
        </PermissionButton>
        {/* <FRPrintButton
          kid="MATERIAL_LOT"
          queryParams={selectedMaterialLotList}
          disabled={!selectedMaterialLotList.length}
          printObjectType="MATERIAL_LOT"
        /> */}
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryFieldsLimit={12}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
          }}
          searchCode="wlpglpt"
          customizedCode="wlpglpt"
          dataSet={tableDs}
          columns={columns.concat(dynamicColumns)}
          style={{ height: TableHeight }}
          headerRowHeight={30}
          rowHeight={28}
          footerRowHeight={20}
          virtual
          virtualCell
          pagination={{
            showPager: true, // 显示数字按钮
            pageSizeOptions: ['20', '50', '100', '200', '500', '1000', '5000'],
          }}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.product.materialLotTrance', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(SubjectMaintainListWms),
);
