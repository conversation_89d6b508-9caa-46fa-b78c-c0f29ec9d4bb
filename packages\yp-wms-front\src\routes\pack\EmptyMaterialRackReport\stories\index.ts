/**
 * @Description: 事务明细报表-主表格-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-25 14:54:49
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.pack.empty.material.rack.report';
const tenantId = getCurrentOrganizationId();

export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  selection: DataSetSelection.multiple,
  queryFields: [
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('PACK料号'),
      // lovCode: 'MT.METHOD.MATERIAL',
      lovCode: 'MT.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'containerTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerType`).d('PACK料架类型'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.CONTAINER_TYPE_CODE`,
      lovPara: {
        tenantId,
      },
      multiple: true,
      ignore: FieldIgnore.always,
      noCache: true,
    },
    {
      name: 'containerTypeCodes',
      bind: 'containerTypeLov.containerTypeCode',
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('单据状态'),
      lovPara: {
        tenantId,
      },
      textField: 'description',
      valueField: 'statusCode',
      defaultValue: 'RELEASED',
      multiple: true,
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS`,
      lookupAxiosConfig: () => {
        return {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        };
      },
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('需求货位'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        locatorCategory: 'INVENTORY',
      },
    },
    {
      name: 'toLocator',
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'updateDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.updateDateFrom`).d('最后更新时间从'),
      max: 'updateDateTo',
    },
    {
      name: 'updateDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.'updateDateTo`).d('最后更新时间至	'),
      min: 'updateDateFrom',
    },
  ],
  fields: [
    {
      name: 'number',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.number`).d('序号'),
    },
    {
      name: 'displayStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.displayStatus`).d('状态'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('PACK料号'),
    },
    {
      name: 'containerTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeCode`).d('PACK料架类型'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('需求货位'),
    },
    {
      name: 'toCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toCode`).d('需求地点'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('需求地点描述'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('需求数量'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-inout-confirm/detail`,
        method: 'GET',
      };
    },
  },
});
