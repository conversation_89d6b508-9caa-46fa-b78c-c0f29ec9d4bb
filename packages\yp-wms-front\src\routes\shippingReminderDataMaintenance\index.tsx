import React, { useMemo } from 'react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import notification from 'utils/notification';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { observer } from 'mobx-react';
import { tableDS } from './stores';

const modelPrompt = 'shippingReminderDataMaintenance';
const tenantId = getCurrentOrganizationId();

const shippingReminderDataMaintenance = observer((props) => {
  const { tableDs } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'siteLov',
        editor: record => record.getState('editing'),
      },
      {
        name: 'customerLov',
        editor: record => record.getState('editing'),
      },
      {
        name: 'stockWarningTime',
        editor: record => record.getState('editing'),
      },
      {
        name: 'loadingWarningTime',
        editor: record => record.getState('editing'),
      },
      {
        name: 'signInWarningTime',
        editor: record => record.getState('editing'),
      },
      {
        name: 'enableFlag',
        editor: record => record.getState('editing'),
      },
    ];
  }, []);

  const handleAdd = () => {
    const record = tableDs.create({}, 0)
    tableDs.select(0);
    record.setState('editing', true);
  };

  const handleEdit = () => {
    tableDs.selected.forEach(record => {
      record.setState('editing', true);
    })
  };

  const handleSave = () => {
    const validate = tableDs.selected.map(record => record.validate());

    Promise.all(validate).then(arr => {
      if (!arr.includes(false)) {
        const data = tableDs.selected.map(record => record.toData());
        request(
          `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-shipping-reminders/update`,
          {
            method: 'POST',
            body: data,
          },
        ).then(res => {
          if (res && res?.length) {
            notification.success({})
            tableDs.query()
          } else {
            notification.error({
              message: res?.message ? res?.message : intl.get(`${modelPrompt}.errorMessage`).d('保存失败，请检查！'),
            });
          }
        });
      }
    });
  };

  const handleCancel = () => {
    tableDs.selected.forEach(record => {
      if (record.status === 'add') {
        tableDs.unSelect(record);
        tableDs.remove(record);
        record.setState('editing', false)
      } else {
        record.reset();
        tableDs.unSelect(record);
        record.setState('editing', false);
      }
    })
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('发运提醒数据维护')}>
        <PermissionButton type="c7n-pro" color={ButtonColor.primary} icon="add" onClick={handleAdd}>
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <Button color={ButtonColor.primary} disabled={!tableDs.selected?.length} onClick={handleEdit}>{intl.get(`${modelPrompt}.editing`).d('编辑')}</Button>
        <Button color={ButtonColor.primary} disabled={!tableDs.selected?.length} onClick={handleSave}>{intl.get(`${modelPrompt}.save`).d('保存')}</Button>
        <Button color={ButtonColor.primary} disabled={!tableDs.selected?.length} onClick={handleCancel}>{intl.get(`${modelPrompt}.cancel`).d('取消')}</Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="shippingReminderDataMaintenance"
          customizedCode="shippingReminderDataMaintenance"
        />
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(shippingReminderDataMaintenance),
);
