/**
 * @feature 物料配送属性维护-详情页具体表单
 * @date 2021-3-8
 * <AUTHOR> <<EMAIL>>
 */
import React, { useMemo, useEffect, useImperativeHandle, forwardRef, useState } from 'react';
import { DataSet, TextField, Form, Switch, Lov, SelectBox, NumberField } from 'choerodon-ui/pro';
import { useDataSetEvent } from 'utils/hooks';
import { Collapse } from 'choerodon-ui';
import { isNil } from 'lodash';
import intl from 'utils/intl';
import myInstance from '@utils/myAxios';
import { getCurrentOrganizationId } from 'utils/utils';
import { LovSelect, NumberFieldSelect } from '@components/tarzan-ui';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';
import { ViewMode } from 'choerodon-ui/pro/lib/radio/enum';
import { fixTimeDS, inTimeDS } from '../stories/PatternDs'; // 配送模式选择
import { timingDS, sequenceDS, orderDS, rationDS } from '../stories/PatternDetailDs'; // 配送模式明细
import styles from '../index.module.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.product.materialDistributionNew.model.materialDistributionNew';

const Detail = (props, ref) => {
  const { canEdit, id, columns = 1, type, copySourceDs, baseInfoDs, customizeForm } = props;

  const fixTimeDs = useMemo(() => new DataSet(fixTimeDS()), []) as any; // 准时配送select

  const inTimeDs = useMemo(() => new DataSet(inTimeDS()), []) as any; // 及时配送

  const timingDs = useMemo(() => new DataSet(timingDS()), []); // 配送模式明细-定时补货

  const sequenceDs = useMemo(() => new DataSet(sequenceDS()), []); // 配送模式明细-顺序补货

  const orderDs = useMemo(() => new DataSet(orderDS()), []); // 配送模式明细-订单补货

  const rationDs = useMemo(() => new DataSet(rationDS()), []) as any; // 配送模式明细-定量补货

  const [currentTab, setCurrentTab] = useState(''); // 当前tab
  const [currentType, setCurrentType] = useState('material'); // 当前类型
  const [selectOrganizationType, setSelectOrganizationType] = useState(''); // organizationType

  useDataSetEvent(baseInfoDs, 'update', ({ name, value, record }) => {
    if (name === 'materialSelect') {
      setCurrentType(value)
    }
    if (record.get('multiplesOfPackFlag') === 'Y' && !isNil(record.get('singleQty')) && !isNil(record.get('packQty'))) {
      record.set('distributionQty', record.get('singleQty') * record.get('packQty'))
    }
  });

  const custCode = useMemo(() => {
    switch (currentType) {
      case 'material':
        return `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DISTRIBUTION_DETAIL.MATERIAL_BASIC`;
      case 'materialCategory':
        return `${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DISTRIBUTION_DETAIL.CATEGORY_BASIC`;
      default:
        return ''
    }
  }, [currentType])

  useEffect(() => {
    if (id !== 'create') {
      detailQuery(id, type);
    }
  }, [id, type]);

  const detailQuery = (paramsId, paramsType) => {
    baseInfoDs.queryParameter = {
      pfepDistributionId: paramsId,
      pfepDistributionType: paramsType,
    };
    baseInfoDs.query().then(res => {
      if (res.success) {
        const data = res.rows;
        const {
          distributionMode,
          organizationType,
          organizationId,
          organizationCode,
          targetLocatorId,
          targetLocatorCode,
          maxInventory,
          minInventory,
          distributionCycle,
          materialConsumeRate,
          materialConsumeRateUomId,
          bufferInventory,
          fromScheduleRateFlag,
          bufferPeriod,
          calendar,
          calendarId,
          calendarCode,
          instructionCreatedByEoFlag,
        } = data;
        setCurrentTab(distributionMode);
        // 转换物料类别type描述
        const materialSelectData = paramsType === 'category' ? 'materialCategory' : 'material';
        setCurrentType(paramsType === 'category' ? 'materialCategory' : 'material');
        baseInfoDs.loadData([
          {
            ...data,
            distributionMode: null,
            organizationId: null,
            organizationCode: null,
            maxInventory: null,
            minInventory: null,
            distributionCycle: null,
            materialConsumeRate: null,
            materialConsumeRateUomId: null,
            bufferInventory: null,
            bufferPeriod: null,
            calendar: null,
            calendarId: null,
            calendarCode: null,
            instructionCreatedByEoFlag: null,
          },
        ]);
        // 注入进baseInfoDs
        baseInfoDs.current.init('materialSelect', materialSelectData);
        // 工作单元与库位的回填值
        setSelectOrganizationType(organizationType);
        if (organizationType === 'WORKCELL') {
          fixTimeDs.loadData([{ distributionMode }]);
          baseInfoDs.current.set('pfepDisOrganization', {
            workcellId: organizationId,
            workcellCode: organizationCode,
          });
          baseInfoDs.current.set('targetLocatorCode', {
            locatorId: targetLocatorId,
            locatorCode: targetLocatorCode,
          });
        } else if (data.organizationType === 'LOCATOR') {
          inTimeDs.loadData([{ distributionMode }]);
          baseInfoDs.current.set('pfepDisOrganization', {
            locatorId: organizationId,
            locatorCode: organizationCode,
          });
        }
        // 控制配送路线传参
        if (distributionMode === 'QUANTITATIVE') {
          baseInfoDs.current.set('DISTRIBUTION_CATEGORY', 'TIMELY_DISTRIBUTION');
        } else {
          baseInfoDs.current.set('DISTRIBUTION_CATEGORY', 'TIMING_DISTRIBUTION');
        }
        // 渲染具体tab数据
        switch (distributionMode) {
          case 'QUANTITATIVE':
            // 定量补货
            rationDs.loadData([{ maxInventory, minInventory, bufferInventory }]);
            timingDs.loadData([]);
            timingDs.reset();
            break;
          case 'BY_WO':
            // 订单补货
            orderDs.loadData([
              {
                distributionCycle,
                materialConsumeRate,
                materialConsumeRateUomId,
                bufferInventory,
                fromScheduleRateFlag,
              },
            ]);
            sequenceDs.loadData([]);
            sequenceDs.reset();
            break;
          case 'IN_TURN':
            // 顺序补货
            sequenceDs.loadData([
              {
                distributionCycle,
                materialConsumeRate,
                materialConsumeRateUomId,
                bufferPeriod,
                calendar,
                calendarId,
                calendarCode,
                instructionCreatedByEoFlag,
                fromScheduleRateFlag,
              },
            ]);
            orderDs.loadData([]);
            orderDs.reset();
            break;
          case 'TIMING':
            // 定时补货
            timingDs.loadData([
              {
                distributionCycle,
                materialConsumeRate,
                materialConsumeRateUomId,
                bufferInventory,
              },
            ]);
            rationDs.loadData([]);
            rationDs.reset();
            break;
          default:
            break;
        }
        // 复制来源(只用来渲染数据)
        copySourceDs.loadData([data]);
        copySourceDs.current.init('materialSelect', materialSelectData);
      } else {
        notification.error({
          message: res.message,
        });
      }
    });
  };

  useImperativeHandle(ref, () => ({
    detailQuery,
    // 暴露给父组件的方法
    submit: async () => {
      const validate = await getResult();
      // 根据ds生成数据
      const baseDetailData = baseInfoDs.toData();
      let tabData = [] as Array<object>;
      let pass = true;
      if (validate) {
        // 对【包装倍数配送】单独校验
        if (baseDetailData[0].multiplesOfPackFlag === 'Y') {
          if (!baseDetailData[0].packQty) {
            notification.error({
              message: intl
                .get(`${modelPrompt}.packQtyValidate`)
                .d('当启用配送属性【包装倍数配送】时，配送包装数必须大于0'),
            });
            pass = false;
          }
        }
        // 将不同tab的数据分发给给tabData一个对象，并对QUANTITATIVE下数据进行逻辑校验
        switch (currentTab as string) {
          case 'QUANTITATIVE':
            tabData = rationDs.toData();
            if ((tabData[0] as any).minInventory > (tabData[0] as any).maxInventory) {
              notification.error({
                message: intl.get(`${modelPrompt}.minMaxValidate`).d('最大库存必须大于最小库存'),
              });
              pass = false;
            } else if ((tabData[0] as any).bufferInventory > (tabData[0] as any).maxInventory ||
              (tabData[0] as any).bufferInventory < (tabData[0] as any).minInventory) {
              notification.error({
                message: intl.get(`${modelPrompt}.bufferValidate`).d('安全库存必须在最大最小库存之间'),
              });
              pass = false;
            }
            break;
          case 'BY_WO':
            tabData = orderDs.toData();
            break;
          case 'IN_TURN':
            tabData = sequenceDs.toData();
            break;
          case 'TIMING':
            tabData = timingDs.toData();
            break;
          default:
            break;
        }
        // 物料配送周期必须为路线配送周期的倍数
        if (currentTab !== 'QUANTITATIVE') {
          if (baseDetailData[0].distributionRouteCycle) {
            if (
              !Number.isInteger(
                (tabData[0] as any).distributionCycle / baseDetailData[0].distributionRouteCycle,
              )
            ) {
              notification.error({
                message: intl
                  .get(`${modelPrompt}.distributionCycleRule`)
                  .d('物料配送属性配送周期需为配送路线配送周期的整数倍'),
              });
              pass = false;
            }
          }
        }
      }
      let success = false; // 返回值需要
      if (validate && pass) {
        // 取出用户选择的物料/物料类别的type
        const { materialSelect } = baseDetailData[0];
        let resultId = '';
        const url = `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-pfep-distribution/pfep/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DISTRIBUTION_DETAIL.MATERIAL_BASIC,${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DISTRIBUTION_DETAIL.CATEGORY_BASIC`;
        await myInstance
          .post(url, {
            fromScheduleRateFlag: 'N',
            ...baseDetailData[0],
            ...tabData[0],
            distributionMode: currentTab,
            organizationId: undefined,
          })
          .then(res => {
            if (res.data.success) {
              notification.success({});
              success = true;
              resultId = res.data.rows;
            } else {
              notification.error({
                message: res.data.message,
              });
            }
          });
        return { success, resultId, materialSelect };
      }
      return { success };

    },
    reset: () => {
      detailQuery(id, type);
    },
  }));
  const getResult = async () => {
    let modeValidate = false;
    const detailValidate = await baseInfoDs.validate(false, true); // 基本信息校验
    if (detailValidate) {
      let data;
      if (selectOrganizationType === 'LOCATOR') {
        data = inTimeDs.toData();
      } else {
        data = fixTimeDs.toData();
      }
      const { distributionMode } = data[0];
      if (distributionMode) {
        // 根据不同的模式校验不同不同模式下的数据
        switch (distributionMode) {
          case 'TIMING':
            // 先校验是否必输，再判断具体的数据是否满足规则
            modeValidate = await timingDs.validate();
            if (modeValidate) {
              // eslint-disable-next-line no-case-declarations
              const timingDsData = timingDs.toData();
              if ((!timingDsData[0] as any).distributionCycle) {
                notification.error({
                  message: intl
                    .get(`${modelPrompt}.distributionCycleValidate`)
                    .d('配送周期要大于0'),
                });
                return false;
              }
            }
            break;
          case 'IN_TURN':
            modeValidate = await sequenceDs.validate();
            if (modeValidate) {
              // eslint-disable-next-line no-case-declarations
              const sequenceDsData = sequenceDs.toData();
              if ((!sequenceDsData[0] as any).distributionCycle) {
                notification.error({
                  message: intl
                    .get(`${modelPrompt}.distributionCycleValidate`)
                    .d('配送周期要大于0'),
                });
                return false;
              }
            }
            break;
          case 'BY_WO':
            modeValidate = await orderDs.validate();
            break;
          case 'QUANTITATIVE':
            modeValidate = await rationDs.validate();
            break;
          default:
            break;
        }
      } else {
        notification.error({
          message: intl.get(`${modelPrompt}.checkDistributionMode`).d('请选择配送模式'),
        });
        return false;
      }
    }
    return detailValidate && modeValidate;
  };

  const handleChangeSelectBox = val => {
    if (val === 'QUANTITATIVE') {
      if (baseInfoDs.current.get('DISTRIBUTION_CATEGORY') === 'TIMING_DISTRIBUTION') {
        // 重置配送路线
        baseInfoDs.current.init('distributionRouteCode1');
      }
      baseInfoDs.current.set('DISTRIBUTION_CATEGORY', 'TIMELY_DISTRIBUTION');
    } else {
      if (baseInfoDs.current.get('DISTRIBUTION_CATEGORY') === 'TIMELY_DISTRIBUTION') {
        // 重置配送路线
        baseInfoDs.current.init('distributionRouteCode1');
      }
      baseInfoDs.current.set('DISTRIBUTION_CATEGORY', 'TIMING_DISTRIBUTION');
    }
    setCurrentTab(val);
  };

  // 修改站点后对关联站点相关数据清除
  const handleChangeSiteCode = () => {
    baseInfoDs.current.init('materialCode1');
    baseInfoDs.current.init('sourceLocatorCode1'); // 来源库位
    baseInfoDs.current.init('distributionRouteCode1');
    baseInfoDs.current.init('pfepDisOrganization');
    baseInfoDs.current.init('targetLocatorCode');
    baseInfoDs.current.init('distributionCategoryDesc');
    baseInfoDs.current.init('organizationType');
    inTimeDs.current.reset();
    fixTimeDs.current.reset();
    // 切换目标点位类型的时候要初始化配送路线的禁用设置参数
    baseInfoDs.current.init('DISTRIBUTION_CATEGORY');
    // 切换目标点位类型的时候要重置当前激活的配送模式tab
    setCurrentTab('');
    // 隐藏配送明细
    setSelectOrganizationType('');
  };

  const changeLovSelect = val => {
    baseInfoDs.current.init('organizationType', val);
    baseInfoDs.current.init('pfepDisOrganization');
    baseInfoDs.current.init('targetLocatorCode');
    inTimeDs.current.reset();
    fixTimeDs.current.reset();
    // 切换目标点位类型的时候要初始化配送路线的禁用设置参数
    baseInfoDs.current.init('DISTRIBUTION_CATEGORY');
    // 切换目标点位类型的时候要重置当前激活的配送模式tab
    setCurrentTab('');
    // 显示配送明细
    setSelectOrganizationType(val);
  };

  const changeMaterialSelect = () => {
    baseInfoDs.current.init('materialCode1');
  };

  // 改为六位小数
  const handleChangeNumber = (field, value) => {
    if (typeof value === 'number') {
      const data = {};
      data[field] = value.toFixed(6);
      rationDs.current.set(data);
    }
  };

  return (
    <>
      <Collapse
        bordered={false}
        defaultActiveKey={['baseInfo', 'distributionDetail', 'distributionLine']}
      >
        <Panel key="baseInfo" header={intl.get(`${modelPrompt}.baseInfo`).d('基础信息')}>
          {customizeForm(
            {
              code: custCode,
            },
            <Form disabled={!canEdit} dataSet={baseInfoDs} columns={columns} labelWidth={112}>
              <Lov
                name="siteCode1"
                required
                placeholder=" "
                onChange={handleChangeSiteCode}
                disabled={id !== 'create'}
              />
              <LovSelect
                dataSet={baseInfoDs}
                name="pfepDisOrganization"
                selectName="organizationType"
                required
                placeholder=" "
                noCache
                selectChange={changeLovSelect}
                selectDisabled={id !== 'create'}
                lovDisabled={id !== 'create'}
              />
              <Lov name="targetLocatorCode" required placeholder=" " noCache />
              <LovSelect
                dataSet={baseInfoDs}
                name="materialCode1"
                selectName="materialSelect"
                required
                placeholder=" "
                noCache
                selectChange={changeMaterialSelect}
                selectDisabled={id !== 'create'}
                lovDisabled={id !== 'create'}
              />
              <Lov name="sourceLocatorCode1" required placeholder=" " noCache dataSet={baseInfoDs} />
              <Lov name="targetPlaceLov" />
              <NumberField name="packQty" min={0} step={1} />
              <NumberField name="distributionQty" min={0} step={1} />
              <NumberField name="singleQty" min={0} step={1} />
              <Switch name="autoCallingFlag" />
              <Switch name="multiplesOfPackFlag" />
              <Switch name="enableFlag" />
              <NumberField name="perCompletedQty" />
              <Switch name="singleFlag" />
              <Switch name="wkcBoudingFlag" />
            </Form>,
          )}
        </Panel>
        {/* 配送模式明细 */}
        {selectOrganizationType && (
          <Panel
            key="distributionDetail"
            header={intl.get(`${modelPrompt}.distributionDetail`).d('配送模式明细')}
          >
            {selectOrganizationType === 'WORKCELL' && (
              <Form disabled={!canEdit} dataSet={fixTimeDs} columns={3} labelWidth={112}>
                <SelectBox
                  mode={ViewMode.button}
                  name="distributionMode"
                  className={styles.selectBox}
                  onChange={handleChangeSelectBox}
                />
              </Form>
            )}
            {selectOrganizationType === 'LOCATOR' && (
              <Form disabled={!canEdit} dataSet={inTimeDs} columns={3} labelWidth={112}>
                <SelectBox
                  mode={ViewMode.button}
                  name="distributionMode"
                  className={styles.selectBox}
                  onChange={handleChangeSelectBox}
                />
              </Form>
            )}

            {/* 定时补货 */}
            {currentTab === 'TIMING' && selectOrganizationType === 'LOCATOR' && (
              <Form disabled={!canEdit} dataSet={timingDs} columns={columns} labelWidth={112}>
                <NumberField
                  name="distributionCycle"
                  nonStrictStep
                  renderer={({ value }) =>
                    typeof value === 'number'
                      ? `${value} ${intl.get(`${modelPrompt}.mins`).d('分钟')}`
                      : intl.get(`${modelPrompt}.mins`).d('分钟')
                  }
                />
                <NumberFieldSelect
                  dataSet={timingDs}
                  name="materialConsumeRate"
                  selectName="materialConsumeRateUomId"
                  required
                  placeholder=" "
                />
                <TextField name="bufferInventory" />
              </Form>
            )}
            {/* 顺序补货 */}
            {currentTab === 'IN_TURN' && selectOrganizationType === 'WORKCELL' && (
              <Form disabled={!canEdit} dataSet={sequenceDs} columns={columns} labelWidth={112}>
                <NumberField
                  name="distributionCycle"
                  nonStrictStep
                  renderer={({ value }) =>
                    typeof value === 'number'
                      ? `${value} ${intl.get(`${modelPrompt}.mins`).d('分钟')}`
                      : intl.get(`${modelPrompt}.mins`).d('分钟')
                  }
                />
                <NumberFieldSelect
                  dataSet={sequenceDs}
                  name="materialConsumeRate"
                  selectName="materialConsumeRateUomId"
                  required
                  placeholder=" "
                />
                <NumberField
                  name="bufferPeriod"
                  nonStrictStep
                  renderer={({ value }) =>
                    typeof value === 'number'
                      ? `${value} ${intl.get(`${modelPrompt}.mins`).d('分钟')}`
                      : intl.get(`${modelPrompt}.mins`).d('分钟')
                  }
                />
                <Lov name="calendar" required placeholder=" " />
                <Switch name="instructionCreatedByEoFlag" />
                <Switch name="fromScheduleRateFlag" />
              </Form>
            )}
            {/* 订单补货 */}
            {currentTab === 'BY_WO' && selectOrganizationType === 'WORKCELL' && (
              <Form disabled={!canEdit} dataSet={orderDs} columns={columns} labelWidth={112}>
                <NumberField
                  name="distributionCycle"
                  nonStrictStep
                  renderer={({ value }) =>
                    typeof value === 'number'
                      ? `${value} ${intl.get(`${modelPrompt}.mins`).d('分钟')}`
                      : intl.get(`${modelPrompt}.mins`).d('分钟')
                  }
                />
                <NumberFieldSelect
                  dataSet={orderDs}
                  name="materialConsumeRate"
                  selectName="materialConsumeRateUomId"
                  required
                  placeholder=" "
                />
                <TextField name="bufferInventory" />
                <Switch name="fromScheduleRateFlag" />
              </Form>
            )}
            {/* 定量补货 */}
            {currentTab === 'QUANTITATIVE' && selectOrganizationType === 'LOCATOR' && (
              <Form disabled={!canEdit} dataSet={rationDs} columns={columns} labelWidth={112}>
                <NumberField
                  name="minInventory"
                  min={0}
                  onChange={value => {
                    handleChangeNumber('minInventory', value);
                  }}
                  renderer={({ value }) => (typeof value === 'number' ? `${value.toFixed(6)}` : '')}
                />
                <NumberField
                  name="bufferInventory"
                  min={0}
                  onChange={value => {
                    handleChangeNumber('bufferInventory', value);
                  }}
                  renderer={({ value }) => (typeof value === 'number' ? `${value.toFixed(6)}` : '')}
                />
                <NumberField
                  name="maxInventory"
                  min={0}
                  onChange={value => {
                    handleChangeNumber('maxInventory', value);
                  }}
                  renderer={({ value }) => (typeof value === 'number' ? `${value.toFixed(6)}` : '')}
                />
              </Form>
            )}
          </Panel>
        )}
        <Panel
          key="distributionLine"
          header={intl.get(`${modelPrompt}.distributionLine`).d('配送路线')}
        >
          <Form disabled={!canEdit} dataSet={baseInfoDs} columns={columns} labelWidth={112}>
            <Lov name="distributionRouteCode1" placeholder=" " noCache />
            <TextField name="distributionCategoryDesc" disabled />
            <NumberField
              name="distributionRouteCycle"
              nonStrictStep
              disabled
              renderer={({ value }) =>
                typeof value === 'number'
                  ? `${value} ${intl.get(`${modelPrompt}.mins`).d('分钟')}`
                  : intl.get(`${modelPrompt}.mins`).d('分钟')
              }
            />
          </Form>
        </Panel>
      </Collapse>
    </>
  );
};

export default forwardRef(Detail);
