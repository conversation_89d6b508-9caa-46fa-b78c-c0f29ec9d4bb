/**
 * @Description: 事务报表平台
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-25 18:09:22
 * @LastEditors: <<EMAIL>>
 */

import React, { useCallback, useMemo, useEffect } from 'react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Collapse, Tag } from 'choerodon-ui';
import { isNil } from 'lodash';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import intl from 'utils/intl';
import request from 'utils/request';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@utils/config';
import SearchForm from './OverrideTableBarC7n'
import { tableDS } from './stories';
import { lineTableDS } from './stories/lineTableDS';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.alarmLimitReport.list';
const Host = `${BASIC.HMES_BASIC}`


const AlarmLimitReport = (props) => {

  const { tableDs, } = props;



  const lineTableDs = useMemo(() => new DataSet(lineTableDS()), []);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'siteCode',
        lock: ColumnLock.left,
        width: 120,
      },
      {
        name: 'warehouseCode',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'materialCode',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'materialName',
        lock: ColumnLock.left,
        width: 150,
      },
      {
        name: 'supplierCode',
        width: 150,
      },
      {
        name: 'supplierName',
        width: 150,
      },
      {
        name: 'minQty',
        width: 90,
      },
      {
        name: 'maxQty',
        width: 90,
      },
      {
        name: 'onhandQty',
        width: 120,
      },
      {
        name: 'secureFlag',
        width: 150,
        renderer: ({ value }) => {
          return value === 'Y' ? <Tag color='red'>{intl.get(`tarzan.common.label.yes`).d('是')}</Tag> : <Tag>{intl.get(`tarzan.common.label.no`).d('否')}</Tag>
        }
      },
      {
        name: 'maxInventoryFlag',
        width: 120,
        renderer: ({ value }) => {
          return value === 'Y' ? <Tag color='red'>{intl.get(`tarzan.common.label.yes`).d('是')}</Tag> : <Tag>{intl.get(`tarzan.common.label.no`).d('否')}</Tag>
        }
      },
      {
        name: 'qty',
        width: 90,
      },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(() => {
    return [...columns, {
      name: 'creationDate',
      width: 150,
    },]
  }, [])

  useEffect(()=>{
    // 获取默认站点
    request(`${Host}/v1/${tenantId}/wms-auto-shelfs/getUser`, {
      method: 'GET',
    }).then(res => {
      const response = getResponse(res)
      if(response){
        tableDs.queryDataSet?.current?.set('siteLov', {
          siteId: response.siteId,
          siteCode: response.siteCode,
          siteName: response.siteName,
        });
      }
    }).finally(()=>{
      query()
    })
  },[])

  const resetQuery=()=>{
    tableDs.queryDataSet?.loadData([{}])
    lineTableDs.queryDataSet?.loadData([{}])
  }

  const query=async ()=>{
    // let serarchData=.current.toJSONData()
    console.log('fix build failed')
    lineTableDs.queryDataSet=tableDs.queryDataSet
    await Promise.all([tableDs.query(),lineTableDs.query()])
  }

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  };

  const getHistoryExportQueryParams = () => {
    if (lineTableDs.selected.length > 0) {
      const data = lineTableDs.selected.map(ele => ele.toJSONData().minmaxId)
      return {
        minmaxIds: data
      }
    }
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.mes.alarmLimitReport.list.title').d('MIN/MAX库存预警报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-minmax-onhands/list/export/get/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.onTime.export`).d('实时数据导出')}
        />
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-minmax-onhands/list/export/get/ui/history`}
          queryParams={getHistoryExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.history.export`).d('历史数据导出')}
        />
      </Header>
      <Content>
        <SearchForm queryDataSet={tableDs.queryDataSet} resetQuery={resetQuery} query={query}/>
        <Collapse bordered={false} defaultActiveKey={['onTime','history']}>
          <Panel
            header={intl.get(`${modelPrompt}.onTime.information`).d('实时结果')}
            key="onTime"
            dataSet={lineTableDs}
          >
            <Table
              queryBar={TableQueryBarType.none}
              dataSet={tableDs}
              columns={columns}
              searchCode="AlarmLimitReport"
              customizedCode="AlarmLimitReport"
            />
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.history.information`).d('历史结果')}
            key="history"
            dataSet={lineTableDs}
          >
            <Table
            queryBar={TableQueryBarType.none}
              dataSet={lineTableDs}
              columns={lineColumns}
              customizedCode="AlarmLimitReport2"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.mes.event.materialTransIface', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(AlarmLimitReport),
);
