import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.customerSuppliedParts';
const tenantId = getCurrentOrganizationId();

const headerTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  paging: false,
  dataKey: 'rows.deliveryDoc',
  cacheSelection: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms_customer_supplied_parts/info/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('客供件单号'),
    },
    {
      name: 'instructionTypeObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
      lookupCode: 'WMS.INSTRUCTION_DOC_CUSTOMER',
      required: true,
    },
    {
      name: 'instructionDocType',
      bind: 'instructionTypeObj.value',
    },
    {
      name: 'instructionTypeTag',
      bind: 'instructionTypeObj.tag',
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      disabled: true,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      lovPara: {
        tenantId,
        noworthFlag: 'Y',
      },
      ignore: 'always',
      noCache: true,
      required: true,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteLov.siteCode',
    },
    {
      name: 'expectedArrivalTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.expectedArrivalTime`).d('预计到货时间'),
    },
    {
      name: 'demandTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.demandTime`).d('需求时间'),
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerCode',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
      required: true,
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'customerCode',
      bind: 'customerLov.customerCode',
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'customerPo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerPo`).d('客户采购订单号'),
      required: true,
    },
  ],
});

const lineTableDS = () => {
  return {
    autoQuery: false,
    autoCreate: false,
    paging: false,
    selection: false,
    forceValidate: true,
    primaryKey: 'instructionId',
    fields: [
      {
        name: 'lineNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      },
      {
        name: 'identifyType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
      },
      {
        name: 'materialLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        lovCode: 'WMS.REQUISITION_TASK_MATERIAL',
        ignore: 'always',
        required: true,
        dynamicProps: {
          lovPara({ record }) {
            return {
              tenantId,
              customerCode: record.get('customerCode'),
            };
          },
        },
      },
      {
        name: 'materialId',
        type: FieldType.number,
        bind: 'materialLov.materialId',
      },
      {
        name: 'revisionCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
        // bind: 'materialLov.currentRevisionCode',
      },
      {
        name: 'materialName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        bind: 'materialLov.materialName',
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        bind: 'materialLov.materialCode',
      },
      {
        name: 'quantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.quantity`).d('数量'),
        required: true,
        min: 0,
      },
      {
        name: 'uomCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
        bind: 'materialLov.uomCode',
      },
      {
        name: 'uomId',
        type: FieldType.string,
        bind: 'materialLov.uomId',
      },
      {
        name: 'wareHouseLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
        lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
        required: true,
        lovPara: {
          tenantId,
          locatorCategory: ['AREA'],
        },
      },
      {
        name: 'locatorCode',
        type: FieldType.string,
        bind: 'wareHouseLov.locatorCode',
      },
      {
        name: 'toLocatorId',
        type: FieldType.number,
        bind: 'wareHouseLov.locatorId',
      },
      {
        name: 'urgentFlag',
        type: FieldType.string,
        trueValue: 'Y',
        falseValue: 'N',
        defaultValue: 'N',
        label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
      },
      {
        name: 'toleranceFlag',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
        trueValue: 'Y',
        falseValue: 'N',
      },
      {
        name: 'toleranceType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
        textField: 'description',
        valueField: 'typeCode',
        lovPara: { tenantId },
        lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=MODELING&typeGroup=INSTRUCTION_TOLERANCE_TYPE`,
        lookupAxiosConfig: {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            return rows;
          },
        },
      },
      {
        name: 'toleranceMinValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差'),
      },
      {
        name: 'toleranceMaxValue',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差'),
      },
      {
        name: 'remark',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
    ],
  };
};

export { headerTableDS, lineTableDS };
