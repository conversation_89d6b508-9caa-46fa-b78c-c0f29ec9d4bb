import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'hzero-front/lib/utils/utils';
import moment from 'moment';

import { BASIC } from '@utils/config';
import intl from 'utils/intl';

// const RTHost = '/rt-mes-24510';


const tenantId = getCurrentOrganizationId();
const modelPrompt = 'hwms.purchaseNeed.fields';

const tableDS = () => ({
  autoQuery: false,
  selection: 'single',
  rowKey:'demandOrderId',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'demandOrder',
      label: intl.get(`${modelPrompt}.demandOrder`).d('需求单号'),
      type: FieldType.string,
    },
    {
      name: 'demandOrderTypeMeaning',
      label: intl.get(`${modelPrompt}.demandOrderType`).d('需求单号类型'),
      type: FieldType.string,
    },
    {
      name: 'returnOrder',
      label: intl.get(`${modelPrompt}.returnOrder`).d('采购单号'),
      type: FieldType.string,
    },
    {
      name: 'supplierCode',
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      type: FieldType.string,
    },
    {
      name: 'supplierName',
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
      type: FieldType.string,
    },
    {
      name: 'prodSampleFlag',
      label: intl.get(`${modelPrompt}.prodSampleFlag`).d('量产样标识'),
      type: FieldType.string,
    },
    {
      name: 'buyerCode',
      label: intl.get(`${modelPrompt}.buyerCode`).d('采购员'),
      type: FieldType.string,
    },
    {
      name: 'makerCode',
      label: intl.get(`${modelPrompt}.makerCode`).d('制单员'),
      type: FieldType.string,
    },
    {
      name: 'demandType',
      label: intl.get(`${modelPrompt}.demandType`).d('需求类型'),
      type: FieldType.string,
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('处理消息'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
  ],
  queryFields: [
    {
      name: 'demandOrder',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.demandOrder`).d('需求单号'),
    },
    {
      name: 'demandOrderType',
      label: intl.get(`${modelPrompt}.demandOrderType`).d('需求单号类型'),
      lookupCode: 'WMS.DEMAND_TYPE',
      type: FieldType.string,
      valueField:'description',
      textField:'meaning',
    },
    {
      name: 'returnOrder',
      label: intl.get(`${modelPrompt}.returnOrder`).d('采购订单号'),
      type: FieldType.string,
    },
    {
      name: 'processDocNum',
      label: intl.get(`${modelPrompt}.processDocNum`).d('资材审批单号'),
      type: FieldType.string,
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      multiple:true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialIdLists',
      type: FieldType.string,
      bind: 'materialLov.materialId',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
      lovCode: 'MT.MODEL.SUPPLIER',
      multiple:true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierIdLists',
      type: FieldType.string,
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      multiple:true,
      lovPara: {
        tenantId,
        noworthFlag: 'Y',
      },
      ignore: FieldIgnore.always,
      noCache: true,
    },
    {
      name: 'siteIdLists',
      type: FieldType.string,
      bind: 'siteLov.siteId',
    },
    {
      name: 'locator',
      type: FieldType.object,
      ignore: FieldIgnore.always,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      multiple: true,
      lovPara: {
        tenantId,
        locatorCategory: ['AREA'],
      },
    },
    {
      name: 'locatorIdLists',
      type: FieldType.string,
      bind: 'locator.locatorId',
    },
    {
      name: 'deleteFlag',
      label: intl.get(`${modelPrompt}.deleteFlag`).d('行删除标识'),
      type: FieldType.string,
      lookupCode:'WMS.DELETE_FLAG',
    },
    {
      name: 'creationDateFrom',
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      type: FieldType.dateTime,
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      type: FieldType.dateTime,
      min: 'creationDateFrom',
    },
    {
      name: 'realName',
      label: intl.get(`${modelPrompt}.createUser`).d('创建人'),
      type: FieldType.string,
    },
    {
      name: 'demandTimeFrom',
      label: intl.get(`${modelPrompt}.demandTimeFrom`).d('需求时间从'),
      type: FieldType.dateTime,
      max: 'demandTimeTo',
    },
    {
      name: 'demandTimeTo',
      label: intl.get(`${modelPrompt}.demandTimeTo`).d('需求时间至'),
      type: FieldType.dateTime,
      min: 'demandTimeFrom',
    },
  ],
  transport: {
    read: ({data,params }) => {
      const realParams = {
        ...data,
        ...params,
        materialIdLists:data.materialIdLists.join(','),
        locatorIdLists:data.locatorIdLists.join(','),
        siteIdLists:data.siteIdLists.join(','),
        supplierIdLists:data.supplierIdLists.join(','),
      }
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/procurement-demand-platform/demandHeadQuery`,
        method: 'GET',
        data:realParams,
      };
    },
  },
});

const lineTableDS = () => ({
  autoQuery: false,
  paging:false,
  selection: 'multiple',
  rowKey:'demandLineId',
  dataKey: 'rows',
  fields: [
    {
      name: 'lineNum',
      label: intl.get(`${modelPrompt}.lineNum`).d('行号'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      type: FieldType.string,
    },
    {
      name: 'materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      type: FieldType.string,
    },
    {
      name: 'qty',
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
      type: FieldType.number,
      required: true,
    },
    {
      name: 'qtyCopy',
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
      type: FieldType.number,
      required: true,
    },
    {
      name: 'uomCode',
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      type: FieldType.string,
    },
    {
      name: 'siteCode',
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      type: FieldType.string,
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('仓库'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      lovPara: {
        tenantId,
        locatorCategory: 'AREA',
      },
      required: true,
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorCode',
      label: intl.get(`${modelPrompt}.locatorCode`).d('仓库'),
      type: FieldType.string,
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'demandDate',
      label: intl.get(`${modelPrompt}.demandDate`).d('需求时间'),
      type: FieldType.dateTime,
      required: true,
      transformRequest: (value) => {
        if (!value) {
          return value;
        }
        return moment(value).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      name: 'processDocNum',
      label: intl.get(`${modelPrompt}.processDocNum`).d('资材审批单'),
      type: FieldType.string,
    },
    {
      name: 'applyPersonName',
      label: intl.get(`${modelPrompt}.applyPersonName`).d('提需人姓名'),
      type: FieldType.string,
    },
    {
      name: 'deleteFlag',
      label: intl.get(`${modelPrompt}.deleteFlag`).d('删除标识'),
      type: FieldType.string,
      lookupCood:'WMS.DELETE_FLAG',
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.createionDate`).d('创建时间'),
      type: FieldType.string,
    },
    {
      name: 'lastUpdateDate',
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      type: FieldType.string,
    },
    {
      name: 'poCotractNum',
      label: intl.get(`${modelPrompt}.poCotractNum`).d('项目代码/采购合同编号-行号'),
      type: FieldType.string,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/procurement-demand-platform/demandLineQuery`,
        method: 'GET',
      };
    },
    update:() => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/procurement-demand-platform/demandLineSave`,
        method: 'POST',
      };
    },
  },
});


export { tableDS, lineTableDS };
