import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.ass.externalWarehouseInventory';

const tenantId = getCurrentOrganizationId();

const outDeliverFactory = () =>
  new DataSet({
    selection: false,
    paging: false,
    autoCreate: false,
    autoQuery: false,
    dataKey: 'rows',
    totalKey: 'totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'instructionDocNum',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.instructionDocNum`).d('送货单号'),
        },
        {
          name: 'warehouseCode',
          lookupCode: 'WMS.OUTSIDE_WAREHOUSE',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.form.warehouseCode`).d('出库仓库'),
        },
      ],
    }),
    fields: [
      {
        name: 'instructionDocNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.instructionDocNum`).d('送货单号'),
      },
      {
        name: 'lineNumber',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.lineNumber`).d('送货单行号'),
      },
      {
        name: 'supplierCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('供应商编码'),
        type: FieldType.string,
      },
      {
        name: 'supplierName',
        label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
        type: FieldType.string,
      },
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        type: FieldType.string,
      },
      {
        name: 'materialName',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        type: FieldType.string,
      },
      {
        name: 'qty',
        label: intl.get(`${modelPrompt}.qty`).d('数量'),
        type: FieldType.string,
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'GET',
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-external-inventory-manage/deliveryDoc/ui`,
          transformResponse: value => {
            let listData: any = {};
            try {
              listData = JSON.parse(value);
            } catch (err) {
              listData = {
                message: err,
              };
            }
            if (!listData.success) {
              return {
                ...listData,
                failed: true,
              };
            }
            return listData;
          },
        };
      },
    },
  });

export default outDeliverFactory;
