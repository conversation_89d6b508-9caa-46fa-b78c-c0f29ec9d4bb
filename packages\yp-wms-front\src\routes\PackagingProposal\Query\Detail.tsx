/**
 * @Description: 物料批管理平台-详情页
 * @Author: <<EMAIL>>
 * @Date: 2021-03-01 10:47:34
 * @LastEditTime: 2022-12-14 16:03:33
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useEffect } from 'react';
import {
  Form,
  TextField,
  CheckBox,
  Table,
  DataSet,
  Lov,
  Select,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Spin, Collapse } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/lib/table';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import UploadModal from '../Component/Upload';
import { formDS, packagingTypeDS, truckInfoDS, supplierPackageListDS } from '../stores/DetailDs';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'hwms.packageProposalQuery';

const PackageProposalDetail = props => {
  const {
    match: { params: { id } },
  } = props;

  const fetchPackageDetail = useRequest({
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-package-proposals/query/headDetil/ui`,
    method: 'get',
  }, {
    manual: true,
  });

  const formDs = useMemo(() => new DataSet(formDS()), []);
  const packagingTypeDs = useMemo(() => new DataSet(packagingTypeDS()), []);
  const truckInfoDs = useMemo(() => new DataSet(truckInfoDS()), []);
  const supplierPackageListDs = useMemo(() => new DataSet(supplierPackageListDS()), []);

  useEffect(() => {
    if(id) {
      handleFetchDetail(id);
    }
  }, [id]);

  const handleFetchDetail = (currentId) => {
    supplierPackageListDs.setQueryParameter('packageProposalId', currentId);
    supplierPackageListDs.query();
    return fetchPackageDetail.run({
      params: {
        packageProposalId: currentId,
      },
      onSuccess: (res) => {
        if(res) {
          formDs.loadData([res]);
          packagingTypeDs.loadData([res]);
          truckInfoDs.loadData([res]);
        } 
      },
    });
  };

  const packageTypeColumns: ColumnProps = [
    { name: 'packageType' },
    {
      title: intl.get(`${modelPrompt}.boxSize`).d('箱外尺寸'),
      children: [
        { name: 'boxL' },
        { name: 'boxW' },
        { name: 'boxH' },
      ],
    },
    { name: 'capacity' },
    {
      title: intl.get(`${modelPrompt}.weight`).d('重量'),
      children: [
        { name: 'materialWeight' },
        { name: 'boxWeight' },
        { name: 'sumWeight' },
      ],
    },
    { name: 'labelPosition' },
    { name: 'boxQty' },
    {
      title: intl.get(`${modelPrompt}.containerSize`).d('托盘外尺寸'),
      children: [
        { name: 'conL' },
        { name: 'conW' },
        { name: 'conH' },
      ],
    },
    {
      name: 'eachPackage',
      renderer: ({ record }) => (
        <CheckBox checked={record.get('eachPackage') === "Y"} />
      ),
    },
    {
      name: 'dustCover',
      renderer: ({ record }) => (
        <CheckBox checked={record.get('dustCover') === "Y"} />
      ),
    },
    {
      name: 'internalItem',
      renderer: ({ record }) => (
        <CheckBox checked={record.get('internalItem') === "Y"} />
      ),
    },
  ];

  const truckInfoColumns: ColumnProps = [
    { name: 'truckCode' },
    { name: 'axlesQty' },
    { name: 'ton' },
    { name: 'truckL' },
    { name: 'truckW' },
    { name: 'truckH' },
    { name: 'mantissa' },
    {
      title: intl.get(`${modelPrompt}.containerQty`).d('托盘放置数量[mm]'),
      children: [
        { name: 'palletQtyL' },
        { name: 'palletQtyW' },
        { name: 'palletQtyH' },
      ],
    },
    { name: 'loadQty' },
  ];

  const supplierPackageListColumns: ColumnProps = [
    { name: 'packageName' },
    {
      name: 'picture',
      renderer: ({ record }) => (
        <UploadModal
          bucketName="wms-package"
          viewOnly
          btnText={intl.get('hzero.common.upload.modal.title').d('附件')}
          attachmentUUID={record.get('picture')}
          showReUploadIcon={false}
          onReUpload={false}
          fileType="image/webp,image/svg,image/png,image/gif,image/jpg,image/jpeg,image/bmp"
        />
      ),
    },
    {
      name: 'emptyFlag',
      renderer: ({ record }) => (
        <CheckBox checked={record.get('emptyFlag') === "Y"} />
      ),
    },
  ];

  return (
    <div className="hmes-style">
      <Spin spinning={fetchPackageDetail.loading}>
        <Header
          title={intl.get(`${modelPrompt}.packageProposalDetail`).d('包装提案明细')}
          backPath="/hwms/packaging-proposal/query/list"
        />
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'baseInfo',
              'concatInfo',
              'packageTypeInfo',
              'truckInfo',
              'supplierPackageListInfo',
              'pictureInfo',
            ]}
          >
            <Panel
              header={intl.get(`${modelPrompt}.baseInfo`).d('包装提案')}
              key="baseInfo"
            >
              <Form labelWidth={112} disabled dataSet={formDs} columns={4}>
                <TextField name="battery" />
                <Lov name="materialLov" />
                <TextField name="materialName" />
                <Lov name="supplierLov" />
                <TextField name="supplierName" />
                <Select name="materialOwnership" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.concatInfo`).d('联系方式')}
              key="concatInfo"
            >
              <Form labelWidth={112} disabled dataSet={formDs} columns={4}>
                <TextField name="department" />
                <DateTimePicker name="startDate" />
                <TextField name="tel" />
                <TextField name="eml" />
                <TextField name="minister" />
                <TextField name="management" />
                <TextField name="engineer" />
              </Form>
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.packageTypeInfo`).d('包装种类')}
              key="packageTypeInfo"
            >
              <Table border columns={packageTypeColumns} dataSet={packagingTypeDs} />
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.truckInfo`).d('装车信息')}
              key="truckInfo"
            >
              <Table border columns={truckInfoColumns} dataSet={truckInfoDs} />
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.supplierPackageListInfo`).d('供应商包装清单与是否返空')}
              key="supplierPackageListInfo"
              dataSet={supplierPackageListDs}
            >
              <Table border columns={supplierPackageListColumns} dataSet={supplierPackageListDs} />
            </Panel>
            <Panel
              header={intl.get(`${modelPrompt}.pictureInfo`).d('实物照片:[零件图片、零件在周转箱/料架摆放图片 (俯视图）、周转箱或料架侧视图、周转箱+托盘组托图片]')}
              key="pictureInfo"
            >
              <Form labelWidth={112} disabled dataSet={formDs} columns={4}>
                <UploadModal
                  bucketName="wms-package"
                  viewOnly
                  btnText={intl.get(`${modelPrompt}.button.attachmentBtn1`).d('图一、零件图片')}
                  attachmentUUID={formDs.current?.get('picture1')}
                  showReUploadIcon={false}
                  onReUpload={false}
                  fileType="image/webp,image/svg,image/png,image/gif,image/jpg,image/jpeg,image/bmp"
                />
                <UploadModal
                  bucketName="wms-package"
                  viewOnly
                  btnText={intl.get(`${modelPrompt}.button.attachmentBtn2`).d('图二、零件在周转箱/料架摆放图片 (俯视图）')}
                  attachmentUUID={formDs.current?.get('picture2')}
                  showReUploadIcon={false}
                  onReUpload={false}
                  fileType="image/webp,image/svg,image/png,image/gif,image/jpg,image/jpeg,image/bmp"
                />
                <UploadModal
                  bucketName="wms-package"
                  viewOnly
                  btnText={intl.get(`${modelPrompt}.button.attachmentBtn3`).d('图三、周转箱或料架侧视图')}
                  attachmentUUID={formDs.current?.get('picture3')}
                  showReUploadIcon={false}
                  onReUpload={false}
                  fileType="image/webp,image/svg,image/png,image/gif,image/jpg,image/jpeg,image/bmp"
                />
                <UploadModal
                  bucketName="wms-package"
                  viewOnly
                  btnText={intl.get(`${modelPrompt}.button.attachmentBtn4`).d('图四、周转箱+托盘组托图片')}
                  attachmentUUID={formDs.current?.get('picture4')}
                  showReUploadIcon={false}
                  onReUpload={false}
                  fileType="image/webp,image/svg,image/png,image/gif,image/jpg,image/jpeg,image/bmp"
                />
                <TextField name="explain1" />
                <TextField name="explain2" />
                <TextField name="explain3" />
                <TextField name="explain4" />
              </Form>
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: ['hwms.packageProposalQuery', 'tarzan.common'],
})(PackageProposalDetail);
