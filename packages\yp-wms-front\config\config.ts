import { extendParentConfig } from '@hzerojs/plugin-micro';

export default extendParentConfig({
  webpack5: {},
  // fastRefresh: {},
  routes: [
    // 呆滞报表
    {
      path: '/hmes/sluggish-report',
      priority: 10,
      routes: [
        {
          path: '/hmes/sluggish-report/list',
          component: '@/routes/sluggishReport/SluggishReport',
          priority: 10,
        },
      ],
    },
    // 销售订单管理
    {
      path: '/hmes/so-delivery/sell-order-manage',
      priority: 10,
      routes: [
        {
          path: '/hmes/so-delivery/sell-order-manage/list',
          component: '@/routes/soDelivery/SellOrder',
          priority: 10,
        },
        {
          path: '/hmes/so-delivery/sell-order-manage/detail/:id',
          component: '@/routes/soDelivery/SellOrder/SellOrderDetail',
        },
      ],
    },
    // 销售发运平台
    {
      path: '/hwms/so-delivery/so-delivery-platform-new',
      routes: [
        {
          path: '/hwms/so-delivery/so-delivery-platform-new/list',
          component: '@/routes/soDelivery/SoDeliveryPlatform/SoDeliveryList',
        },
        {
          path: '/hwms/so-delivery/so-delivery-platform-new/detail/:id',
          component: '@/routes/soDelivery/SoDeliveryPlatform/SoDeliveryDetail',
        },
      ],
    },
    // 报检请求管理平台
    {
      path: '/hwms/inspection/inspection-management-new',
      routes: [
        {
          path: '/hwms/inspection/inspection-management-new/list',
          component: '@/routes/inspection/InspectionManagement',
        },
      ],
    },
    // 领退料工作台-wms-新
    {
      path: '/hwms/receive/receive-return-new',
      routes: [
        {
          path: '/hwms/receive/receive-return-new/list',
          component: '@/routes/receive/ReceiveReturn',
        },
        {
          path: '/hwms/receive/receive-return-new/detail/:id/:docType/:docTypeTag',
          component: '@/routes/receive/ReceiveReturn/Detail',
        },
      ],
    },
    // 杂项工作台-新
    {
      path: '/hwms/in-library/miscellaneous-new',
      routes: [
        {
          path: '/hwms/in-library/miscellaneous-new/list',
          component: '@/routes/inLibrary/miscellaneous',
        },
        {
          path: '/hwms/in-library/miscellaneous-new/detail/:id',
          component: '@/routes/inLibrary/miscellaneous/Detail',
        },
      ],
    },
    // 供应商每月到货及时率报表
    {
      path: '/hwms/supplier/monthly-timeliness-report',
      routes: [
        {
          path: '/hwms/supplier/monthly-timeliness-report/list',
          component: '@/routes/supplier/MonthlyTimelinessReport',
        },
      ],
    },

    // 杂项工作台审批
    {
      path: '/pub/hwms/in-library/miscellaneous-new-oa/list/:code',
      component: '@/routes/inLibrary/miscellaneous-oa',
    },

    // 事物报表平台
    {
      path: '/hmes/transaction-report/platform-new',
      component: '@/routes/transactionReport/TransactionReportPlatform',
    },
    // MIN/MAX库存预警报表
    {
      path: '/hmes/alarm/limit',
      component: '@/routes/alarmLimitReport',
    },
    // 配送路径与人员关系维护
    {
      path: '/hwms/delivery-routes-and-personnel-relationships',
      component: '@/routes/basic/DeliveryRoutesAndPersonnelRelationships',
    },
    // 自动上架配置维护
    {
      path: '/hwms/automatic-shelving-configuration',
      component: '@/routes/basic/AutomaticShelvingConfiguration',
    },
    // 移动事件明细报表-新
    {
      path: '/hmes/transaction-report/mobile-event-detail-report-new',
      component: '@/routes/transactionReport/MobileEventDetailReport',
    },
    // PACK空料架需求报表
    {
      path: '/hwms/pack/empty-material-rack-report',
      component: '@/routes/pack/EmptyMaterialRackReport',
    },
    // 物料批管理平台-WMS
    {
      path: '/hwms/product/material-lot-traceability',
      priority: 10,
      routes: [
        {
          path: '/hwms/product/material-lot-traceability/list/:timer?',
          component: '@/routes/product/MaterialLotTrace',
          priority: 10,
        },
        {
          path: '/hwms/product/material-lot-traceability/detail/:id',
          component: '@/routes/product/MaterialLotTrace/MaterialLotTraceDetail',
          priority: 10,
        },
      ],
    },
    // 容器类型维护
    {
      path: '/hwms/hagd/container-type',
      priority: 10,
      routes: [
        {
          path: '/hwms/hagd/container-type/list',
          component: '@/routes/hagd/Container',
          priority: 10,
        },
        {
          path: '/hwms/hagd/container-type/detail/:id',
          component: '@/routes/hagd/Container/ContainerTypeDetail',
          priority: 10,
        },
      ],
    },
    // 无价值标签管理平台-WMS
    {
      path: '/hwms/product/worthless',
      priority: 10,
      routes: [
        {
          path: '/hwms/product/worthless/list/:timer?',
          component: '@/routes/Worthless/MaterialLotTrace',
          priority: 10,
        },
      ],
    },
    // 厂内/厂外拉动主数据
    {
      path: '/hwms/in-out-factory-master-data',
      component: '@/routes/product/InoutFactoryMasterData',
    },
    // 发运提醒数据维护
    {
      path: '/hwms/shipping-reminder-data-maintenance/list',
      authorized: true,
      component: '@/routes/shippingReminderDataMaintenance',
    },
    // 客供件管理平台
    {
      path: '/hwms/customer-supplied-parts',
      routes: [
        {
          path: '/hwms/customer-supplied-parts/list',
          component: '@/routes/CustomerSuppliedParts',
        },
        {
          path: '/hwms/customer-supplied-parts/detail/:id',
          component: '@/routes/CustomerSuppliedParts/Detail',
        },
      ],
    },
    // 送货单管理
    {
      path: '/hwms/purchase/delivery-management-new',
      routes: [
        {
          path: '/hwms/purchase/delivery-management-new/list',
          component: '@/routes/purchase/Delivery',
        },
        {
          path: '/hwms/purchase/delivery-management-new/detail/:id',
          component: '@/routes/purchase/Delivery/Detail',
        },
      ],
    },
    // 供应商交付异常明细报表
    {
      path: '/hwms/purchase/delivery-exception-details-report',
      routes: [
        {
          path: '/hwms/purchase/delivery-exception-details-report/list',
          component: '@/routes/purchase/DeliveryExceptionDetailsReport',
        },
      ],
    },
    // 送货单提醒管理
    {
      path: '/hwms/purchase/delivery-reminder-management/main',
      component: '@/routes/purchase/DeliveryReminder',
    },
    // 采购退货平台-WMS
    {
      path: '/hwms/purchase/purchase-return',
      priority: 10,
      routes: [
        {
          path: '/hwms/purchase/purchase-return/list',
          component: '@/routes/purchase/PurchaseReturn/PurchaseList',
          priority: 10,
        },
        {
          path: '/hwms/purchase/purchase-return/detail/:id',
          component: '@/routes/purchase/PurchaseReturn/PurchaseDetail',
          priority: 10,
        },
      ],
    },
    // 资材审批平台
    {
      path: '/hwms/purchase/material-approval',
      routes: [
        {
          path: '/hwms/purchase/material-approval/list',
          component: '@/routes/purchase/MaterialApproval/MaterialApprovalList',
        },
        {
          path: '/hwms/purchase/material-approval/detail/:id',
          component: '@/routes/purchase/MaterialApproval/MaterialApprovalDetail',
        },
      ],
    },
    {
      path: '/public/hwms/purchase/material-approval-oa/list/:code',
      authorized: true,
      component: '@/routes/purchase/MaterialApproval/MaterialApprovalList-oa',
    },
    // 事件查询-WMS
    {
      path: '/hwms/event/query',
      component: '@/routes/event/EventQuery',
    },
    // 库存调拨平台
    {
      path: '/hwms/in-library/send-receive-doc-new',
      routes: [
        {
          path: '/hwms/in-library/send-receive-doc-new/list',
          component: '@/routes/inLibrary/SendReceiveDoc/SendReceiveList',
        },
        {
          path: '/hwms/in-library/send-receive-doc-new/detail/:id',
          component: '@/routes/inLibrary/SendReceiveDoc/SendReceiveDetail',
        },
      ],
    },
    // 免费入库明细报表
    {
      path: '/hwms/free-storage-detail-report/list',
      // authorized: true,
      component: '@/routes/freeStorageDetailReport',
    },
    // 事件类型维护-wms
    {
      path: '/hwms/event/event-type',
      component: '@/routes/event/EventType',
    },
    // 事件请求类型维护-wms
    {
      path: '/hwms/event/event-request-type',
      component: '@/routes/event/EventRequestTypeDemo',
    },
    // 边检边用申请管理平台-wms
    {
      path: '/hwms/use-while-checking-platform',
      component: '@/routes/UseWhileCheckingPlatform',
    },
    // 容器管理平台-WMS
    {
      path: '/hwms/product/container-management-platform-new',
      routes: [
        {
          path: '/hwms/product/container-management-platform-new/list',
          component: '@/routes/product/ContainerManagePlatform',
        },
      ],
    },
    // 采购订单管理
    {
      path: '/hwms/purchase/order-management-new',
      priority: 10,
      // authorized: true,
      routes: [
        {
          path: '/hwms/purchase/order-management-new/list',
          component: '@/routes/purchase/OrderNew',
          priority: 10,
        },
      ],
    },
    // 入库单查询
    {
      path: '/hwms/inbound/inbound-order-query',
      priority: 10,
      routes: [
        {
          path: '/hwms/inbound/inbound-order-query/list',
          priority: 10,
          component: '@/routes/inbound/InboundOrderQuery/InboundOrderQueryList',
        },
      ],
    },
    // 事务明细报表-新
    {
      path: '/hmes/transaction-report/transaction-detail-report-new',
      component: '@/routes/transactionReport/TransactionDetailReport',
    },
    // 库存快照报表
    {
      path: '/hmes/transaction-report/inventory-snapshot-report',
      component: '@/routes/transactionReport/InventorySnapshotReport',
    },
    // 供应商零件生产日计划报表
    {
      path: '/hmes/supplier-parts-production/daily-plan-report',
      component: '@/routes/supplierPartsProduction/DailyPlanReport',
    },
    // 供应商零件生产日计划报表-供应商
    {
      path: '/hmes/supplier-parts-production/daily-plan-report-supplier',
      component: '@/routes/supplierPartsProduction/DailyPlanReportSupplier',
    },
    // 供应商零件生产月计划报表
    {
      path: '/hmes/supplier-parts-production/month-plan-report',
      component: '@/routes/supplierPartsProduction/MonthPlanReport',
    },
    // 供应商成品库存实时报表
    {
      path: '/hmes/supplier-finished-product/inventory-real-time-report',
      component: '@/routes/supplierFinishedProduct/InventoryRealTimeReport',
    },
    // 供应商成品库存实时报表-供应商
    {
      path: '/hmes/supplier-finished-product/inventory-real-time-report-supplier',
      component: '@/routes/supplierFinishedProduct/InventoryRealTimeReportSupplier',
    },
    // 供应商成品库存日报表
    {
      path: '/hmes/supplier-finished-product/inventory-daily-time-report',
      component: '@/routes/supplierFinishedProduct/InventoryDailyTimeReport',
    },
    //
    {
      path: '/hmes/event/transaction/type/rel-maintenance-new',
      component: '@/routes/event/TransactionTypeRelMaintenance',
    },
    // 消息处理查询
    {
      path: '/message/message-processing',
      title: '消息处理查询',
      component: '@/routes/message/MessageProcessing',
    },
    {
      path: '/wms/bad-record/platform',
      routes: [
        {
          path: '/wms/bad-record/platform/list',
          component: '@/routes/badRecord/Platform/List',
        },
        {
          path: '/wms/bad-record/platform/detail/:id',
          component: '@/routes/badRecord/Platform/Detail',
        },
      ],
    },
    // 库存查询
    {
      path: '/hwms/inventory/query/:timer?',
      component: '@/routes/inventory/Query/QueryList',
      priority: 10,
    },
    // 例外出库
    {
      path: '/hwms/exceptionalOutbound/query',
      component: '@/routes/inventory/exceptionalOutbound/OutboundList',
      priority: 10,
    },
    // 超期报表
    {
      path: '/hwms/report/over-due',
      component: '@/routes/report/OverDue',
      priority: 10,
    },
    // 自动化任务平台
    {
      path: '/hwms/automated-task-platform',
      component: '@/routes/AutomatedTaskPlatform',
    },
    // 报废单查询
    {
      path: '/hwms/scrap/scrap-form-query',
      component: '@/routes/scrap/ScrapFormQuery',
    },
    // 无价值出入库管理平台
    {
      path: '/hwms/naught/inOut-bound-management-platform',
      component: '@/routes/naught/InOutBoundManagementPlatform',
    },
    // 盘点差异调整
    {
      path: '/hwms/variance/variance-adjustment',
      component: '@/routes/variance/varianceAdjustment',
    },
    // 报废执行
    {
      path: '/public/hwms/scrap/scrap-form-query-oa',
      authorized: true,
      routes: [
        {
          title: '报废执行',
          path: `/public/hwms/scrap/scrap-form-query-oa/list/`,
          component: '@/routes/scrapoa/ScrapFormQuery',
          authorized: true,
        },
        {
          title: '审批流程URL创建',
          path: `/public/hwms/scrap/scrap-form-query-oa/list/:code`,
          component: '@/routes/scrapoa/ScrapFormQuery',
          authorized: true,
        },
      ],
    },
    // 库位维护
    {
      path: '/hmes/organization-modeling/locator',
      priority: 10,
      routes: [
        {
          path: '/hmes/organization-modeling/locator/list',
          component: '@/routes/hcmModel/Locator/LocatorList',
          priority: 10,
        },
        {
          path: '/hmes/organization-modeling/locator/detail/:locatorId',
          component: '@/routes/hcmModel/Locator/LocatorDetail',
          priority: 10,
        },
      ],
    },
    // 售后需求管理
    {
      path: '/ass/demandManage',
      routes: [
        {
          path: '/ass/demandManage/list',
          component: '@/routes/DemandManage/list/listPage',
        },
        {
          path: '/ass/demandManage/detail/:id',
          component: '@/routes/DemandManage/list/detailPage',
        },
      ],
    },
    // 物料维护
    {
      path: '/hmes/product/material-manager',
      priority: 100,
      routes: [
        {
          path: '/hmes/product/material-manager/list',
          priority: 100,
          component: '@/routes/product/Material/MaterialList',
        },
        {
          path: '/hmes/product/material-manager/dist/:id',
          priority: 100,
          component: '@/routes/product/Material/MaterialDetail',
        },
        {
          path: '/hmes/product/material-manager/site-assignment/:id',
          priority: 100,
          component: '@/routes/product/Material/MaterialSiteAssignment',
        },
      ],
    },
    // 物料配送属性维护
    {
      path: '/hmes/product/pfep-distribution-new',
      priority: 10,
      routes: [
        {
          path: '/hmes/product/pfep-distribution-new/list',
          component: '@/routes/product/MaterialDistributionAttr',
          priority: 10,
        },
        {
          path: '/hmes/product/pfep-distribution-new/detail/:type/:id',
          component: '@/routes/product/MaterialDistributionAttr/Detail',
          priority: 10,
        },
      ],
    },
    {
      path: '/public/hwms/packaging-proposal/management/detail-oa/:id',
      authorized: true,
      component: '@/routes/PackagingProposal/Management/Detail-oa',
    },
    // 出厂申请管理
    {
      path: '/hwms/out-factory-apply',
      component: '@/routes/OutFactoryApply',
    },
    // 出厂申请管理
    {
      path: '/public/hwms/out-factory-apply-oa/list/:instructionDocNum',
      authorized: true,
      component: '@/routes/OutFactoryApply/Approval',
    },
    // 盘点工作台
    {
      path: '/hwms/inventory/inventory-workbench',
      priority: 10,
      routes: [
        {
          path: '/hwms/inventory/inventory-workbench/list',
          component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchList',
          priority: 10,
        },
        {
          path: '/hwms/inventory/inventory-workbench/detail/:id',
          component: '@/routes/stocktake/StocktakeWorkbench/StocktakeWorkbenchDetail',
          priority: 10,
        },
      ],
    },
    // 跳批拣配记录报表
    {
      path: '/hwms/inventory/skipbatchpicking',
      component: '@/routes/report/level/QueryList',
      priority: 10,
    },
    // 库存明细三级报表（仓库）
    {
      path: '/hwms/inventory/Level',
      component: '@/routes/level/QueryList',
      priority: 10,
    },
    // 库存明细三级报表（货位）
    {
      path: '/hwms/inventory/GoodsAllocation',
      component: '@/routes/goodsAllocation/QueryList',
      priority: 10,
    },
    // 事务类型接口关系
    {
      path: '/hmes/event-type-interface-relationship',
      component: '@/routes/EventTypeInterfaceRelationship',
    },
    // 库存冻结、不良明细报表
    {
      path: '/hwms/purchase/freeze-defect-report',
      authorized: true,
      component: '@/routes/purchase/FreezeAndDefectReport',
    },
    // 采购需求
    {
      path: '/hwms/purchase/requirement',
      routes: [
        {
          path: '/hwms/purchase/requirement/list',
          component: '@/routes/purchaseNeed/List',
        },
      ],
    },
    // 包装提案管理
    {
      path: '/hwms/packaging-proposal/management',
      routes: [
        {
          path: '/hwms/packaging-proposal/management/list',
          component: '@/routes/PackagingProposal/Management',
        },
        {
          path: '/hwms/packaging-proposal/management/detail/:id',
          component: '@/routes/PackagingProposal/Management/Detail',
        },
      ],
    },
    // 包装提案查询
    {
      path: '/hwms/packaging-proposal/query',
      routes: [
        {
          path: '/hwms/packaging-proposal/query/list',
          component: '@/routes/PackagingProposal/Query',
        },
        {
          path: '/hwms/packaging-proposal/query/detail/:id',
          component: '@/routes/PackagingProposal/Query/Detail',
        },
      ],
    },
    // 称重数据报表
    {
      path: '/hwms/weight-report',
      component: '@/routes/weightReport',
    },

    // 采购收货明细报表
    {
      path: '/hwms/purchase-receipt-report',
      component: '@/routes/PurchaseReceiptReport',
    },
    // 冻结实绩查询报表
    {
      path: '/hmes/frozenPerformanceQueryReport',
      component: '@/routes/FrozenPerformanceQueryReport',
    },
    {
      path: '/hwms/alesShipment',
      authorized: true,
      component: '@/routes/SalesShipment/list/listPage',
    },
    // 本地缓存清理
    {
      path: '/hwms/local/cache/cleanup',
      component: '@/routes/HelpOneselfTo/LocalCacheCleanup',
    },
    {
      title: '无价值入库抽检明细报表',
      path: '/hwms/worthlessIncomingInspection',
      component: '@/routes/WorthlessIncomingInspection/list/listPage',
      authorized: true,
    },
    // 供应商物料安全库存数据维护
    {
      path: '/hwms/safety-stock',
      component: '@/routes/SafetyStock',
    },
    {
      title: '资材物料查询报表',
      path: '/hwms/contractsMaterial',
      component: '@/routes/ContractsMaterial/list/listPage',
    },
    {
      title: '外仓库存管理',
      path: '/wms/externalWarehouseInventory/list',
      component: '@/routes/ExternalWarehouseInventory/list/listPage',
    }
  ],
  hash: true,
  hzeroMicro: {
    // microConfig: {
    //   registerRegex: '\\/.*',
    // },
  },
  // 如果存在发布 lib 包需求,可以解开该配置，对应 babelrc 中的内容
  // 注意若父模块与子模块都配置了module-resolver插件,请保证数组的第三个参数不能为同一个字符串或者都为空
  extraBabelPlugins: [
    [
      'module-resolver',
      {
        root: ['./'],
        alias: {
          // '@': './src',
          '@/hcmMesCus': '@/hcmMesCus', // babel配置-在编译时对二开做一个别名的适配
          '@': './src',
          '@components': 'hcm-components-front/lib/components',
          '@services': 'hcm-components-front/lib/services',
          '@utils': 'hcm-components-front/lib/utils',
          '@assets': 'hcm-components-front/lib/assets',
        },
      },
    ],
  ],
});
