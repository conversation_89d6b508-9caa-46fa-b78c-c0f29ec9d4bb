import React, { useEffect } from 'react';
import { observer } from 'mobx-react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import { Content } from 'components/Page';
import withProps from 'utils/withProps';
import { TabelDS, LineDs } from '../stores/TabelOaDS';
import styles from './index.module.less';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.purchase.materialApproval';

const MaterialApprovalList = observer(props => {
  const {
    tableDs,
    lineDs,
    match: { params },
  } = props;

    useEffect(() => {
      if (params && params.code) {
        tableDs.setQueryParameter('processDocNum', params.code);
      }
      tableDs.query(props.tableDs.currentPage).then(res => {
        if (res?.rows?.content.length) {
          handleLineQuery(res?.rows?.content[0].processDocId);
        }
      });
    }, []);

  const handleLineQuery = processDocId => {
    lineDs.setQueryParameter('processDocId', processDocId);
    lineDs.query();
  };

  const handleRowClick = record => {
    const { processDocId } = record.toData();
    handleLineQuery(processDocId);
  };

  const goLotDetail = id => {
    props.history.push(`/hwms/purchase/material-approval/detail/${id}`);
  };

  const columns: ColumnProps[] = [
    {
      name: 'creationDate',
      width: 150,
    },
    {
      name: 'processDocNum',
      width: 120,
      renderer: ({ record, value }) => {
        if (record?.get('status') === 'DISAPPROVED') {
          return (
            <span className="action-link">
              <a onClick={() => goLotDetail(record?.get('processDocId'))}>{value}</a>
            </span>
          );
        }
        return value;
      },
    },
    {
      name: 'title',
    },
    {
      name: 'status',
    },
    {
      name: 'applyPersonName',
    },
    {
      name: 'applyUnitName',
    },
    {
      name: 'applyTime',
    },
    {
      name: 'budgetPersonName',
    },
    {
      name: 'siteCode',
    },
    {
      name: 'warehouseCode',
    },
    {
      name: 'costCenterCode',
    },
    {
      name: 'centralizedUnit',
    },
    {
      name: 'demandType',
    },
    {
      name: 'applyReason',
    },
  ];

  const lineColumns: ColumnProps[] = [
    {
      name: 'lineNumber',
      width: 80,
    },
    {
      name: 'materialCode',
      width: 80,
    },
    {
      name: 'materialName',
      width: 80,
    },
    {
      name: 'supplierCode',
      width: 80,
    },
    {
      name: 'supplierName',
      width: 80,
    },
    {
      name: 'contractNumber',
      width: 80,
    },
    {
      name: 'brand',
      width: 80,
    },
    {
      name: 'model',
      width: 80,
    },
    {
      name: 'qty',
      width: 80,
    },
    {
      name: 'minPoQty',
      width: 100,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'demandTime',
      width: 80,
    },
    {
      name: 'unitPrice',
      width: 80,
    },
    {
      name: 'price',
      width: 80,
    },
    {
      name: 'applyNum',
      width: 80,
    },
    {
      name: 'applyLineNum',
      width: 80,
    },
    {
      name: 'remark',
      width: 80,
    },
  ];

  return (
    <div className="hmes-style">
      <Content>
        <Table
          searchCode="materialApprovalList"
          customizedCode="materialApprovalList"
          dataSet={tableDs}
          columns={columns}
          highLightRow
          queryBar={TableQueryBarType.none}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          onRow={({ record }) => {
            return {
              onClick: () => {
                handleRowClick(record);
              },
            };
          }}
        />
        ,
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineDs}
          >
            {lineDs && (
              <Table
                customizedCode="materialApprovalLine"
                className={styles['expand-table']}
                dataSet={lineDs}
                highLightRow={false}
                columns={lineColumns}
              />
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.hmes.purchase.materialApproval', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({ ...TabelDS() });
      const lineDs = new DataSet({ ...LineDs() });
      return {
        tableDs,
        lineDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MaterialApprovalList),
);
