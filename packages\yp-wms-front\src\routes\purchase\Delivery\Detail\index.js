import React, { useEffect, useState } from 'react';
import { DataSet, Table, TextField, Form, DatePicker, Switch, Lov, Button } from 'choerodon-ui/pro';
import { Badge, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import notification from 'utils/notification';
import { headerTableDS, lineTableDS } from '../stores/DeliveryDetailDS';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.purchase.delivery';

const Order = props => {
  const {
    headerTableDs,
    lineTableDs,
    match: {
      path,
      params: { id },
    },
  } = props;

  const [hasPermissionFlag, setHasPermissionFlag] = useState(false);
  const [changeAll, setChangeAll] = useState(false);
  const [canEdit, setCanEdit] = useState(false);
  const [instructionDocStatus, setInstructionDocStatus] = useState('');

  useEffect(() => {
    setCanEdit(false);
    queryDeatil();
  }, [id]);

  const queryDeatil = () => {
    headerTableDs.setQueryParameter('instructionDocId', id);
    headerTableDs.query().then(res => {
      if (res?.rows?.deliveryDocLineList) {
        const permissionFlagList = [];
        res.rows.deliveryDocLineList.forEach(item => {
          if (item.permissionFlag !== 'Y') {
            permissionFlagList.push(item);
          }
          item.toLocatorRequiredFlag = res.rows.deliveryDoc.toLocatorRequiredFlag;
        });
        lineTableDs.loadData(res.rows.deliveryDocLineList);
        setHasPermissionFlag(permissionFlagList.length === 0);
      }
      if (res?.rows?.deliveryDoc) {
        setInstructionDocStatus(res.rows.deliveryDoc.instructionDocStatus);
      }
    });
  };

  const handleCancel = () => {
    queryDeatil();
    setCanEdit(false);
  };

  const handleSave = async () => {
    const headerData = headerTableDs.toData()[0];
    const lines = lineTableDs.toData();
    const validate = await lineTableDs.validate();
    const poIds = [];
    if (validate) {
      lines.forEach(item => {
        if (poIds.indexOf(item?.poHeaderId) === -1) {
          poIds.push(item.poHeaderId);
        }
      });

      return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-delivery-doc/save/ui`, {
        method: 'POST',
        body: {
          mtInstructionDocDTO2: headerData,
          instructionList: lines,
        },
      }).then(res => {
        if (res && res.success) {
          handleCancel();
        } else {
          notification.error({
            message: res && res.message,
          });
        }
      });
    }
  };

  const fromWarehouseFooter = (okBtn, cancelBtn) => {
    const allBtn = (
      <Button
        onClick={() => {
          setChangeAll(true);
          setTimeout(() => {
            okBtn.props.onClick();
          });
        }}
      >
        {intl.get(`${modelPrompt}.all.rows`).d('应用至所有行')}
      </Button>
    );
    return (
      <div>
        {allBtn}
        {cancelBtn}
        {okBtn}
      </div>
    );
  };

  const wareHouseChange = (record, type, value) => {
    if (changeAll) {
      lineTableDs.forEach(item => {
        item.set(type, value);
      });
      setChangeAll(false);
    }
  };

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'lineNumber',
      width: 100,
    },
    {
      name: 'identifyType',
      width: 120,
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        } if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'materialCode',
      width: 140,
    },
    {
      name: 'revisionCode',
      width: 100,
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'siteCode',
      width: 140,
    },
    {
      name: 'quantity',
      width: 100,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'instructionStatusDesc',
      width: 100,
    },
    {
      name: 'poNumber',
      width: 140,
    },
    {
      name: 'lineNum',
      width: 100,
    },
    {
      name: 'locator',
      width: 140,
      editor: record =>
        canEdit &&
        record.get('permissionFlag') === 'Y' &&
        record?.get('instructionStatus') === 'RELEASED' && (
          <Lov
            tableProps={{
              highLightRow: true,
            }}
            modalProps={{
              footer: (okBtn, cancelBtn, modal) => {
                return fromWarehouseFooter(okBtn, cancelBtn, modal, record);
              },
            }}
            onChange={value => {
              wareHouseChange(record, 'locator', value);
            }}
          />
        ),
      renderer: ({ record }) => record?.get('locatorCode'),
    },
    {
      name: 'urgentFlag',
      width: 100,
      align: 'center',
      editor: record =>
        canEdit &&
        record.get('permissionFlag') === 'Y' &&
        record?.get('instructionStatus') === 'RELEASED' && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      width: 140,
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
    },
    {
      name: 'soNum',
      width: 140,
    },
    {
      name: 'soLineNum',
      width: 100,
    },
    {
      name: 'demandTime',
      width: 140,
      align: 'center',
    },
  ];

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.purchaseDeliveryManagement`).d('送货单管理')}
        backPath="/hwms/purchase/delivery-management-new/list"
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <PermissionButton type="c7n-pro" icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </PermissionButton>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            disabled={instructionDocStatus !== 'RELEASED' || !hasPermissionFlag}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.basic`).d('基本属性')}
            key="basicInfo"
            dataSet={headerTableDs}
          >
            <Form
              disabled={!canEdit}
              dataSet={headerTableDs}
              columns={3}
              labelLayout="horizontal"
              labelWidth={110}
            >
              <TextField disabled name="instructionDocNum" />
              <TextField disabled name="instructionDocStatusDesc" />
              <TextField disabled name="instructionDocTypeDesc" />
              <TextField disabled name="supplierName" />
              <TextField disabled name="supplierSiteName" />
              <DatePicker name="expectedArrivalTime" mode="dateTime" />
              <TextField disabled name="realName" />
              <DatePicker disabled name="creationDate" mode="dateTime" />
              <TextField disabled name="sourceSystemDesc" />
              <TextField colSpan={2} name="remark" />
            </Form>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="location"
            dataSet={lineTableDs}
          >
            <Table
              loading={headerTableDs.status === 'loading'}
              dataSet={lineTableDs}
              highLightRow={false}
              columns={lineTableColumns}
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};
export default formatterCollections({
  code: ['tarzan.hmes.purchase.delivery', 'tarzan.common'],
})(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Order),
);
