import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hwms.delivery.reminder';
const tenantId = getCurrentOrganizationId();

const headerTableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  pageSize: 10,
  selection: 'multiple',
  dataKey: 'content',
  totalKey: 'totalElements',
  cacheSelection: false,
  primaryKey: 'instructionDocId',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-delivery-notices/list?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_REMINDER_LIST.QUERY,${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_REMINDER_LIST.HEAD`,
        method: 'GET',
      };
    },
  },
  queryFields: [
    {
      name: 'site',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      noCache: true,
      required: true,
      ignore: 'always',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'siteIdList',
      type: FieldType.string,
      bind: 'site.siteId',
    },
    {
      name: 'deliveryNoticenum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.deliveryNoticenum`).d('任务号'),
    },
    {
      name: 'statusList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialIdList',
      type: FieldType.number,
      bind: 'materialLov.materialId',
    },
    {
      name: 'fromLocatorIdObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.fromLocator`).d('来源仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: 'always',
      noCache: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'fromLocatorIdList',
      bind: 'fromLocatorIdObj.locatorId',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'deliveryNoticenum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.deliveryNoticenum`).d('任务号'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workStatus`).d('任务状态'),
    },
    {
      name: 'lineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'fromLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromLocator`).d('来源仓库'),
    },
    {
      name: 'toLocatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.targetLocator`).d('目标仓库'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'createdRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createBy`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.createTime`).d('创建时间'),
    },
    {
      name: 'lastUpdateRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedBy`).d('更新人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdatedTime`).d('更新时间'),
    },
  ],
  record: {
    dynamicProps: {
      // 关闭类型的单据不可选择
      selectable: record => !['CANCEL', 'CLOSED'].includes(record?.get('instructionDocStatus')),
    },
  },
});


export { headerTableDS };
