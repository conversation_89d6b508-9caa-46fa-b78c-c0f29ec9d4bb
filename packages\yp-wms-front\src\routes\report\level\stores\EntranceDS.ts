/**
 * @Description: 库存查询 - 入口页DS
 * @Author: <EMAIL>
 * @Date: 2022/7/6 15:39
 * @LastEditTime: 2022-11-21 13:37:27
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import {DataSetSelection} from "choerodon-ui/dataset/data-set/enum";

const modelPrompt = 'tarzan.inventory.query.model.query';
const tenantId = getCurrentOrganizationId();

const entranceDS = (): DataSetProps => ({
  autoQuery: true,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  paging: 'server',
  pageSize: 10,
  primaryKey: 'recordId',
  expandField: 'expand',
  queryFields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('领料单'),
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      noCache: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'material.materialId',
    },
    {
      name: 'supplier',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplier`).d('供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      noCache: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierId',
      type: FieldType.string,
      bind: 'supplier.supplierId',
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('推荐/指定批次'),
    },
    {
      name: 'jumpLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.jumpLot`).d('实际执行批次'),
    },
    {
      name: 'user',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
      lovCode: 'MT.USER.ORG',
      noCache: true,
      ignore: FieldIgnore.always,
      textField: 'realName',
      valueField: 'id',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'creationBy',
      type: FieldType.string,
      bind: 'user.id',
    },
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
      max: 'creationDateTo',
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
      min: 'creationDateFrom',
    },
  ],
  fields: [
    {
      name: 'uuid',
      type: FieldType.string,
    },
    {
      name: 'parentUuid',
      type: FieldType.string,
    },
    {
      name: 'expand',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('领料单创建时间'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('领料单'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('推荐/指定批次'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
    },
    {
      name: 'jumpLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.jumpLot`).d('实际执行批次'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批'),
    },
    {
      name: 'name',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.name`).d('执行人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('执行时间'),
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('跳批原因'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-jump-lot-record/list/ui`,
        method: 'GET',
      };
    },
  },
});

export { entranceDS };
