/**
 * @Description: 创建送货单抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-12-13 10:31:13
 * @LastEditTime: 2023-05-18 15:33:33
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { BASIC } from '@utils/config';
import { returnMaterialLotDS, receiveMaterialLotDS } from '../stores/MaterialLotDS';

const MaterialLotDrawer = props => {
  const { record, customizeTable } = props;
  const { instructionDocLineId, instructionDocType, instructionId, docTypeTag } = record;
  const returnMaterialLotDs = useMemo(() => {
    return new DataSet(returnMaterialLotDS());
  }, []);
  const receiveMaterialLotDs = useMemo(() => {
    return new DataSet(receiveMaterialLotDS());
  }, []);

  useEffect(() => {
    // instructionDocType=WO_REQUISITION_DOC或者LINEWARE_REQUISITION_DOC  领
    // instructionDocType=WO_RETURN_DOC或者LINEWARE_RETURN_DOC 退
    returnMaterialLotDs.queryParameter = {
      instructionId,
      instructionDocType,
    };
    receiveMaterialLotDs.queryParameter = {
      instructionId,
      instructionDocType,
    };
    if (docTypeTag === "PICK") {
      receiveMaterialLotDs.query();
    }
    if (docTypeTag === "RETURN") {
      returnMaterialLotDs.query();
    }
  }, [instructionDocLineId, instructionDocType, docTypeTag]);

  // 组件息表配置
  const receiveColumns = [
    {
      name: 'materialIdentification',
      width: 180,
    },
    {
      name: 'materialLotStatusDesc',
      width: 120,
    },
    {
      name: 'containerIdentification',
      width: 140,
    },
    {
      name: 'sumActualQty',
      width: 100,
      align: 'right',
    },
    {
      name: 'uomCode',
      width: 60,
    },
    {
      name: 'lot',
      width: 140,
    },
    {
      name: 'receiveLocatorCode',
      width: 80,
    },
    {
      name: 'receiveDate',
      width: 200,
      align: 'center',
    },
    {
      name: 'receiveBy',
      width: 80,
    },
    {
      name: 'signedLocatorCode',
      width: 80,
    },
    {
      name: 'signedDate',
      width: 200,
      align: 'center',
    },
    {
      name: 'signedBy',
      width: 120,
    },
    {
      name: 'supplierCode',
      width: 120,
    },
    {
      name: 'supplierName',
      width: 120,
    },
    {
      name: 'supplierLot',
      width: 120,
    },
  ];

  const returnColumns = [
    {
      name: 'materialIdentification',
      width: 180,
    },
    {
      name: 'materialLotStatusDesc',
      width: 120,
    },
    {
      name: 'containerIdentification',
      width: 80,
    },
    {
      name: 'sumActualQty',
      width: 160,
      align: 'right',
    },
    {
      name: 'uomCode',
      width: 60,
    },
    {
      name: 'lot',
      width: 80,
    },
    {
      name: 'returnLocatorCode',
      width: 80,
    },
    {
      name: 'returnDate',
      width: 200,
      align: 'center',
    },
    {
      name: 'returnName',
      width: 120,
    },
    {
      name: 'supplierCode',
      width: 120,
    },
    {
      name: 'supplierName',
      width: 120,
    },
    {
      name: 'supplierLot',
      width: 120,
    },
  ];

  if (
    docTypeTag === "PICK"
  ) {
    return customizeTable(
      {
        code: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_MATERIAL_LOT.QUERY`,
      },
      <Table dataSet={receiveMaterialLotDs} columns={receiveColumns} />,
    )
  }
  return customizeTable(
    {
      code: `${BASIC.CUSZ_CODE_BEFORE}.RECEIVE_RETURN_MATERIAL_LOT.QUERY`,
    },
    <Table dataSet={returnMaterialLotDs} columns={returnColumns} />,
  )
};

export default MaterialLotDrawer;
