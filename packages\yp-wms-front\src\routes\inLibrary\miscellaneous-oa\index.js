/**
 * 杂项工作台-入口文件
 * @date 2023-1-3
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState } from 'react';
import { DataSet, Table, Dropdown, Modal, Button } from 'choerodon-ui/pro';
import { Badge, Menu, Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
// import { Button as PermissionButton } from 'components/Permission';
import { Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
// import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
// import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
// import FRPrintButton from '@components/tarzan-ui/FRPrintButton';
import { BASIC } from '@utils/config';
// import notification from 'utils/notification';
import { headerTableDS, lineTableDS } from './stores/ListDS';
import styles from './index.module.less';

const { Panel } = Collapse;
// const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.receive.miscellaneous';
// let modalAssembly;
// const lugeUrl = '';

const Order = props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { params },
    customizeTable,
  } = props;
  // 判断头搜索条件切换
  const [siteId, setSiteId] = useState();
  // const [selectedStatus, setSelectedStatus] = useState(undefined);
  // const [printIds, setPrintIds] = useState([]); // 头表格选择的id

  // 头选中行instructionDocType
  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

   // 返回页面时恢复选中项和当前项状态
   useEffect(() => {
    if (params && params.code) {
      headerTableDs.setQueryParameter('instructionDocNum', params.code);
    }
    headerTableDs.query(props.headerTableDs.currentPage);
    handleLineTableChange({
      dataSet: headerTableDs,
    });
  }, []);

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag
        ? headerTableDs.queryDataSet.addEventListener
        : headerTableDs.queryDataSet.removeEventListener;
      handler.call(headerTableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
      handler.call(headerTableDs, 'batchSelect', handleLineTableChange);
      handler.call(headerTableDs, 'batchUnSelect', handleLineTableChange);
    }
  };

  // 头搜索条件切换清空供应商地点
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (data.siteId !== siteId) {
      setSiteId(data.siteId);
    }
    if (!data.instructionDocTypeObj) {
      record.set('instructionDocStatus', null);
    }
    if (!data.site) {
      record.set('warehouse', null);
    }
    if (!data.warehouse) {
      record.set('locator', null);
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    // 列表刷新清除头单选状态
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable();
    }
  };

  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    const _selectedStatus = [];
    const _printIds = [];
    const completedList = ['1_PROCESSING', '1_COMPLETED', '2_PROCESSING', 'COMPLETED'];
    dataSet.selected.forEach(item => {
      const instructionDocStatus = item?.data?.instructionDocStatus;
      _printIds.push(item?.data?.instructionDocId);
      if (completedList.indexOf(instructionDocStatus) > -1) {
        if (_selectedStatus.indexOf('COMPLETED') === -1) {
          _selectedStatus.push('COMPLETED');
        }
      } else if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
        _selectedStatus.push(instructionDocStatus);
      }
    });
    // setSelectedStatus(_selectedStatus.length === 1 ? _selectedStatus[0] : undefined);
    // setPrintIds(_printIds);
  };

  // 行列表数据查询
  const queryLineTable = data => {
    lineTableDs.setQueryParameter('instructionDocId', data?.instructionDocId);
    lineTableDs.setQueryParameter('instructionDocType', data?.instructionDocType);
    lineTableDs.setQueryParameter('workOrderId', data?.workOrderId);
    lineTableDs.setQueryParameter('prodLineId', data?.prodLineId);
    lineTableDs.setQueryParameter('materialId', data?.materialId);
    lineTableDs.setQueryParameter('revisionCode', data?.revisionCode);
    lineTableDs.query();
  };

  // // 操作列渲染
  // const optionRender = record => (
  //   <>
  //     <a
  //       style={{ marginRight: '8px' }}
  //       onClick={() => {
  //         handleMaterialLotDetail(record);
  //       }}
  //     >
  //       {intl.get(`${modelPrompt}.detail`).d('明细')}
  //     </a>
  //     <PermissionButton
  //       type="text"
  //       disabled={
  //         record.get('instructionStatus') !== 'RELEASED' || record.get('permissionFlag') !== 'Y'
  //       }
  //       onClick={() => handleCancel(record)}
  //       permissionList={[
  //         {
  //           code: `${path}.button.edit`,
  //           type: 'button',
  //           meaning: '列表页-编辑新建删除复制按钮',
  //         },
  //       ]}
  //     >
  //       {intl.get(`${modelPrompt}.line.button.cancel`).d('行取消')}
  //     </PermissionButton>
  //   </>
  // );

  // 组件信息
  // const handleMaterialLotDetail = async record => {
  //   const recordDetail = record?.toData() || {};
  //   modalAssembly = Modal.open({
  //     title:
  //       recordDetail.identifyType === 'MATERIAL_LOT'
  //         ? intl.get(`${modelPrompt}.materialLotDetail`).d('物料批明细')
  //         : intl.get(`${modelPrompt}.materialDetail`).d('物料明细'),
  //     maskClosable: true,
  //     destroyOnClose: true,
  //     drawer: true,
  //     closable: true,
  //     style: {
  //       width: 1080,
  //     },
  //     className: 'hmes-style-modal',
  //     children: (
  //       <MaterialLotDrawer
  //         record={{
  //           identifyType: recordDetail.identifyType,
  //           instructionId: recordDetail.instructionId,
  //           instructionDocType: headerTableDs.current.get('instructionDocType'),
  //         }}
  //         customizeTable={customizeTable}
  //       />
  //     ),
  //     footer: (
  //       <Button onClick={() => modalAssembly.close()}>
  //         {intl.get('tarzan.common.button.back').d('返回')}
  //       </Button>
  //     ),
  //   });
  // };

  // 行取消
  // const handleCancel = async record => {
  //   const _index = headerTableDs.currentIndex;
  //   return request(`${BASIC.HMES_BASIC}${lugeUrl}/v1/${tenantId}/miscellaneous/line/cancel/ui`, {
  //     method: 'POST',
  //     body: {
  //       instructionId: record.get('instructionId'),
  //       businessType: 'CANCEL',
  //     },
  //   }).then(res => {
  //     if (res?.success) {
  //       headerTableDs.query(props.headerTableDs.currentPage).then(() => {
  //         headerTableDs.locate(_index);
  //       });
  //       lineTableDs.query(props.lineTableDs.currentPage);
  //       notification.success({
  //         message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
  //       });
  //     } else {
  //       notification.error({
  //         message: res?.message,
  //       });
  //     }
  //   });
  // };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'instructionDocNum',
      width: 150,
      renderer: ({ record, value }) => {
        if (record.data.instructionDocStatus === 'RELEASED') {
          return (
            <a
              onClick={() => {
                props.history.push(
                  `/hwms/in-library/miscellaneous-new/detail/${record.data.instructionDocId}`,
                );
              }}
            >
              {value}
            </a>
          );
        }
        return value;
      },
      lock: 'left',
    },
    {
      name: 'instructionDocTypeDesc',
      width: 150,
    },
    {
      name: 'instructionDocStatusDesc',
      width: 80,
    },
    {
      name: 'siteCode',
      width: 120,
    },
    {
      name: 'accountTypeDesc',
      width: 80,
    },
    {
      name: 'costcenterCode',
      width: 150,
    },
    {
      name: 'costcenterDesc',
      width: 150,
    },
    {
      name: 'costcenterCategoryDesc',
      width: 100,
    },
    {
      name: 'internalOrderCode',
      width: 150,
    },
    {
      name: 'internalOrderDesc',
      width: 150,
    },
    {
      name: 'internalOrderCategoryDesc',
      width: 100,
    },
    {
      name: 'remark',
      width: 120,
    },
    {
      name: 'printTimes',
      width: 100,
    },
    {
      name: 'realName',
      width: 120,
    },
    {
      name: 'creationDate',
      align: 'center',
      width: 150,
    },
  ];

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'lineNumber',
      width: 60,
      lock: 'left',
    },
    {
      name: 'identifyType',
      width: 120,
      lock: 'left',
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        } if (value === 'MATERIAL_LOT' || value === '') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
      },
    },
    {
      name: 'materialCode',
      width: 140,
      lock: 'left',
    },
    {
      name: 'revisionCode',
      width: 80,
      lock: 'left',
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'quantity',
      width: 100,
    },
    {
      name: 'executedQty',
      width: 80,
    },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'instructionStatusDesc',
      width: 80,
    },
    {
      name: 'soNumber',
      width: 140,
    },
    {
      name: 'soLineNum',
      width: 100,
    },
    {
      name: 'warehouseCode',
      width: 140,
    },
    {
      name: 'locatorCode',
      width: 140,
    },
    {
      name: 'toleranceFlag',
      width: 80,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceTypeDesc',
      width: 100,
    },
    {
      name: 'toleranceMaxValue',
      width: 80,
    },
    {
      name: 'toleranceMinValue',
      width: 80,
    },
    {
      name: 'remark',
      width: 140,
    }
  ];

  const headerRowClick = record => {
    queryLineTable(record?.toData());
  };

  return (
    <div className="hmes-style">
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.HEAD`,
          },
          <Table
            searchCode="zxgzt1"
            customizedCode="zxgzt1"
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={headerTableDs}
            columns={headerTableColumns}
            highLightRow
            onRow={({ record }) => {
              return {
                onClick: () => {
                  headerRowClick(record);
                },
              };
            }}
          />,
        )}
        <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineTableDs}
          >
            {lineTableDs && (
              customizeTable(
                {
                  code: `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.LINE`,
                },
                <Table
                  customizedCode="zxgzt2"
                  className={styles['expand-table']}
                  dataSet={lineTableDs}
                  highLightRow={false}
                  columns={lineTableColumns}
                />,
              )
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_LIST.LINE`,
      `${BASIC.CUSZ_CODE_BEFORE}.MISCELLANEOUS_MATERIAL_LOT.QUERY`,
    ],
  }),
  formatterCollections({ code: ['tarzan.receive.miscellaneous', 'tarzan.common'] }),
)(Order);
