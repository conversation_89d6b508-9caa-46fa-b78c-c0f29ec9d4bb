/**
 * @Description: 物料维护-services
 * @Author: <<EMAIL>>
 * @Date: 2022-07-25 17:45:04
 * @LastEditTime: 2023-05-18 11:38:26
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 物料维护-详情页-保存消息
export function SaveMaterial() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/save/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DETAIL.BASIC`,
    method: 'POST',
  };
}

// 消息维护-复制抽屉-检验物料是否被使用
export function CheckMaterial() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/check/ui`,
    method: 'GET',
  };
}

// 消息维护-站点分配-保存物料站点信息
export function SaveMaterialSiteAssignment() {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/save/site/ui`,
    method: 'POST',
  };
}
