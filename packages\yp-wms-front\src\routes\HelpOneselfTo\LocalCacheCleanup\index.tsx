import React, {useState } from 'react';
import {Button} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import request from 'utils/request';
import notification from 'utils/notification';
import { flow } from 'lodash';
import { Content, Header } from 'components/Page';

const { Panel } = Collapse;
const modelPrompt = 'hwms.LocalCacheCleanup';

const LocalCacheCleanup = () => {

  const [commonCacheCleanupLoading, setCommonCacheCleanup] = useState<boolean>(false);
  const [modelCacheCleanupLoading, setModelCacheCleanup] = useState<boolean>(false);
  const [methodCacheCleanupLoading, setMethodCacheCleanup] = useState<boolean>(false);
  const [mesCacheCleanupLoading, setMesCacheCleanup] = useState<boolean>(false);


  const handleCommonCacheCleanup = async () => {
    setCommonCacheCleanup(true);
    return request('/mes/v1/mt-common-rpc/cache-clear/publish?cacheCategory=ALL', {
      method: 'POST',
    }).then(res => {
      setCommonCacheCleanup(false);
      if (!res.failed) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  }
  const handleModelCacheCleanup = async () => {
    setModelCacheCleanup(true);
    return request('/mes/v1/mt-model-rpc/cache-clear/publish?cacheCategory=ALL', {
      method: 'POST',
    }).then(res => {
      setModelCacheCleanup(false);
      if (!res.failed) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  }
  const handleMethodCacheCleanup = async () => {
    setMethodCacheCleanup(true);
    return request('/mes/v1/mt-method-rpc/cache-clear/publish?cacheCategory=ALL', {
      method: 'POST',
    }).then(res => {
      setMethodCacheCleanup(false);
      if (!res.failed) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  }
  const handleMesCacheCleanup = async () => {
    setMesCacheCleanup(true);
    return request('/mes/v1/mt-mes-rpc/cache-clear/publish?cacheCategory=ALL', {
      method: 'POST',
    }).then(res => {
      setMesCacheCleanup(false);
      if (!res.failed) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
      } else {
        notification.error({
          message: res?.message,
        });
      }
    });
  }


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.LocalCacheCleanup`).d('本地缓存清理')}>
      </Header>
      <Content>
        <Collapse
          bordered={false}
          defaultActiveKey={[
            'commonCache',
            'modelCache',
            'methodCache',
            'mesCache',
          ]}
        >
          <Panel
            header={intl.get(`${modelPrompt}.commonCache`).d('common缓存')}
            key="commonCache"
          >
            <Button loading={commonCacheCleanupLoading} onClick={() => handleCommonCacheCleanup()} color={ButtonColor.primary}>{intl.get(`${modelPrompt}.button.commonCacheCleanup`).d('common缓存清理')}</Button>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.modelCache`).d('model缓存')}
            key="modelCache"
          >
            <Button loading={modelCacheCleanupLoading} onClick={() => handleModelCacheCleanup()} color={ButtonColor.primary}>{intl.get(`${modelPrompt}.button.modelCacheCleanup`).d('model缓存清理')}</Button>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.methodCache`).d('method缓存')}
            key="methodCache"
          >
            <Button loading={methodCacheCleanupLoading} onClick={() => handleMethodCacheCleanup()} color={ButtonColor.primary}>{intl.get(`${modelPrompt}.button.methodCacheCleanup`).d('method缓存清理')}</Button>
          </Panel>
          <Panel
            header={intl.get(`${modelPrompt}.mesCache`).d('mes缓存')}
            key="mesCache"
          >
            <Button loading={mesCacheCleanupLoading} onClick={() => handleMesCacheCleanup()} color={ButtonColor.primary}>{intl.get(`${modelPrompt}.button.mesCacheCleanup`).d('mes缓存清理')}</Button>
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default flow(
  formatterCollections({ code: ['hwms.LocalCacheCleanup', 'tarzan.common'] }),
)(LocalCacheCleanup);
