/**
 * 批次变更-Form-DS
 * @date 2021-12-24
 * <AUTHOR> <<EMAIL>>
 * @version 0.0.1
 * @copyright Copyright (c) 2021, Hand
 */
import intl from 'utils/intl';

const modelPrompt = 'tarzan.mes.sluggishReport.form';
function getFormDsProps(params) {
  return {
    autoCreate: true,
    forceValidate: true,
    fields: [
      {
        name: 'reason',
        label: intl.get(`${modelPrompt}.selectReason`).d('选择原因'),
        lookupCode: 'WMS.INVENTORY_FREEZE_REASON',
        required: params,
      },
      {
        name: 'remark',
        type: 'string',
        label: intl.get(`${modelPrompt}.remark`).d('备注信息'),
        dynamicProps: {
          disabled: ({ record }) => {
            const data = record.get('reason');
            return  data !== 'OTHER';
          },
          required: ({ record }) => {
            const data = record.get('reason');
            return  data === 'OTHER';
          },
        },
      },
    ],
  };
}

export default getFormDsProps;
