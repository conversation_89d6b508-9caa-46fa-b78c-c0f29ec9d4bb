/**
 * 送货单提醒管理-入口文件
 */
import React, { useEffect, useState, useMemo } from 'react';
import { DataSet, Table, Dropdown, Menu } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
import { flow } from 'lodash';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import request from 'utils/request';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import withProps from 'utils/withProps';
// import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import ExcelExport from 'components/ExcelExport';
import { getCurrentOrganizationId, filterNullValueObject } from 'utils/utils';
import { BASIC, API_HOST } from '@utils/config';
import { headerTableDS } from './stores/DeliveryExceptionDetailsReportDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hwms.deliveryExceptionDetailsReport';

const deliveryExceptionDetailsReport = props => {
  const { headerTableDs } = props;

  const [selectedItem, setSelectedItem] = useState([]);
  const [editFlag, setEditFlag] = useState(false);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(headerTableDs, 'query', handleHeaderTableDsQuery);
      handler.call(headerTableDs, 'reset', handleHeaderTableDsQuery);
      handler.call(headerTableDs, 'select', handleHeaderTableDsSelect);
      handler.call(headerTableDs, 'batchSelect', handleHeaderTableDsSelect);
      handler.call(headerTableDs, 'unSelect', handleHeaderTableDsSelect);
      handler.call(headerTableDs, 'batchUnSelect', handleHeaderTableDsSelect);
    }
  };

  // 头单选按钮选中
  const handleHeaderTableDsSelect = () => {
    const selectedItemList = headerTableDs.selected;
    setSelectedItem(selectedItemList);
  };

  const handleHeaderTableDsQuery = () => {
    setSelectedItem([]);
  };

  // 头列表配置
  const headerTableColumns = useMemo(() => {
    return [
      {
        name: 'happenDate',
        width: 140,
        editor: record => record.getState('editing'),
      },
      {
        name: 'statusMeaning',
        width: 140,
      },
      {
        name: 'instructionDocNum',
        width: 100,
      },
      {
        name: 'poNumber',
        width: 100,
      },
      {
        name: 'supplierCdoe',
        width: 100,
      },
      {
        name: 'supplierName',
        width: 140,
      },
      {
        name: 'materialCode',
        width: 140,
      },
      {
        name: 'materialName',
        width: 140,
        align: 'center',
      },
      {
        name: 'problemType',
        width: 140,
        editor: record => record.getState('editing'),
      },
      {
        name: 'problemDescription',
        width: 100,
        editor: record => record.getState('editing'),
      },
      {
        name: 'affect',
        width: 140,
        editor: record => record.getState('editing'),
      },
      {
        name: 'downTime',
        width: 140,
        align: 'center',
        editor: record => record.getState('editing'),
      },
      {
        name: 'problemTerritory',
        width: 140,
        align: 'center',
        editor: record => record.getState('editing'),
      },
      {
        name: 'solutionTime',
        width: 140,
        align: 'center',
        editor: record => record.getState('editing'),
      },
      {
        name: 'creator',
        width: 140,
        align: 'center',
      },
      {
        name: 'creationDate',
        width: 140,
        align: 'center',
      },
      {
        name: 'lastUpdatedByName',
        width: 140,
        align: 'center',
      },
      {
        name: 'lastUpdateDate',
        width: 140,
        align: 'center',
      },
      {
        name: 'uuid',
        width: 140,
        align: 'center',
        lock: 'right',
      },
    ];
  }, [editFlag]);

  // 获取导出入参
  const getExportQueryParams = () => {
    return filterNullValueObject({
      ...headerTableDs.queryDataSet.current.toData()[0],
      payIds: selectedItem.map(item => {
        return item.data.payId;
      }),
    });
  };

  // 编辑
  const handleEdit = () => {
    selectedItem.forEach(record => {
      record.setState('editing', true);
    });
    setEditFlag(true);
  };

  // 状态变更
  const handleChangeStatus = type => {
    const arr = selectedItem
      .map(item => item.toData())
      .map(item => {
        return {
          ...item,
          status: type,
        };
      });
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-supplier-pay/update-status`, {
      method: 'PUT',
      body: arr,
    }).then(res => {
      if (res?.success) {
        notification.success();
        headerTableDs.query();
      }
    });
  };

  // 保存
  const handleSave = () => {
    return request(`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-pay/update`, {
      method: 'PUT',
      body: selectedItem.map(item => item.toData()),
    }).then(res => {
      if (res?.success) {
        notification.success();
        headerTableDs.query();
        selectedItem.forEach(record => {
          record.setState('editing', false);
        });
        setEditFlag(false);
      }
    });
  };

  // 取消
  const handleCancel = () => {
    selectedItem.forEach(record => {
      record.reset();
      record.setState('editing', false);
    });
    headerTableDs.unSelectAll();
    setEditFlag(false);
  };

  const menu = (
    <Menu style={{ width: '100px' }}>
      <Menu.Item>
        <a onClick={() => handleChangeStatus('HANDLE')}>
          {intl.get(`${modelPrompt}.handle`).d('处理中')}
        </a>
      </Menu.Item>
      <Menu.Item>
        <a onClick={() => handleChangeStatus('COMPLETED')}>
          {intl.get(`${modelPrompt}.completed`).d('完成')}
        </a>
      </Menu.Item>
      <Menu.Item>
        <a onClick={() => handleChangeStatus('CANCEL')}>
          {intl.get(`${modelPrompt}.cancel`).d('取消')}
        </a>
      </Menu.Item>
      <Menu.Item>
        <a onClick={() => handleChangeStatus('DELAY')}>
          {intl.get(`${modelPrompt}.delay`).d('延迟')}
        </a>
      </Menu.Item>
    </Menu>
  );

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get(`${modelPrompt}.title.deliveryExceptionDetailsReport`)
          .d('供应商交付异常明细报表')}
      >
        <ExcelExport
          method="GET"
          // exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-pay/abnormal-export`}
          queryParams={getExportQueryParams}
          buttonText="导出"
        />
        {editFlag ? (
          <>
            <PermissionButton type="c7n-pro" onClick={() => handleSave()}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <PermissionButton type="c7n-pro" onClick={() => handleCancel()}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </PermissionButton>
          </>
        ) : (
          <PermissionButton
            type="c7n-pro"
            onClick={() => handleEdit()}
            disabled={
              !selectedItem.length > 0 ||
              selectedItem.some(item => ['CANCEL', 'COMPLETED'].includes(item.get('status')))
            }
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
        <Dropdown
          overlay={menu}
          placement={Placements.bottomRight}
          disabled={
            !selectedItem.length > 0 ||
            selectedItem.some(item => ['CANCEL', 'COMPLETED'].includes(item.get('status')))
          }
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={
              !selectedItem.length > 0 ||
              selectedItem.some(item => ['CANCEL', 'COMPLETED'].includes(item.get('status')))
            }
          >
            {intl.get('tarzan.common.button.changeStatus').d('状态变更')}
          </PermissionButton>
        </Dropdown>
      </Header>
      <Content>
        <Table
          searchCode="deliveryExceptionDetailsReport"
          customizedCode="deliveryExceptionDetailsReport"
          dataSet={headerTableDs}
          columns={headerTableColumns}
          highLightRow
          queryBar="filterBar"
          queryFieldsLimit={5}
          queryBarProps={{
            fuzzyQuery: false,
          }}
        />
      </Content>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      return {
        headerTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  formatterCollections({ code: ['tarzan.hwms.deliveryExceptionDetailsReport', 'tarzan.common'] }),
)(deliveryExceptionDetailsReport);
