import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType, FieldIgnore, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.ass.externalWarehouseInventory';

const tenantId = getCurrentOrganizationId();

const historyPageFactory = () =>
  new DataSet({
    selection: DataSetSelection.multiple,
    paging: true,
    autoCreate: false,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    queryFields: [
      {
        name: 'supplierLov',
        type: FieldType.object,
        multiple: true,
        label: intl.get(`${modelPrompt}.supplier`).d('供应商编码'),
        lovCode: 'MT.MODEL.SUPPLIER',
        ignore: FieldIgnore.always,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'supplierIds',
        type: FieldType.string,
        bind: 'supplierLov.supplierId',
      },
      {
        name: 'materialLov',
        multiple: true,
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        lovCode: 'MT.METHOD.MATERIAL',
        ignore: FieldIgnore.always,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'materialIds',
        type: FieldType.string,
        bind: 'materialLov.materialId',
      },
      {
        name: 'warehouseCodes',
        lookupCode: 'WMS.OUTSIDE_WAREHOUSE',
        multiple: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.warehouseCode`).d('仓库编码'),
      },
    ],
    fields: [
      {
        name: 'supplierCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('供应商编码'),
        type: FieldType.string,
      },
      {
        name: 'supplierName',
        label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
        type: FieldType.string,
      },
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        type: FieldType.string,
      },
      {
        name: 'materialName',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        type: FieldType.string,
      },
      {
        name: 'warehouseCode',
        lookupCode: 'WMS.OUTSIDE_WAREHOUSE',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.warehouseCode`).d('仓库编码'),
      },
      {
        name: 'qualifiedQuantity',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.qualifiedQuantity`).d('合格数'),
      },
      {
        name: 'unQualifiedQuantity',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.unQualifiedQuantity`).d('不合格数'),
      },
      {
        name: 'lastUpdatedByName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
      },
      {
        name: 'lastUpdateDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          method: 'POST',
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-external-inventory-manage/list/ui`,
          // transformResponse: value => {
          //   let listData: any = {};
          //   try {
          //     listData = JSON.parse(value);
          //   } catch (err) {
          //     listData = {
          //       message: err,
          //     };
          //   }
          //   if (!listData.success) {
          //     return {
          //       ...listData,
          //       failed: true,
          //     };
          //   }
          //   return listData;
          // },
        };
      },
    },
  });

export default historyPageFactory;
