import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { DataSet } from "choerodon-ui/pro";

const modelPrompt = 'tarzan.hmes.inspection.inspection-management';
const tenantId = getCurrentOrganizationId();

const statusOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui`,
        method: 'GET',
        params: { statusGroup: 'INSPECT_REQUEST_STATUS', tenantId },
      };
    },
  },
});

const headerTableDS = () => ({
  autoQuery: true,
  autoCreate: false,
  cacheSelection: true,
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'inspectDocId',
  queryFields: [
    {
      name: 'inspectRequestCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestCode`).d('报检请求编码'),
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'inspectRequestStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestStatus`).d('状态'),
      options: statusOptionDs,
      textField: 'description',
      valueField: 'statusCode',
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单号'),
    },
    {
      name: 'disposalFunctionLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.disposalFunctionLov`).d('处置方法'),
      ignore: FieldIgnore.always,
      lovCode: 'WMS.INSPINF_DISPOSITION_FUNCTION',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'disposalFunction',
      bind: 'disposalFunctionLov.disposalFunction',
    },
    {
      name: 'disposalFunctionId',
      bind: 'disposalFunctionLov.dispositionFunctionId',
    },
    {
      name: 'businessObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
      lovCode: 'MT.COMMON.BUSINESS_TYPE',
      lovPara: {
        tenantId,
      },
      ignore: 'always',
    },
    {
      name: 'businessType',
      bind: 'businessObj.typeCode',
    },
    {
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
      name: 'inspectBusinessTypeObject',
      ignore: FieldIgnore.always,
      lovCode: 'MT.QMS.INSPECT_BUS_TYPE_RULE',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'inspectBusinessType',
      bind: 'inspectBusinessTypeObject.inspectBusinessType',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      ignore: FieldIgnore.always,
      multiple: true,
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      textField: 'materialCode',
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },

    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'sourceObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectType`).d('来源单据类型'),
      lookupCode: 'MT.SOURCE_OBJECT_TYPE',
    },
    {
      name: 'sourceObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectCode`).d('来源单据号'),
    },
    {
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('报检时间从'),
      name: 'inspectReqCreationDateFrom',
      type: FieldType.date,
      max: 'inspectReqCreationDateEnd',
    },
    {
      label: intl.get(`${modelPrompt}.creationDateTo`).d('报检时间至'),
      name: 'inspectReqCreationDateEnd',
      type: FieldType.date,
      min: 'inspectReqCreationDateFrom',
    },
    {
      name: 'inspectInfoUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.applicant`).d('报检人'),
      ignore: FieldIgnore.always,
      lovCode: 'MT.USER.ORG',
      textField: 'realName',
    },
    {
      name: 'inspectReqUserId',
      bind: 'inspectInfoUserLov.id',
    },
    {
      name: 'materialLot',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLot`).d('物料批'),
      ignore: FieldIgnore.always,
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MATERIAL_LOT`,
      textField: 'materialLotCode',
      multiple: true,
      lovPara: { tenantId },
    },
    {
      name: 'materialLotCodes',
      bind: 'materialLot.materialLotCode',
    },
  ],
  fields: [
    {
      name: 'inspectRequestCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestCode`).d('报检请求编码'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'businessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.businessType`).d('业务类型'),
    },
    {
      name: 'inspectBusinessTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectBusinessType`).d('检验业务类型'),
    },
    {
      name: 'inspectRequestStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectRequestStatus`).d('状态'),
    },
    {
      name: 'disposalFunctions',
      type: FieldType.string,
      multiple:true,
      label: intl.get(`${modelPrompt}.disposalFunction`).d('处置方法'),
    },
    {
      name: 'urgentFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.urgentFlag`).d('加急标识'),
      lookupCode: 'MT.FLAG_YN',
    },
    {
      name: 'quantity',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quantity`).d('报检数量'),
    },
    {
      name: 'uomName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomName`).d('单位'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'sourceObjectTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceObjectType`).d('来源单据类型'),
    },
    {
      name: 'sourceNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceNumber`).d('来源单据编码#行号'),
    },
    {
      name: 'sourceObjectLineCode',
      type: FieldType.string,
    },
    {
      name: 'inspectDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectDocNum`).d('检验单号'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('库位描述'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('场内批次'),
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    {
      name: 'inspectReqUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqUserName`).d('报检人'),
    },
    {
      name: 'inspectReqCreationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCreationDate`).d('报检时间'),
    },
    {
      name: 'okQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.okQty`).d('合格数'),
    },
    {
      name: 'ngQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngQty`).d('不合格数'),
    },
    {
      name: 'scrapQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.scrapQty`).d('报废数'),
    },
    {
      name: 'inspectorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectorName`).d('检验人'),
    },
    {
      name: 'inspectReqCompleteUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCompleteUserName`).d('处置完成人'),
    },
    {
      name: 'inspectReqCompleteDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCompleteDate`).d('处置完成时间'),
    },
    {
      name: 'inspectReqCancelUserName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCancelUserName`).d('报检取消人'),
    },
    {
      name: 'inspectReqCancelDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectReqCancelDate`).d('报检取消时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inspect-requests/list/ui`,
        method: 'GET',
      };
    },
  },
  // record: {
  //   dynamicProps: {
  //     // LAST_COMPLETED才可点击
  //     selectable: record => ['LAST_COMPLETED'].includes(record?.get('inspectRequestStatus')),
  //   },
  // },
});

export { headerTableDS };
