/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-06-28 15:09:45
 * @LastEditTime: 2023-06-30 17:07:55
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useCallback } from 'react';
import intl from 'utils/intl';
import { Table, Button, Lov } from 'choerodon-ui/pro';
import { Popconfirm } from 'choerodon-ui';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';

export default ({ ds, canEdit, kid, detailDs }) => {

  if (canEdit) {
    ds.forEach(item => {
      item.set('siteId', detailDs?.current?.get('siteId'));
    });
  }

  const handleAdd = useCallback(() => {
    const listData = ds.toData();
    let maxNumber = 0;
    listData.forEach(item => {
      const { lineNumber } = item as any;
      if (lineNumber) {
        if (lineNumber > maxNumber) {
          maxNumber = lineNumber;
        }
      }
    });
    ds.create(
      {
        flag: detailDs?.current?.toData()!.demandTypeObj.tag,
        siteId: detailDs?.current?.get('siteId'),
        lineNumber: parseInt(String(maxNumber / 10), 10) * 10 + 10,
      },
      0,
    );
  }, [ds]);


  const columns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <Button
            icon="add"
            disabled={!canEdit}
            funcType={FuncType.flat}
            onClick={handleAdd}
            size={Size.small}
          />
        ),
        align: ColumnAlign.center,
        width: 70,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              ds.remove(record);
            }}
          >
            <Button
              icon="remove"
              disabled={!canEdit || record!.get('instructionDocLineId')}
              funcType={FuncType.flat}
              size={Size.small}
            />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'lineNumber',
        align: ColumnAlign.left,
        width: 120,
        // renderer: ({ dataSet, record }) => {
        //   return (dataSet!.indexOf(record!) + 1) * 10;
        // },
      },
      {
        name: 'materialObjLov',
        width: 180,
        // renderer: ({ record }) => record!.get('materialCode'),
        editor: record =>
          canEdit && (
            <Lov
              name="materialObjLov"
              onChange={value => {
                handleChangeMaterial(record, value, 'material');
              }}
            />
          ),
      },
      {
        name: 'materialName',
        width: 180,
      },
      {
        name: 'supplierLov',
        width: 120,
        editor: record => canEdit  && (
          <Lov
            name="supplierLov"
            onChange={value => {
              handleChangeMaterial(record, value, 'supplier');
            }}
          />
        ),
      },
      {
        name: 'supplierName',
        width: 180,
      },
      {
        name: 'contractNumberObj',
        width: 120,
        editor: () => canEdit,
      },
      {
        name: 'attributeVarchar15',
        width: 120,
      },
      {
        name: 'brand',
        width: 120,
      },
      {
        name: 'model',
        width: 180,
      },
      {
        name: 'qty',
        width: 180,
        editor: () => canEdit,
      },
      {
        name: 'canRequestQty',
        width: 180,
      },
      {
        name: 'minPoQty',
      },
      {
        name: 'uomName',
        width: 180,
      },
      {
        name: 'demandTime',
        width: 180,
        editor: () => canEdit,
      },
      {
        name: 'unitPrice',
        width: 180,
      },
      {
        name: 'price',
        width: 180,
        renderer: ({ record, dataSet }) => {
          if (record!.get('qty') * record!.get('unitPrice')) {
            let linePrice = 0;
            dataSet?.toData()?.forEach(e => {
              const { qty, unitPrice } = e as any;
              const price = qty * unitPrice;
              if (price) {
                linePrice = price + linePrice;
              }
            });
            detailDs?.current?.set('totalPrice', linePrice);
            return record!.get('qty') * record!.get('unitPrice');
          }
        },
      },
      {
        name: 'applyNum',
        width: 180,
        editor: record => canEdit && !(record?.get('flag') === undefined),
      },
      {
        name: 'applyLineNum',
        width: 180,
        editor: record => canEdit && !(record?.get('flag') === undefined),
      },
      {
        name: 'remark',
        width: 180,
        editor: () => canEdit,
      },
    ],
    [canEdit, kid],
  );

  const handleChangeMaterial = (record, value, type) => {
    if (type === 'material') {
      record.set('supplierLov', {
        supplierName: value.supplierName,
        supplierCode: value.supplierCode,
        supplierId: value.supplierId,
      });
    }
    record.set('contractNumberObj', value);
    record.set('minPoQty', value.minPoQty);
    record.set('uomCode', value.uom);
    record.set('uomName', value.uomName);
  };

  return (
    <Table
      dataSet={ds}
      columns={columns}
      filter={record => {
        return record.status !== 'delete';
      }}
    />
  );
};
