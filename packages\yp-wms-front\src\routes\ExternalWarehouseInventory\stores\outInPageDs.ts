import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.ass.externalWarehouseInventory';

const tenantId = getCurrentOrganizationId();

const outInPageFactory = () =>
  new DataSet({
    selection: false,
    paging: false,
    autoCreate: false,
    autoQuery: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    forceValidate: true,
    fields: [
      {
        name: 'supplierCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('供应商编码'),
        type: FieldType.string,
      },
      {
        name: 'supplierName',
        label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
        type: FieldType.string,
      },
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        type: FieldType.string,
      },
      {
        name: 'materialName',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        type: FieldType.string,
      },
      {
        name: 'warehouseCode',
        lookupCode: 'WMS.OUTSIDE_WAREHOUSE',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.warehouseCode`).d('仓库编码'),
      },
      {
        name: 'qualifiedQuantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.form.qualifiedQuantity`).d('合格数'),
        min: 0,
        dynamicProps: {
          required: ({ record }) => {
            if (record&&record.get('unQualifiedQuantity')) {
              return false
            } else {
              return true
            }
          }
        }
      },
      {
        name: 'unQualifiedQuantity',
        min: 0,
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.form.unQualifiedQuantity`).d('不合格数'),
        dynamicProps: {
          required: ({ record }) => {
            if (record&&record.get('qualifiedQuantity')) {
              return false
            } else {
              return true
            }
          }
        }
      },
    ],
  });

export default outInPageFactory;
