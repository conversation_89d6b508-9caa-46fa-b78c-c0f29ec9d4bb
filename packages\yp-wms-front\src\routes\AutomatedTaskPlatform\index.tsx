import React, { useMemo, useCallback, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Record } from 'choerodon-ui/dataset';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { isNil } from 'lodash';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useDataSetEvent } from 'utils/hooks';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import ExcelExport from 'components/ExcelExport';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import { GetMethod } from './services';
import { headDS, lineDS } from './stores';

const modelPrompt = 'modelPrompt_code';
const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();

const AutomatedTaskPlatformList = observer((props) => {
  const {
    // match: { path },
    headDs,
    lineDs,
    // history,
    // location,
  } = props;

  const { run: getMethod } = useRequest(GetMethod(), { manual: true, needPromise: true }); // 手动查询，需要promise

  useDataSetEvent(headDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'siteLov':
        record?.init('materialLov', {});
        break;
      default:
        break;
    }
  });

  useDataSetEvent(headDs, 'load', ({ dataSet }) => {
    if (!dataSet.length) {
      // 查询出来没有数据
      lineDs.loadData([]);
      return;
    }
    makeLineTableQuery(dataSet.current);
  })

  useEffect(() => {
    // 获取默认站点
    request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/mt-mod-site/user-organization/site/lov/ui?page=0&size=10`, {
      method: 'GET',
      params: {
        tenantId,
      },
    }).then(res => {
      const response = getResponse(res)
      if(response){
        if(response.content.length === 1){
          headDs.queryDataSet?.current?.set('siteLov', {
            siteId: response.content[0].siteId,
            siteCode: response.content[0].siteCode,
            siteName: response.content[0].siteName,
          });
        }
        headDs.query(headDs.currentPage);
      }
    })
  }, [])

  const makeLineTableQuery = useCallback(
    (record: Record) => {
      lineDs.setQueryParameter('autoInstructionId', record.get('autoInstructionId'));
      lineDs.query();
    },
    [],
  )

  const headerRowClick = useCallback(
    (record) => {
      makeLineTableQuery(record)
    },
    [],
  )

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'autoInstructionNum' },
      { name: 'instructionDocNum' },
      { name: 'autoInstructionStatusDesc' },
      { name: 'quantity' },
      { name: 'actualQty' },
      { name: 'siteCode' },
      { name: 'fromWarehouse' },
      { name: 'toWarehouse' },
      { name: 'fromLocator' },
      { name: 'toLocator' },
      { name: 'containerTypeCode' },
      { name: 'materialCode' },
      { name: 'autoInstructionTypeDesc' },
      { name: 'toLocation' },
      { name: 'autoCode' },
      { name: 'errorCode' },
      { name: 'errorMessage' },
      { name: 'taskLevel' },
      { name: 'taskLot' },
      { name: 'priorty' },
      { name: 'createRealName' },
      { name: 'creationDate', width: 150 },
      { name: 'lastupdateRealName' },
      { name: 'lastUpdateDate', width: 150 },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'materialLotCode' },
      { name: 'actualQty' },
      { name: 'containerCode' },
      { name: 'lineFromLocatorCode' },
      { name: 'lineToLocatorCode' },
      {
        name: 'arrivalFlag',
        renderer: ({record}) => {
          const arrivalFlag = record?.get('arrivalFlag');
          return !arrivalFlag ? '' : arrivalFlag === 'Y' ? '是' : '否';
        },
      },
      { name: 'lineCreateRealname' },
      { name: 'lineCreateDate', width: 150 },
      { name: 'lineUpdateRealname' },
      { name: 'lastUpdateDate', width: 150 },
    ];
  }, []);

  const handleCancel = useCallback(
    () => {
      getMethod({
        params: {
          autoInstructionId: headDs.selected[0].get('autoInstructionId'),
        },
      }).then((res) => {
        if (res && res.success){
          headDs.query(headDs.currentPage);
        }
      })
    },
    [],
  )

  const getQueryParams = () => {
    if (!headDs.queryDataSet || !headDs.queryDataSet.current) {
      return {};
    }
    const queryParams = headDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach((i) => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    if(headDs.selected.length < 0) {
      return {
        ...queryParams,
      };
    }
    return {
      ...queryParams,
      autoInstructionIds: headDs.selected.map(item => item.get('autoInstructionId')),
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('自动化任务平台')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-auto-instructions/export`}
          queryParams={getQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        <Button
          color={ButtonColor.primary}
          disabled={headDs.selected.length !== 1}
          onClick={handleCancel}
        >
          取消出库
        </Button>
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryFieldsLimit={10}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          highLightRow
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
          dataSet={headDs}
          columns={headColumns}
          searchCode="page_searchCode"
          customizedCode="page_customizedCode"
        />
        <Collapse bordered={false} defaultActiveKey={['lineTable']}>
          <Panel
            key="lineTable"
            header={intl.get(`${modelPrompt}.title.lineTable`).d('行表格')}
          >
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              customizedCode="page_lineTable_customizedCode"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
})

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(AutomatedTaskPlatformList),
);
