/**
 * @Description: 采购退货平台
 * @Author: <<EMAIL>>
 * @Date: 2021-12-21 14:14:48
 * @LastEditTime: 2023-05-18 15:23:35
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { getCurrentSiteInfo } from '@utils/utils';
import moment from 'moment';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.purchase.materialApproval';

const tenantId = getCurrentOrganizationId();

const TabelDS = (): DataSetProps => ({
  autoQuery: false,
  pageSize: 10,
  selection: DataSetSelection.multiple,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-synthesise-demands/query/head/ui`,
        method: 'GET',
      };
    },
  },
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'instructionDocId',
  queryFields: [
    {
      name: 'processDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processDocNum`).d('流程单号'),
    },
  ],
  fields: [
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'processDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.processDocNum`).d('流程单号'),
    },
    {
      name: 'title',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.title`).d('标题'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('状态'),
      lookupCode: 'WMS.SYNTHESISE_DEMAND_STATUS',
    },
    {
      name: 'applyPersonName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyPersonName`).d('申请人'),
    },
    {
      name: 'applyUnitName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyUnitName`).d('申请部门'),
    },
    {
      name: 'applyTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyTime`).d('申请日期'),
    },
    {
      name: 'budgetPersonName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.budgetPersonName`).d('预算员'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`tarzan.common.warehouseCode`).d('仓库'),
    },
    {
      name: 'costCenterCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.costCenterCode`).d('成本中心'),
    },
    {
      name: 'centralizedUnit',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.centralizedUnit`).d('归口部门'),
      lookupCode: 'WMS.CENTRALIZED_UNIT',
    },
    {
      name: 'demandType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.demandType`).d('需求类型'),
      lookupCode: 'WMS.DEMAND_TYPE',
    },
    {
      name: 'applyReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyReason`).d('申请原因'),
    },
  ],
});

const LineDs = (): DataSetProps => ({
  autoQuery: false,
  pageSize: 10,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-synthesise-demands/query/line/ui`,
        method: 'GET',
      };
    },
  },
  primaryKey: 'instructionId',
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'contractNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contractNumber`).d('合同号'),
    },
    {
      name: 'brand',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.brand`).d('品牌'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('型号'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'demandTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.demandTime`).d('需求时间'),
    },
    {
      name: 'unitPrice',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.unitPrice`).d('未税单价'),
    },
    {
      name: 'price',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.price`).d('未税总价'),
    },
    {
      name: 'applyNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyNum`).d('财审单号'),
    },
    {
      name: 'applyLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.applyLineNum`).d('财审单行号'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

export { TabelDS, LineDs };
