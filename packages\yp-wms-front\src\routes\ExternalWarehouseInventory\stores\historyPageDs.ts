import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.ass.externalWarehouseInventory';

const tenantId = getCurrentOrganizationId();

const listPageFactory = () =>
  new DataSet({
    selection: false,
    paging: true,
    autoCreate: false,
    autoQuery: false,
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    forceValidate: true,
    fields: [
      {
        name: 'eventTypeCode',
        label: intl.get(`${modelPrompt}.eventTypeCode`).d('事件类型'),
        type: FieldType.string,
      },
      {
        name: 'eventTypeDesc',
        label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件描述'),
        type: FieldType.string,
      },
      {
        name: 'eventUserName',
        label: intl.get(`${modelPrompt}.eventUserName`).d('操作人'),
        type: FieldType.string,
      },
      {
        name: 'eventTime',
        label: intl.get(`${modelPrompt}.eventTime`).d('操作时间'),
        type: FieldType.string,
      },
      {
        name: 'supplierCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('供应商编码'),
        type: FieldType.string,
      },
      {
        name: 'supplierName',
        label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
        type: FieldType.string,
      },
      {
        name: 'materialCode',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
        type: FieldType.string,
      },
      {
        name: 'materialName',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
        type: FieldType.string,
      },
      {
        name: 'warehouseCode',
        lookupCode: 'WMS.OUTSIDE_WAREHOUSE',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.warehouseCode`).d('仓库编码'),
      },
      {
        name: 'inQualifiedQuantity',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.inQualifiedQuantity`).d('合格入库数'),
      },
      {
        name: 'inUnQualifiedQuantity',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.inUnQualifiedQuantity`).d('不合格入库数'),
      },
      {
        name: 'outQualifiedQuantity',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.outQualifiedQuantity`).d('合格出库数'),
      },
      {
        name: 'outUnQualifiedQuantity',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.outUnQualifiedQuantity`).d('不合格出库数'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        const { data } = config
        return {
          ...config,
          data: data.historyIds,
          method: 'POST',
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-external-inventory-manage/list/his`,
          transformResponse: value => {
            let listData: any = {};
            try {
              listData = JSON.parse(value);
            } catch (err) {
              listData = {
                message: err,
              };
            }
            if (!listData.success) {
              return {
                ...listData,
                failed: true,
              };
            }
            return listData;
          },
        };
      },
    },
  });

export default listPageFactory;
