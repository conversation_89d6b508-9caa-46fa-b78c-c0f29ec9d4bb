/**
 * @Description: 事件查询-事件影响对象抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-10-31 14:19:51
 * @LastEditTime: 2023-05-25 14:16:02
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useState, useMemo } from 'react';
import { Table, Tooltip, Button, } from 'choerodon-ui/pro';
import { Collapse, Popconfirm } from 'choerodon-ui';
import intl from 'utils/intl';
import uuid from 'uuid/v4';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
const Panel = Collapse.Panel;
const modelPrompt = 'tarzan.event.eventQuery.model.eventQuery';

type CollpaseItem = {
  columnNames: string[],
  dataValues: string[][],
  eventDetails: any,
  objectDescription: string,
  objectTypeCode: string,
  objectTypeId: number,
  sumTrxQty: number,
}

const EventDetailsDrawer = (props) => {
  const { dataSource } = props;

  const [collpaseActiveKeys, setCollpaseActiveKeys] = useState<string[]>([]);
  const [panelList, setPanelList] = useState<{ key: string, title: string, sumTrxQty: number }[]>([]);
  const [wholdTableObj, setWholdTableObj] = useState<any>({});

  useEffect(() => {
  }, []);
  const handleAdd = () => {
    console.log('add')
    dataSource.create({}, 0);
    dataSource.current.setState('editing', true);
  };
  // 新建表格后可删除
  const handleDelete = record => {
    dataSource.delete(record, false);
  };
  const bom = (value) => {
    console.log(value)
  }
  const createcolumns: ColumnProps[] = useMemo(() => {
    return [
      {

        header: (
          <Button icon="add" onClick={() => handleAdd()} funcType="flat" shape="circle" size="small" />
        ),
        align: 'center',
        name: 'name',
        width: 80,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => handleDelete(record)}
            okText={intl.get('tarzan.common.button.confirm').d('确认')}
            cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
          >
            <Button
              disabled={record.status !== 'add'}
              onClick={bom(record)}
              icon="remove"
              funcType="flat"
              shape="circle"
              size="small"
            />
          </Popconfirm>
        ),
        lock: 'left',
      },
      {
        name: 'materialLotCode',
        // width: 250,
        // editor: true,
      },
      {
        name: 'identification',
        // width: 250,
        // editor: true
      },
      {
        name: 'siteLov',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'materialLov',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name: 'wareHouseLov',
        // width: 100,
        editor: record => record.status === 'add',
      },
      {
        name:"primaryUomQty",
        editor: record => record.status === 'add',
      },
      {
        name:"produceArea",
        editor: record => record.status === 'add',
      },
      {
        name:"produceProcess",
        editor: record => record.status === 'add',
      },
    ];
  }, []);

  return (
    <Table
      columns={createcolumns}
      dataSet={dataSource}
    />
  );
}

export default EventDetailsDrawer;
