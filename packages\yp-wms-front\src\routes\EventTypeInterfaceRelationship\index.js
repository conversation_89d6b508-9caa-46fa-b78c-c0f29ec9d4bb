/*
 * @Description: 事务类型接口关系
 * @Author: YinWQ
 * @Date: 2023-07-19 09:42:16
 * @LastEditors: YinWQ
 * @LastEditTime: 2023-07-19 15:19:57
 */
import React, { useMemo, useCallback } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, Button, Spin } from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { Popconfirm } from 'choerodon-ui';
import notification from 'utils/notification';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { useRequest } from '@components/tarzan-hooks';
import MainTableDS from './stroe/MainTableDS';
import { DeleteItems, SaveItem } from './service';

const modelPrompt = 'tarzan.model.event.type.interface.relationship.table';

const DeleteBtn = observer(({ ds }) => {
  const selectedRows = ds.selected;

  const { run: runDelete, loading } = useRequest(DeleteItems(), { manual: true });

  const deleteMessage = () => {
    const delDataList = selectedRows
      .filter(item => item.get('eventTypeInterfaceRelId'))
      .map(item => item.get('eventTypeInterfaceRelId'));
    if (!delDataList.length) {
      ds.query();
      return;
    }
    runDelete({
      params: delDataList,
      onSuccess: () => {
        ds.query();
      },
    });
  };

  return (
    <Popconfirm
      title={intl
        .get(`${modelPrompt}.confirm.delete`, {
          count: selectedRows.length,
        })
        .d(`总计${selectedRows.length}条数据，是否确认删除?`)}
      onConfirm={deleteMessage}
      cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
      okText={intl.get('tarzan.common.button.confirm').d('确定')}
    >
      <Button loading={loading} icon="delete_black-o" disabled={!selectedRows.length}>
        {intl.get('tarzan.common.button.delete').d('删除')}
      </Button>
    </Popconfirm>
  );
});

const ErrorMessage = props => {
  const {
    match: { path },
  } = props;

  const mainTableDS = useMemo(() => new DataSet(MainTableDS()), []);
  const { run: runSave } = useRequest(SaveItem(), { manual: true, needPromise: true });

  const columns = useMemo(() => {
    return [
      {
        name: 'nameSpace',
        editor: record => record.getState('editing'),
      },
      {
        name: 'serverCode',
        editor: record => record.getState('editing'),
      },
      {
        name: 'interfaceCode',
        editor: record => record.getState('editing'),
      },
      {
        name: 'erpSystemType',
        editor: record => record.getState('editing'),
      },
      {
        name: 'transTypeCode',
        editor: record => record.getState('editing'),
      },
      {
        name: 'transcationTransferNum',
        editor: record => record.getState('editing'),
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ record }) => {
          if (record && record.getState('editing')) {
            return record.getState('loading') ? (
              <div style={{ zoom: 0.5 }}>
                <Spin spinning />
              </div>
            ) : (
              <>
                <a onClick={() => handleCancel(record)} style={{ marginRight: '0.1rem' }}>
                  {intl.get('tarzan.common.button.cancel').d('取消')}
                </a>
                <a onClick={() => handleSubmit(record)}>
                  {intl.get('tarzan.common.button.save').d('保存')}
                </a>
              </>
            );
          }
          return (
            <PermissionButton
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
              type="text"
              onClick={() => handleEdit(record)}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          );
        },
      },
    ];
  }, []);

  const hanleAdd = useCallback(() => {
    const newRow = mainTableDS.create({}, 0);
    newRow.setState('editing', true);
  }, [mainTableDS]);

  const handleEdit = record => {
    record.setState('editing', true);
  };

  const handleCancel = useCallback(
    record => {
      if (record.status === 'add') {
        mainTableDS.remove(record);
      } else {
        record.reset();
        record.setState('editing', false);
      }
    },
    [mainTableDS],
  );

  const handleSubmit = async record => {
    const validate = await record.validate()
    if (validate) {
      runSave({
        params: record.toData(),
        onSuccess: res => {
          record.setState('loading', false);
          record.init('operationLov', null);
          record.init('workcellLov', null);
          record.init({
            ...res,
          });
          record.setState('editing', false);
          record.status = 'sync';
          notification.success({});
          mainTableDS.query();
        },
      });
    } else {
      notification.warning({
        message: '请将数据填写完整后再保存',
      });
    }
  };

  const onFieldEnterDown = () => {
    mainTableDS.query(mainTableDS.currentPage);
  }

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get(`tarzan.model.event.type.interface.relationship.title`)
          .d('事务类型接口关系')}
      >
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={hanleAdd}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <DeleteBtn ds={mainTableDS} />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
            autoQuery: false,
            onFieldEnterDown,
          }}
          dataSet={mainTableDS}
          columns={columns}
          searchCode="EventTypeInterfaceRelationship"
          customizedCode="EventTypeInterfaceRelationship"
          // onRow={() => ({ style: { background: 'red' } })}
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.model.event.type.interface.relationship', 'tarzan.common'],
})(ErrorMessage);

