import React, { FC, useEffect, } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import ExcelExport from 'components/ExcelExport';
import { API_HOST, BASIC } from '@utils/config';
import { isNil } from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import listPageFactory from '../stores/listPageDs';

const tenantId = getCurrentOrganizationId()
interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
}

const modelPrompt = 'tarzan.ass.contractsMaterial';

const ListPageComponent: FC<ListPageProps> = ({ listDs, }) => {

  useEffect(() => {
    listDs.query();
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'brand',
    },
    {
      name: 'model',
    },
    {
      name: 'minPackQty',
    },
    {
      name: 'minPoQty',
    },
    {
      name: 'attributeVarchar15',
    },
    {
      name: 'shelfDate',
      width:150
    },
    {
      name: 'supplierCode',
    },
    {
      name: 'supplierName',
    },
    {
      name: 'contractNumber',
    },
    {
      name: 'lineNum',
    },
    {
      name: 'priceDateFrom',
      width:150
    },
    {
      name: 'priceDateTo',
      width:150
    },
    {
      name: 'qty',
    },
    {
      name: 'minStockQty',
    },
    {
      name: 'effectiveDays',
      width:150
    },
  ];

  const getExportQueryParams = () => {
    if (!listDs.queryDataSet || !listDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = listDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    // queryParmas.wareHouseIds=(queryParmas.wareHouseLov??[]).map(ele=>ele.locatorId).join(',')
    // queryParmas.locatorIds=(queryParmas.locatorLov??[]).map(ele=>ele.locatorId).join(',')
    queryParmas.contractLineIds = listDs.selected.map(item => item.get('contractLineId')).join(',')
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.contractsMaterial`).d('资材物料查询')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-contracts/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="contractsMaterial"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          queryFieldsLimit={5} // 头部显示的查询字段的数量
          searchCode="contractsMaterial" // 动态筛选条后端接口唯一编码
          customizedCode="contractsMaterial" // 个性化编码
        />
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    return {
      listDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.ass.contractsMaterial'],
})(ListPage);
