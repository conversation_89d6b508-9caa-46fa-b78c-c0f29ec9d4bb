import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { getCurrentSiteInfo } from '@utils/utils';

const modelPrompt = 'tarzan.hmes.purchase.freeze';
const tenantId = getCurrentOrganizationId();

// const BASIC = {
//   TARZAN_REPORT: '/yp-report-wms-44212',
// }

const listDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  primaryKey: 'id',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      required: true,
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        defaultValue: () => {
          const siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo };
          }
          return undefined;
        },
      },
    },
    {
      name: 'siteIds',
      multiple: true,
      bind: 'siteLov.siteId',
    },
    {
      name: 'areaLocatorLov',
      type: FieldType.object,
      multiple: true,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        locatorCategory: 'AREA',
      },
    },
    {
      name: 'descendantLocatorIds',
      multiple: true,
      bind: 'areaLocatorLov.locatorId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料编码'),
      ignore: FieldIgnore.always,
      multiple: true,
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      textField: 'materialCode',
      dynamicProps: {
        lovPara: ({ record }) => ({
          siteId: record.get('siteId'),
          tenantId,
        }),
      },
    },
    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'inventoryStatus',
      type: FieldType.string,
      multiple: true,
      label: intl.get(`${modelPrompt}.inventoryStatus`).d('库存状态'),
      lookupCode: 'WMS_NVENTORY_STATUS',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      multiple: true,
      label: intl.get(`${modelPrompt}.locatorLov`).d('货位'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      lovPara: {
        tenantId: getCurrentOrganizationId(),
        locatorCategory: ['INVENTORY'],
      },
      textField: 'locatorCode',
      valueField: 'locatorId',
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierLov`).d('供应商代码'),
      lovCode: 'MT.MODEL.SUPPLIER',
      noCache: true,
      multiple: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'supplierIds',
      bind: 'supplierLov.supplierId',
    },
  ],
  fields: [
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.siteCode`).d('站点'),
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.warehouseCode`).d('仓库'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.materialCode`).d('物料'),
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.materialDesc`).d('物料描述'),
    },
    {
      name: 'number',
      label: intl.get(`${modelPrompt}.table.number`).d('数量'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.status`).d('冻结/不良'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.supplierCode`).d('供应商代码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.supplierName`).d('供应商名称'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-inventory-freeze-poor/query-header`,
        method: 'GET',
        data: {
          ...data,
        },
      };
    },
  },
});

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [{ name: 'id' }],
  fields: [
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.locatorCode`).d('条码所在货位'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.materialCode`).d('物料编码'),
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.materialDesc`).d('物料描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.supplierCode`).d('供应商代码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.supplierName`).d('供应商名称'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.primaryUomQty`).d('数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.primaryUomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.lot`).d('批次'),
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.reason`).d('冻结/不良原因'),
    },
    {
      name: 'receipeTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.receipeTime`).d('收货时间'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdateByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.table.lastUpdateByName`).d('最后更新人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-inventory-freeze-poor/query-line`,
        method: 'GET',
      };
    },
  },
});

export { listDS, detailDS };
