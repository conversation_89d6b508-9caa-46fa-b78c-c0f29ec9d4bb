/**
 * @Description: 采购退货平台-详情页
 * @Author: <<EMAIL>>
 * @Date: 2023-06-28 15:02:35
 * @LastEditTime: 2023-06-30 17:49:06
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { observer } from 'mobx-react';
import {
  DataSet,
  Button,
  Form,
  Lov,
  TextField,
  Select,
} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { useDataSetEvent } from 'utils/hooks';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { TarzanSpin } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import DetailPageTable from './DetailPageTable';
import { detailDS, lineTableDS } from '../stores/DetailDS';
import {
  FetchInstructionRule,
  SaveInstructionReturnDoc,
} from '../services';

const { Panel } = Collapse;
const modelPrompt = 'tarzan.hmes.purchase.purchaseReturn';

const PurchaseDetailWms = observer((props) => {
  const {
    history,
    match: { path, params },
  } = props;
  const kid = params.id;

  const [canEdit, setCanEdit] = useState(false);
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const lineTableDs = useMemo(() => new DataSet(lineTableDS()), []);
  const { run: fetchInstructionRule, data: instructionRule } = useRequest(FetchInstructionRule(), { manual: true, needPromise: true, initialData: {} });
  const { run: saveInstructionReturnDoc, loading: saveLoading } = useRequest(SaveInstructionReturnDoc(), { manual: true, needPromise: true });

  useEffect(() => {
    if (kid === 'create') {
      // 新建时
      fetchInstructionRule({
        params: {
          instructionDocType: "PO_RETURN",
        },
      })
      setCanEdit(true);
      return;
    }
    // 编辑时
    detailDs.setQueryParameter('instructionDocId', kid);
    detailDs.query().then(res => {
      if (res && res.success) {
        lineTableDs.loadData(res.rows.lines || []);
        fetchInstructionRule({
          params: {
            instructionDocType: res.rows.instructionDocType,
          },
        })
        lineTableDs.setState('siteId', res.rows.siteId);
      } else {
        lineTableDs.loadData([]);
        notification.error({
          message: res.message || intl.get('hzero.common.notification.error').d('操作失败'),
        });
      }
    });
  }, [kid]);

  useDataSetEvent(detailDs, 'update', ({ name, record, value }) => {
    switch (name) {
      case 'instructionDocType':
        fetchInstructionRule({
          params: {
            instructionDocType: value,
          },
        })
        break;
      case 'siteLov':
        lineTableDs.setState('siteId', value ? value.siteId : '');
        lineTableDs.loadData([]);
        break;
      case 'supplierLov':
        record.init('supplierSiteLov', {});
        break;
      default:
        break;
    }
  });

  useEffect(() => {
    lineTableDs.setState('instructionRule', instructionRule);
  }, [instructionRule])

  const handleEdit = useCallback(() => {
    setCanEdit(true);
  }, []);

  const handleCancel = useCallback(
    () => {
      if (kid === 'create') {
        history.push('/hmes/purchase/purchase-return/list')
      } else {
        setCanEdit(false);
        detailDs.query().then(res => {
          if (res && res.success) {
            lineTableDs.loadData(res.rows.lines || []);
            fetchInstructionRule({
              params: {
                instructionDocType: res.rows.instructionDocType,
              },
            })
            lineTableDs.setState('siteId', res.rows.siteId);
          } else {
            notification.error({
              message: res.message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
        });
      }
    },
    [kid, detailDs, lineTableDs],
  );

  const handleSave = async () => {
    const validateFlag = await detailDs.validate();
    const tableValidateFlag = await lineTableDs.validate();
    if (!validateFlag || !tableValidateFlag) {
      return false;
    }
    if (!lineTableDs.length) {
      notification.error({ message: intl.get(`${modelPrompt}.noLine`).d('未创建单据行，请检查！') })
      return;
    }
    const saveParams = {
      purchaseReturnHeader: {
        ...detailDs!.current!.toData(),
        instructionDocId: kid === 'create' ? null : kid,
      },
      purchaseReturnLine: lineTableDs.toData(),
      // purchaseReturnLine: lineTableDs.toData().map((item, index) => {
      //   return {
      //     ...item,
      //     lineNumber: (index + 1) * 10,
      //   }
      // }),
    }
    const res = await saveInstructionReturnDoc({
      params: saveParams,
    });
    if (res && res.success) {
      notification.success({});
      setCanEdit(false);
      history.push(`/hwms/purchase/purchase-return/detail/${res.rows}`);
      return true;
    }
    return false;
  }

  return (
    <div className='hmes-style'>
      <TarzanSpin dataSet={detailDs} spinning={saveLoading}>
        <Header
          title={intl.get(`${modelPrompt}.title.detail`).d('采购退货单')}
          backPath='/hwms/purchase/purchase-return/list'
        >
          {canEdit ? (
            <>
              <Button
                color={ButtonColor.primary}
                icon="save"
                onClick={handleSave}
              >
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button
                icon="close"
                onClick={handleCancel}
              >
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          ) : (
            <PermissionButton
              type="c7n-pro"
              icon="edit-o"
              color={ButtonColor.primary}
              onClick={handleEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
        </Header>
        <Content>
          <Collapse
            bordered={false}
            defaultActiveKey={[
              'headInfo',
              'lineInfo',
            ]}
          >
            <Panel
              key="headInfo"
              header={intl.get(`${modelPrompt}.title.headInfo`).d('头信息')}
            >
              <Form dataSet={detailDs} columns={3} disabled={!canEdit} labelWidth={112}>
                <TextField name='instructionDocNum' disabled={kid !== 'create'} />
                <Select name='instructionDocType' disabled={kid !== 'create'} />
                <Select name='instructionDocStatus' disabled={kid !== 'create'} />
                <Lov name='siteLov' disabled={kid !== 'create'} />
                <Lov name='supplierLov' disabled={kid !== 'create'} />
                <Lov name='supplierSiteLov' disabled={kid !== 'create'} />
                <TextField name='remark' />
              </Form>
            </Panel>
            <Panel
              key="lineInfo"
              header={intl.get(`${modelPrompt}.title.lineInfo`).d('行信息')}
            >
              <DetailPageTable canEdit={detailDs.current!.get('siteId') && canEdit} ds={lineTableDs} kid={kid} />
            </Panel>
          </Collapse>
        </Content>
      </TarzanSpin>
    </div>
  )
});


export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(PurchaseDetailWms);
