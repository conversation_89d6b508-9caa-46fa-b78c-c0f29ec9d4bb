import { IConfig } from 'umi'; // ref: https://umijs.org/config/
import { join } from 'path';
import packages from './packages-config';

// 整体配置项参考umi配置 https://umijs.org/zh-CN/config
export default {
  hzeroUed: {
    //   dynamicLess: 'true',
  },
  webpack5: {}, // 必须开启
  // 配置主题，实际上是配 less 变量。
  theme: {
    'input-height-base': '28px',
    'btn-height-base': '28px',
    'font-size-base': '12px',
    'text-color': '#333',
    'border-radius-base': '2px',
    'primary-color': '#29BECE',
    'layout-header-height': '48px',
    'modal-mask-bg': 'rgba(0, 0, 0, 0.288)',
    'pagination-item-size': '26px',
    'form-item-margin-bottom': '14px',
    'icon-url': '/assets/hzero-ui/font_148784_v4ggb6wrjmkotj4i',
    // 'iconfont-css-prefix': 'c7n', // 设置 c7n 图标 class 前缀
  },
  // 是否启用按需加载
  dynamicImport: {
    loading: '@hzerojs/plugin-layout/browsers/components/PageLoading',
  },
  // 配置环境变量
  define: {
    'process.env': {
      APS_CUSZ_CODE_BEFORE: 'MT_APS',
      HMES_BASIC: '/wms',
      TARZAN_COMMON: '/tznc',
      HRPT_COMMON: '/hrpt',
      TARZAN_MODEL: '/tznm',
      TARZAN_REPORT: '/tznr-wms',
      TARZAN_METHOD: '/tznd',
      TARZAN_SAMPLING: '/tznq',
      TARZAN_HSPC: '/tzns',
      TARZAN_MONGO: '/tzng',
      ADDITIONAL: process.env.ADDITIONAL,
      API_HOST: 'http://************:30080',
      PLATFORM_VERSION: 'SAAS',
      CLIENT_ID: 'localhost',
      MULTIPLE_SKIN_ENABLE: true,
      REACT_APP_SC_DISABLE_SPEEDY: 'false',
      SKIP_TS_CHECK_IN_START: false,
      SKIP_ESLINT_CHECK_IN_START: false,
      PACKAGE_PUBLIC_URL: '',
      // aps相关变量
      POOL_QUERY: 'query',
      BASE_SERVER: '/aps',
      BASE_SERVERPURCHASE: '/aps-purchase',
      BASE_SERVERPLAN: '/aps-mltp',
      //后续aps:7.X的包下面三个环境变量可删除
      APS_COMMON: '/tznc',
      APS_METHOD: '/aps',
      APS_METHODTZND: '/tznd',
      CUSZ_CODE_BEFORE: 'YP_WMS_MT', // 个性化单元前缀
      LOV_CODE_BEFORE: 'YP_WMS', // lov值集前缀
    },
  },
  manifest: {
    basePath: '/',
  },
  presets: ['@hzerojs/preset-hzero'],
  plugins: [
    'hzero-front',
  ],
  alias: {
    components: 'hzero-front/lib/components',
    utils: 'hzero-front/lib/utils',
    services: 'hzero-front/lib/services',
    '@/assets': join(__dirname, '../src/assets'),
    '@config': join(__dirname, '../src/config'),
    '@hcbb': 'hcbb-front/lib',
    '@components': 'hcm-components-front/lib/components',
    '@services': 'hcm-components-front/lib/services',
    '@utils': 'hcm-components-front/lib/utils',
    '@assets': 'hcm-components-front/lib/assets',
  },
  hzeroMicro: {
    // commonMf: 'cache',
    modifyMfRemotesArrayConfig(remoteArr) {
      remoteArr.push('ali-table-fix-hd');
      if (process.env.ADDITIONAL !== 'true') {
        return remoteArr;
      }
      // c7n多版本的时候 ued的c7n也必须多版本 并且设置 mf-config
      const additionalArr = ['choerodon-ui', '@hzero-front-ui/c7n-ui'];

      return remoteArr.map(v => {
        if (additionalArr.includes(v)) {
          return [v, '145_hotfix'];
        }
        return v;
      });
    },
    modifyMfConfig(config, currentPackageName) {
      if (currentPackageName === 'hzero-front') {
        // eslint-disable-next-line no-restricted-syntax
        for (const k in config.exposes) {
          if (/\.\/lib\/routes\/MarketClient\/ServiceList\/SearchForm/.test(k)) {
            // eslint-disable-next-line no-param-reassign
            delete config.exposes[k];
          }
        }
        // eslint-disable-next-line no-param-reassign
        // config.exposes['./lib/components/Lov/index.less'] = join(
        //   config.exposes['./lib/components/Lov'],
        //   '/index.less',
        // );
        // config.exposes['./lib/utils/menuTab.js'] = config.exposes['./lib/utils/menuTab']
        // config.exposes['./lib/utils/menuTab.js'] = join(process.cwd(), 'lib/utils/menuTab');
      }
      return config;
    },
    // 子模块配置 在这里面配置后当执行build:ms的时候可以选择到对应的子模块
    packages,
    // mfDepPresets: ['mf-ali-table-fix-hd', '@hzerojs/mf-deps-preset'],
    // 配置公共模块，在打包的时候会预先编译公共模块
    common: [
      // {
      //   name: 'hcm-components-front',
      // },
    ],
    // 指定暴露出去的模块
    // mfExposes: {
    //   TestCom: '@/components/TestCom',
    // },
  },
  // 开启esbuild
  // esbuild: {},
  // 同umi routes https://umijs.org/zh-CN/config#routes
  routes: [],
} as IConfig;
