/**
 * @Description: 销单库存转移-入口页DS
 * @Author: <EMAIL>
 * @Date: 2022/1/13 10:29
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.inventory.soStockTransfer';
const tenantId = getCurrentOrganizationId();

const entranceDS = (): DataSetProps => ({
  autoQuery: false,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  primaryKey: 'materialLotId',
  autoLocateFirst: true,
  queryFields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MATERIAL_LOT`,
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialLotId',
      type: FieldType.number,
      bind: 'materialLotCode.materialLotId',
    },
    {
      name: 'materialCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialCode.materialId',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteObj.siteId',
    },
    {
      name: 'locatorObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'locatorObject.locatorId',
    },
    {
      name: 'currentContainerCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.CONTAINER`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'currentContainerId',
      type: FieldType.number,
      bind: 'currentContainerCode.containerId',
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=QUALITY_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
      textField: 'description',
      valueField: 'statusCode',
      lovPara: {
        tenantId,
      },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'soNumberObject',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.soNumber`).d('销单号'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.SO_NUMBER`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'soId',
      type: FieldType.number,
      bind: 'soNumberObject.soId',
    },
    {
      name: 'soLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNum`).d('销单行号'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('soNumberObject');
        },
      },
    },
  ],
  fields: [
    {
      name: 'materialLotId',
      type: FieldType.number,
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentContainerCode`).d('当前容器'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('主单位'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('主单位数量'),
    },
    {
      name: 'secondaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondaryUomCode`).d('辅单位'),
    },
    {
      name: 'secondaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondaryUomQty`).d('辅单位数量'),
    },
    {
      name: 'inSiteTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inStationTime`).d('入站时间'),
    },
    {
      name: 'inLocatorTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTime`).d('入库时间'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
    },
    {
      name: 'reservedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedFlag`).d('预留标识'),
    },
    {
      name: 'reservedObjectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectType`).d('预留对象类型'),
    },
    {
      name: 'reservedObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象编码'),
    },
    {
      name: 'eoNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业'),
    },
    {
      name: 'createReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdReason`).d('创建原因'),
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('冻结标识'),
    },
    {
      name: 'stockTakeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stockTakeFlag`).d('盘点停用标识'),
    },
    {
      name: 'soNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soNumber`).d('销单号'),
    },
    {
      name: 'soLineNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNum`).d('销单行号'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-so-inventory-transfer/query/for/ui`,
        method: 'GET',
      };
    },
  },
});

const drawerDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  selection: false,
  fields: [
    {
      name: 'targetSoNumberLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.targetSONumber`).d('目标销售订单/行'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.SO_LINE`,
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'targetSoNumber',
      type: FieldType.string,
      bind: 'targetSoNumberLov.soNumber',
    },
    {
      name: 'targetSoLineNumber',
      type: FieldType.string,
      bind: 'targetSoNumberLov.soLineNum',
    },
  ],
});

export { entranceDS, drawerDS };
