// 无价值处置管理平台单查询
import intl from 'utils/intl';
import DataSet from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { FieldType } from "choerodon-ui/pro/lib/data-set/enum";
import { searchCopy } from '@utils/utils';

const modelPrompt = 'tarzan.workshop.InOutBoundManagementPlatform';
const tenantId = getCurrentOrganizationId();

// const BASIC = {
//   HMES_BASIC: '/yp-mes-40066',
//   TARZAN_COMMON: '/tznc',
// };

// 头表格ds
const headTableDS = () => {
  return {
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: 'multiple',
    paging: true,
    autoQuery: false,
    queryDataSet: new DataSet({
      events: {
        update({ record, name, value }) {
          searchCopy(
            [
              'materialLotCodes',
            ],
            name,
            record,
            value,
          );
        }
      },
      fields: [
        {
          name: 'siteObj',
          type: 'object',
          label: intl.get(`${modelPrompt}.site`).d('站点'),
          lovCode: 'WMS.NOWORTH_SITE',
          ignore: 'always',
          required: true,
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'siteId',
          bind: 'siteObj.siteId',
        },
        {
          name: 'instructionDocNum',
          type: 'string',
          label: intl.get(`${modelPrompt}.instructionDocNum`).d('无价值处置单号'),
        },
        {
          name: 'creationDateFrom',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
          max: 'creationDateTo',
        },
        {
          name: 'creationDateTo',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
          min: 'creationDateFrom',
        },
        {
          name: 'instructionDocType',
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
          lookupCode: 'WMS.NOWORTH_PLATFORM_TYPE',
        },
        {
          name: 'instructionDocStatus',
          type: 'string',
          label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
          lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS`,
          textField: 'description',
          valueField: 'statusCode',
          noCache: true,
          lookupAxiosConfig: {
            transformResponse(data) {
              if (data instanceof Array) {
                return data;
              }
              const { rows } = JSON.parse(data);
              return rows;
            },
          },
        },
        {
          name: 'materialObj',
          type: 'object',
          label: intl.get(`${modelPrompt}.materialObj`).d('物料编码'),
          lovCode: 'MT.METHOD.MATERIAL',
          ignore: 'always',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'materialId',
          bind: 'materialObj.materialId',
        },
        {
          name: 'materialLotCodes',
          multiple: true,
          type: FieldType.string,
          label: intl.get(`${modelPrompt}.materialLotCodes`).d('物料批编码'),
        },
      ]
    }),
    fields: [
      {
        name: 'siteCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.siteCode`).d('工厂'),
      },
      {
        name: 'instructionDocNum',
        type: 'string',
        label: intl.get(`${modelPrompt}.instructionDocNum`).d('无价值处置单号'),
      },
      {
        name: 'instructionDocType',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
        lookupCode: 'WMS.NOWORTH_PLATFORM_TYPE',
      },
      {
        name: 'instructionDocStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.instructionDocStatusDesc`).d('单据状态'),
      },
      {
        name: 'supplierCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.supplier`).d('回收供应商'),
      },
      {
        name: 'licensePlateNumber',
        type: 'string',
        label: intl.get(`${modelPrompt}.licensePlateNumber`).d('车牌号'),
      },
      {
        name: 'weight',
        type: 'number',
        label: intl.get(`${modelPrompt}.weight`).d('毛重'),
      },
      {
        name: 'tare',
        type: 'number',
        label: intl.get(`${modelPrompt}.tare`).d('皮重'),
      },
      {
        name: 'netWeight',
        type: 'number',
        label: intl.get(`${modelPrompt}.netWeight`).d('净重'),
      },
      {
        name: 'containerWeight',
        type: 'number',
        label: intl.get(`${modelPrompt}.containerWeight`).d('包装容器重量'),
      },
      {
        name: 'repeatDifferRate',
        type: 'string',
        label: intl.get(`${modelPrompt}.repeatDifferRate`).d('重复差异率'),
      },
      {
        name: 'uuid',
        type: 'attachment',
        bucketName: 'wms-wjzck',
        label: intl.get(`${modelPrompt}.uuid`).d('附件'),
      },
      {
        name: 'realName',
        type: 'string',
        label: intl.get(`${modelPrompt}.realName`).d('创建人'),
      },
      {
        name: 'creationDate',
        type: 'string',
        label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      },
      {
        name: 'remark',
        type: 'string',
        label: intl.get(`${modelPrompt}.remark`).d('备注'),
      },
    ],
    transport: {
      read: ({ data }) => {
        data.materialLotCodes = data.materialLotCodes.join()
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-in-and-out/head/list/ui`,
          method: 'GET',
          data,
        };
      },
    },
  };
};

// 行表格ds
const lineTableDS = () => {
  return {
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'lineNumber',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
      },
      {
        name: 'lineStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.lineStatusDesc`).d('行状态'),
      },
      {
        name: 'materialCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      },
      {
        name: 'quantity',
        type: 'string',
        label: intl.get(`${modelPrompt}.quantity`).d('需执行数量'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'sumActualQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.sumActualQty`).d('执行数量'),
      },
      {
        name: 'fromLocatorCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.fromLocatorCode`).d('来源仓库'),
      },
      {
        name: 'toLocatorCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.toLocatorCode`).d('目标仓库'),
      },
      {
        name: 'option',
        type: 'string',
        label: intl.get(`${modelPrompt}.option`).d('操作'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-in-and-out/line/list/ui`,
          method: 'GET',
          data,
        };
      },
    },
  };
};

// 明细表格ds
const detailTableDS = () => {
  return {
    dataKey: 'rows.content',
    totalKey: 'rows.totalElements',
    selection: false,
    paging: true,
    autoQuery: false,
    fields: [
      {
        name: 'materialLotCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批标识'),
      },
      {
        name: 'materialLotStatusDesc',
        type: 'string',
        label: intl.get(`${modelPrompt}.materialLotStatusDesc`).d('物料批状态'),
      },
      {
        name: 'containerCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
      },
      {
        name: 'primaryUomQty',
        type: 'string',
        label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
      },
      {
        name: 'uomCode',
        type: 'string',
        label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      },
      {
        name: 'lot',
        type: 'string',
        label: intl.get(`${modelPrompt}.lot`).d('批次'),
      },
    ],
    transport: {
      read: ({ data }) => {
        return {
          url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-worthless-in-and-out/detail/list/ui`,
          method: 'GET',
          data,
        };
      },
    },
  };
};

const createNewOrderDS = () => ({
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  paging: false,
  autoQuery: false,
  fields: [
    {
      name: 'instructionDocNum',
      type: 'string',
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据编码'),
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
      lookupCode: 'WMS.NOWORTH_PLATFORM_TYPE',
    },
    {
      name: 'instructionDocStatus',
      type: 'string',
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=INSTRUCTION_DOC_STATUS`,
      textField: 'description',
      valueField: 'statusCode',
      noCache: true,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteLov',
      type: 'object',
      lovCode: 'WMS.NOWORTH_SITE',
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('disabled');
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'supplerLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.suppler`).d('回收供应商'),
      lovCode: 'MT.MODEL.SUPPLIER',
      noCache: true,
      ignore: 'always',
      lovPara: {
        tenantId,
      },
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('disabled');
        },
      },
    },
    {
      name: 'supplierId',
      bind: 'supplerLov.supplierId',
    },
    {
      name: 'supplierCode',
      bind: 'supplerLov.supplierCode',
    },
    {
      name: 'supplierName',
      bind: 'supplerLov.supplierName',
    },
    {
      name: 'licensePlateNumber',
      type: 'string',
      label: intl.get(`${modelPrompt}.licensePlateNumber`).d('车牌号'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('disabled');
        },
      },
    },
    {
      name: 'outLot',
      label: intl.get(`${modelPrompt}.outLot`).d('出库批次'),
      dynamicProps: {
        required: ({ record }) => {
          const base = record.getField('instructionDocType')
          const baseValue = record.get('instructionDocType')
          const obj = base.getLookupData(baseValue) || {}
          return obj.description === 'H'
        }
      }
    },
    {
      name: 'disposeLot',
      label: intl.get(`${modelPrompt}.disposeLot`).d('委外批次'),
      dynamicProps: {
        required: ({ record }) => {
          const base = record.getField('instructionDocType')
          const baseValue = record.get('instructionDocType')
          const obj = base.getLookupData(baseValue) || {}
          return obj.description === 'H'
        }
      }
    },
    {
      name: 'outTime',
      type: 'dateTime',
      label: intl.get(`${modelPrompt}.outTime`).d('出厂时间'),
      dynamicProps: {
        required: ({ record }) => {
          const base = record.getField('instructionDocType')
          const baseValue = record.get('instructionDocType')
          const obj = base.getLookupData(baseValue) || {}
          return obj.description === 'H'
        }
      }
    },
    {
      name: "contacts",
      label: intl.get(`${modelPrompt}.contacts`).d('物流承运商联系人'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('disabled');
        },
      },
    },
    {
      name: "contactInformation",
      label: intl.get(`${modelPrompt}.contactInformation`).d('物流承运商联系方式'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('disabled');
        },
      },
    },
    {
      name: 'remark',
      type: 'string',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

const createMaterialDS = () => ({
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: false,
  paging: false,
  autoQuery: false,
  fields: [
    {
      name: 'siteLov',
      type: 'object',
      lovCode: 'WMS.NOWORTH_SITE',
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
      label: intl.get(`${modelPrompt}.siteName`).d('站点描述'),
    },
    {
      name: 'materialLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: 'always',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialName',
      bind: 'materialLov.materialName',
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'locatorLov',
      type: 'object',
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      required: true,
      lovPara: {
        tenantId,
        locatorCategory: ['AREA'],
      },
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorName',
      bind: 'locatorLov.locatorName',
      label: intl.get(`${modelPrompt}.warehouseName`).d('仓库描述'),
    },
    {

      name: 'weight',
      required: true,
      type: 'number',
      label: intl.get(`${modelPrompt}.weight`).d('重量'),
      validator: (value) => { // 校验器 自定义校验规则对内容进行校验
        if (value <= 0) {
          return '重量需大于0';
        }
        return true;
      },
    },
    {
      name: 'number',
      required: true,
      type: 'number',
      label: intl.get(`${modelPrompt}.number`).d('个数'),
      validator: (value) => { // 校验器 自定义校验规则对内容进行校验
        if (!/^[1-9]\d*$/.test(value)) {
          return '个数需为正整数';
        }
        return true;
      },
    },
    {
      name: 'remark',
      type: 'string',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

const materialLotTableDS = () => ({
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  selection: 'multiple',
  paging: false,
  autoQuery: false,
  fields: [
    {
      name: 'materialLotCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.materialLotCode`).d('标签条码号'),
    },
    {
      name: 'siteCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.siteCode`).d('无价值工厂代码'),
    },
    {
      name: 'siteName',
      type: 'string',
      label: intl.get(`${modelPrompt}.siteName`).d('工厂名称'),
    },
    {
      name: 'locatorCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.locatorCode`).d('仓库代码'),
    },
    {
      name: 'materialCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.materialName`).d('废料SAP虚拟号'),
    },
    {
      name: 'materialName',
      type: 'string',
      label: intl.get(`${modelPrompt}.materialName`).d('废料名称'),
    },
    {
      name: 'weight',
      type: 'string',
      label: intl.get(`${modelPrompt}.weight`).d('称重重量'),
    },
    {
      name: 'uomCode',
      type: 'string',
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'creationDate',
      type: 'string',
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'remark',
      type: 'string',
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

const exportDS = () => ({
  fields: [{
    name: 'instructionDocNums',
    type: 'string',
    multiple: true,
    label: intl.get(`${modelPrompt}.instructionDocNum`).d('无价值处置单号'),
  }, {
    name: 'remark',
    type: 'string',
    label: intl.get(`${modelPrompt}.remark`).d('备注'),
  },]
})

export { exportDS, headTableDS, lineTableDS, detailTableDS, createNewOrderDS, createMaterialDS, materialLotTableDS };
