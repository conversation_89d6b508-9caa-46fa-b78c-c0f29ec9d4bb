/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-06-28 15:09:45
 * @LastEditTime: 2023-06-30 17:07:55
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useCallback } from 'react';
import intl from 'utils/intl';
import { Table, Button, NumberField, Lov, Select, Switch } from 'choerodon-ui/pro';
import { Popconfirm, Badge } from 'choerodon-ui';
import { FuncType } from 'choerodon-ui/pro/lib/button/enum';
import { Size } from 'choerodon-ui/pro/lib/core/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';

export default ({ ds, canEdit, kid }) => {

  const handleAdd = useCallback(
    () => {
      const listData = ds.toData();
      let maxNumber = 0;
      listData.forEach(item => {
        const { lineNumber } = item as any;
        if (lineNumber) {
          if (lineNumber > maxNumber) {
            maxNumber = lineNumber;
          }
        }
      });
      ds.create({
        lineNumber: parseInt(String(maxNumber / 10), 10) * 10 + 10,
      }, 0);
    },
    [ds],
  );

  const handleChangeMaterial = useCallback(
    (val) => {
      if (val) {
        ds.current!.init('revisionCode', val.currentRevisionCode);
      } else {
        ds.current!.init('revisionCode');
      }
    },
    [ds],
  )

  // 行上“允差标识”Switch变化的回调
  const handleToleranceChange = () => {
    ds.current!.init('toleranceType');
    ds.current!.init('toleranceMaxValue');
    ds.current!.init('toleranceMinValue');
  };

  // 行上“允差类型”Select变化的回调
  const handleToleranceTypeChange = () => {
    ds.current!.init('toleranceMaxValue');
    ds.current!.init('toleranceMinValue');
  };
  const columns: ColumnProps[] = useMemo(
    () => [
      {
        header: () => (
          <Button
            icon="add"
            disabled={!canEdit}
            funcType={FuncType.flat}
            onClick={handleAdd}
            size={Size.small}
          />
        ),
        align: ColumnAlign.center,
        width: 70,
        renderer: ({ record }) => (
          <Popconfirm
            title={intl.get(`hzero.common.message.confirm.delete`).d('是否确认删除?')}
            onConfirm={() => {
              ds.remove(record);
            }}
          >
            <Button icon="remove" disabled={!canEdit || record!.get('instructionDocLineId')} funcType={FuncType.flat} size={Size.small} />
          </Popconfirm>
        ),
        lock: ColumnLock.left,
      },
      {
        name: 'lineNumber',
        align: ColumnAlign.left,
        width: 120,
        // renderer: ({ dataSet, record }) => {
        //   return (dataSet!.indexOf(record!) + 1) * 10;
        // },
      },
      {
        name: 'materialLov',
        width: 180,
        renderer: ({ record }) => record!.get('materialCode'),
        editor: (record) => canEdit && !record!.get('instructionDocLineId') && <Lov name="materialLov" onChange={handleChangeMaterial} />,
      },
      {
        name: 'revisionCode',
        width: 120,
        editor: (record) => canEdit && !record!.get('instructionDocLineId') && record.get('revisionFlag') === 'Y' && <Select />,
      },
      {
        name: 'materialName',
        width: 180,
      },
      {
        name: 'needReturnQty',
        width: 120,
        editor: () => canEdit && <NumberField />,
      },
      {
        name: 'actualReturnQty',
        width: 120,
      },
      {
        name: 'uomCode',
        width: 180,
      },
      {
        name: 'fromWarehouseLov',
        width: 180,
        editor: () => canEdit && <Lov />,
      },
      {
        name: 'fromLocatorLov',
        width: 180,
        editor: () => canEdit && <Lov />,
      },
      {
        name: 'toleranceFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yse`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
        editor: (record) => canEdit && !record!.get('instructionDocLineId') && <Switch onChange={handleToleranceChange} />,
      },
      {
        name: 'toleranceType',
        width: 120,
        editor: (record) => canEdit && !record!.get('instructionDocLineId') && <Select onChange={handleToleranceTypeChange} />,
      },
      {
        name: 'toleranceMaxValue',
        align: ColumnAlign.right,
        width: 120,
        editor: (record) => canEdit && !record!.get('instructionDocLineId') && <NumberField />,
      },
      {
        name: 'toleranceMinValue',
        align: ColumnAlign.right,
        width: 120,
        editor: (record) => canEdit && !record!.get('instructionDocLineId') && <NumberField />,
      },
    ],
    [canEdit, kid],
  );

  return (
    <Table
      dataSet={ds}
      columns={columns}
      filter={record => {
        return record.status !== 'delete';
      }}
    />
  );
};
