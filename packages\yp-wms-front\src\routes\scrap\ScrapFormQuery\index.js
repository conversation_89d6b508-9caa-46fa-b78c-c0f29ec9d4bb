// 报废单查询
import React, { Component, Fragment } from 'react';
import { DataSet, Table, Modal, Button, Form, NumberField, TextField, Lov, TextArea} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { isNil,flow } from 'lodash';
import intl from 'utils/intl';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import ExcelExport from 'components/ExcelExport';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { API_HOST, BASIC } from '@utils/config';
import request from 'utils/request';
import notification from 'utils/notification';
import { headTableDS, lineTableDS, detailTableDS, createMaterialDS, materialLotTableDS } from './stores/ScrapFormQueryDS';

const tenantId = getCurrentOrganizationId();
const Panel = Collapse.Panel;
const modelPrompt = 'tarzan.hwms.ScrapFormQuery';
let modalAssembly;

@formatterCollections({ code: ['tarzan.hwms.ScrapFormQuery', 'tarzan.common'] })
export default class ScrapFormQuery extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.headTableDs = new DataSet(headTableDS()); // 头表格dateSet
    this.lineTableDs = new DataSet(lineTableDS()); // 行表格dateSet
    this.detailTableDs = new DataSet(detailTableDS()); // 明细表格dateSet
    this.createMaterialDs = new DataSet(createMaterialDS());
    this.materialLotTableDs = new DataSet(materialLotTableDS());
    this.materialLotColumns = [
      { name: 'materialLotCode', width: 180 },
      { name: 'siteCode', width: 130 },
      { name: 'siteName' },
      { name: 'locatorCode' },
      { name: 'materialCode', width: 120 },
      { name: 'materialName', width: 130 },
      { name: 'weight' },
      { name: 'uomCode' },
      { name: 'creationDate', width: 150 },
      { name: 'remark' },
    ];
  }

  // 查询行
  headerRowClick = record => {
    const instructionDocId = record.get('instructionDocId');
    this.lineTableDs.setQueryParameter('instructionDocId', instructionDocId);
    this.lineTableDs.query();
  };

  // 查询物料批明细
  handleMaterialLotDetail = record => {
    const instructionDocLineId = record.get('instructionDocLineId');
    this.detailTableDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
    this.detailTableDs.query();
    const detailColumns = [
      { name: 'materialLotCode', width: 150, align: 'center' },
      { name: 'materialLotStatusDesc', align: 'center' },
      { name: 'containerCode', align: 'center' },
      { name: 'primaryUomQty', align: 'center' },
      { name: 'uomCode', align: 'center' },
      { name: 'lot', align: 'center' },
      { name: 'scrapReason', align: 'center' },
    ];
    modalAssembly = Modal.open({
      title:intl.get(`${modelPrompt}.title.materialLot.detail`).d('物料批明细'),
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      className: 'hmes-style-modal',
      children: (
        <Table
          dataSet={this.detailTableDs}
          columns={detailColumns}
          customizedCode="detailScrapFormQuery"
        />
      ),
      footer: <Button onClick={() => modalAssembly.close()}>{intl.get(`${modelPrompt}.button.back`).d('返回')}</Button>,
    });
  };

  // 操作列渲染
  optionRender = record => (
    <>
      <a
        style={{ marginRight: '8px' }}
        onClick={() => {
          this.handleMaterialLotDetail(record);
        }}
      >
        {intl.get(`${modelPrompt}.materialLot.detail`).d('物料批明细')}
      </a>
    </>
  );

  handlePrintOrder = () => {
    if (this.headTableDs.selected.length <= 0) {
      return notification.warning({ description: '请勾选报废单进行打印！'});
    }
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wmsScapInstructionDoc/instruction/doc/print`, {
      method: 'POST',
      responseType: 'blob',
      body: this.headTableDs.selected.map(e => e.toData().instructionDocId),
    }).then(res => {
      if (res) {
        if (res.type === 'application/json') {
          const fileReader = new FileReader();
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result);
            // 普通对象，读取信息
            getResponse(jsonData);
          };
          fileReader.readAsText(res);
        } else {
          const file = new Blob([res], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(file);
          const newwindow = window.open(fileURL, 'newwindow');
          if (newwindow) {
            newwindow.print();
            notification.success({
              message: '打印成功',
            });
          } else {
            notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
          }
        }
      }
    })
  };

  getExportQueryParams = () => {
    if (!this.headTableDs.queryDataSet || !this.headTableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = this.headTableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  };

  handleFetchDefaultSite = () => {
    this.createMaterialDs.create({});
    return request(`${BASIC.TARZAN_MODEL}/v1/${tenantId}/lovs/sql/data`, {
      method: 'get',
      query: {
        lovCode: 'WMS.NOWORTH_SITE',
        tenantId: 0,
      },
    }).then(res => {
      if(res && res.content.length === 1) {
        this.createMaterialDs.current.set('siteLov', res.content[0]);
      }
    })
  };

  handleCreateMaterialLot = async () => {
    const flag = await this.createMaterialDs.validate();
    if(flag) {
      return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-scrap-docs/creating-worthless-barcodes`, {
        method: 'POST',
        body: this.createMaterialDs.current.toData(),
      }).then(res => {
        if(res && res.success) {
          res.rows.forEach(e => {
            this.materialLotTableDs.create(e, 0);
          })
          notification.success();
        } else {
          notification.warning({ description: res.message });
        }
      })
    }
  };

  handlePrint = () => {
    if (this.materialLotTableDs.selected.length <= 0) {
      return notification.warning({ description: intl.get(`${modelPrompt}.notification.materialLot.selected`).d('请勾选生成的条码进行打印！')});
    }
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-scrap-docs/print-worthless-barcodes`, {
      method: 'POST',
      responseType: 'blob',
      body: this.materialLotTableDs.selected.map(e => e.toData()),
    }).then(res => {
      if (res) {
        if (res.type === 'application/json') {
          const fileReader = new FileReader();
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result);
            // 普通对象，读取信息
            getResponse(jsonData);
          };
          fileReader.readAsText(res);
        } else {
          const file = new Blob([res], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(file);
          const newwindow = window.open(fileURL, 'newwindow');
          if (newwindow) {
            newwindow.print();
            notification.success({
              message: intl.get(`${modelPrompt}.notification.print.success`).d('打印成功'),
            });
          } else {
            notification.error({ message: intl.get(`${modelPrompt}.notification.setConfig.browser`).d('当前窗口已被浏览器拦截，请手动设置浏览器！') });
          }
        }
      }
    })
  };

  // handleCreate = () => {
  //   this.createMaterialDs.loadData([]);
  //   this.materialLotTableDs.loadData([]);
  //   this.handleFetchDefaultSite();
  //   this.createModal = Modal.open({
  //     key: Modal.key(),
  //     title: (
  //       <div>
  //         <span style={{ fontSize: '14px' }}>新建无价值标签</span>
  //         <div
  //           style={{
  //             float: 'right',
  //             display: 'flex',
  //             flexDirection: 'row-reverse',
  //             alignItems: 'center',
  //             marginRight: '0.3rem',
  //           }}
  //         >
  //           <Button icon="save" color="primary" onClick={this.handleCreateMaterialLot}>确定</Button>
  //         </div>
  //       </div>
  //     ),
  //     destroyOnClose: true,
  //     drawer: true,
  //     closable: true,
  //     keyboardClosable: true,
  //     style: {
  //       width: 1200,
  //     },
  //     footer: null,
  //     className: 'hmes-style-modal',
  //     onClose: () => {
  //       this.createMaterialDs.loadData([]);
  //       this.materialLotTableDs.loadData([]);
  //     },
  //     children: (
  //       <>
  //         <Form dataSet={this.createMaterialDs} columns={4}>
  //           <Lov name="siteLov" />
  //           <TextField name="siteName" disabled />
  //           <Lov name="materialLov" />
  //           <TextField name="materialName" disabled />
  //           <Lov name="locatorLov" />
  //           <TextField name="locatorName" disabled />
  //           <NumberField name="weight" />
  //           <NumberField name="number" />
  //           <TextArea colSpan={2} name="remark" />
  //         </Form>
  //         <Collapse defaultActiveKey={['1']}>
  //           <Panel header="行表格" key="1">
  //             <Table
  //               dataSet={this.materialLotTableDs}
  //               columns={this.materialLotColumns}
  //               buttons={[
  //                 <Button onClick={this.handlePrint}>打印</Button>,
  //               ]}
  //             />
  //           </Panel>
  //         </Collapse>
  //       </>
  //     ),
  //   });
  // };


  render() {
    const headColumns = [
      { name: 'siteCode', align: 'center' },
      { name: 'instructionDocNum', align: 'center' },
      { name: 'instructionDocStatusDesc', align: 'center' },
      { name: 'costcenterCode', align: 'center' },
      { name: 'realName', align: 'center' },
      { name: 'creationDate', align: 'center' },
    ];
    const lineColumns = [
      { name: 'lineNumber', align: 'center' },
      { name: 'lineStatusDesc', align: 'center' },
      { name: 'materialCode', align: 'center' },
      { name: 'materialName', align: 'center' },
      { name: 'quantity', align: 'center' },
      { name: 'uomCode', align: 'center' },
      { name: 'sumActualQty', align: 'center' },
      { name: 'fromLocatorCode', align: 'center' },
      { name: 'toLocatorCode', align: 'center' },
      {
        name: 'option',
        fixed: 'right',
        lock: 'right',
        width: 120,
        align: 'center',
        renderer: ({ record }) => this.optionRender(record),
      },
    ];
    return (
      <Fragment>
        <Header title={intl.get(`${modelPrompt}.title.parent`).d("报废单查询")}>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/wms-scrap-docs/export/ui`}
            queryParams={this.getExportQueryParams}
            buttonText={intl.get(`${modelPrompt}.button.export`).d('导出')}
          />
          {/* <Button icon="add" color="primary" onClick={this.handleCreate}>创建</Button> */}
          <Button icon="print" color="primary" onClick={this.handlePrintOrder}>打印</Button>
        </Header>
        <Content>
          <Table
            dataSet={this.headTableDs}
            columns={headColumns}
            queryBar="filterBar"
            queryBarProps={{
              fuzzyQuery: false,
            }}
            searchCode="headScrapFormQuery"
            customizedCode="headScrapFormQuery"
            onRow={({ record }) => {
              return {
                onClick: () => {
                  this.headerRowClick(record);
                },
              };
            }}
          />
          <Collapse defaultActiveKey={['1']}>
            <Panel header={intl.get(`${modelPrompt}.title.line`).d("行表格")} key="1">
              <Table
                dataSet={this.lineTableDs}
                columns={lineColumns}
                customizedCode="lineScrapFormQuery"
              />
            </Panel>
          </Collapse>
        </Content>
      </Fragment>
    );
  }
}
