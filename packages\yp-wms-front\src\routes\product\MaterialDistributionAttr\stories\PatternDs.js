/**
 * @feature 物料配送属性维护-配送模式选择
 * @date 2021-3-14
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.product.materialDistributionNew.model.materialDistributionNew';

/**
 * 配送模式明细
 */
const fixTimeDS = () => ({
  autoQuery: false,
  selection: false,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'distributionMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.distributionMode`).d('配送模式'),
      options: new DataSet({
        data: [
          { typeCode: 'IN_TURN', description: intl.get(`${modelPrompt}.IN_TURN`).d('顺序补货') },
          { typeCode: 'BY_WO', description: intl.get(`${modelPrompt}.BY_WO`).d('订单补货') },
        ],
      }),
      textField: 'description',
      valueField: 'typeCode',
    },
  ],
  transport: {},
});

const inTimeDS = () => ({
  autoQuery: false,
  selection: false,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'distributionMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.distributionMode`).d('配送模式'),
      options: new DataSet({
        data: [
          {
            typeCode: 'QUANTITATIVE',
            description: intl.get(`${modelPrompt}.QUANTITATIVE`).d('定量补货'),
          },
          { typeCode: 'TIMING', description: intl.get(`${modelPrompt}.TIMING`).d('定时补货') },
        ],
      }),
      textField: 'description',
      valueField: 'typeCode',
    },
  ],
  transport: {},
});

export { fixTimeDS, inTimeDS };
