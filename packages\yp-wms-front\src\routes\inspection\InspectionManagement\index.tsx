/**
 * 报检请求管理平台-入口文件
 * @date 2023-5-15
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState } from 'react';
import { Button, DataSet, Modal, Table } from 'choerodon-ui/pro';
import { Badge, Tag, Spin } from 'choerodon-ui';
import intl from 'utils/intl';
import myInstance from '@utils/myAxios';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import ExcelExport from 'components/ExcelExport';
import { flow } from 'lodash';
import { ColumnAlign, ColumnLock, TableQueryBarType } from "choerodon-ui/pro/lib/table/enum";
import { BASIC } from "hcm-components-front/lib/utils/config";
import notification from "utils/notification";
import { getCurrentOrganizationId } from "utils/utils";
import { headerTableDS } from './stores/ListDS';
import styles from './index.module.less';
import CreateMaterial from "./CreateMaterialDrawer";
import { useDataSetEvent } from 'utils/hooks';

const modelPrompt = 'tarzan.hmes.inspection.inspection-management';
const tenantId = getCurrentOrganizationId();

let modalMaterial;

const Order = props => {
  const {
    headerTableDs,
    match: { path },
  } = props;
  const [inspectRequestIds, setInspectRequestIds] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);;

  const [statusFlag, setStatusFlag] = useState(true)


  useDataSetEvent(headerTableDs, 'load', () => {
    if (headerTableDs.toData().length === 0) {
      setStatusFlag(true)
    }
  });

  useDataSetEvent(headerTableDs, 'select', () => {
    eventStatus()
  });

  useDataSetEvent(headerTableDs, 'selectAll', () => {
    eventStatus()
  });

  useDataSetEvent(headerTableDs, 'unSelectAll', () => {
    eventStatus()
  });

  useDataSetEvent(headerTableDs, 'unselect', () => {
    eventStatus()
  });

  const eventStatus = () => {
    if (headerTableDs.selected.length > 0) {
      const status = headerTableDs.selected.every(item => item.get('disposalFunctions').includes('退货'))
      setStatusFlag(!status)
    } else {
      setStatusFlag(true)
    }
  }
  // 头列表配置
  const tableColumns = [
    { name: 'inspectRequestCode', width: 150, lock: ColumnLock.left },
    { name: 'siteCode', width: 220 },
    { name: 'businessTypeDesc', width: 120 },
    { name: 'inspectBusinessTypeDesc', width: 120 },
    {
      name: 'inspectRequestStatusDesc',
      width: 120,
      renderer: ({ record }) => {
        if (record!.get('inspectRequestStatus') === 'NEW') {
          return <Tag color="blue">{intl.get(`${modelPrompt}.new`).d('新建')}</Tag>;
        }
        if (record!.get('inspectRequestStatus') === 'CANCEL') {
          return <Tag color="red">{intl.get(`${modelPrompt}.cancel`).d('取消')}</Tag>;
        }

        if (record!.get('inspectRequestStatus') === 'COMPLETED') {
          return <Tag color="green">{intl.get(`${modelPrompt}.complete`).d('完成')}</Tag>;
        }
        if (record!.get('inspectRequestStatus') === 'FIRST_COMPLETED') {
          return (
            <Tag color="yellow">{intl.get(`${modelPrompt}.firstComplete`).d('已初检完成')}</Tag>
          );
        }
        if (record!.get('inspectRequestStatus') === 'INSPECTING') {
          return (
            <Tag color="geekblue">{intl.get(`${modelPrompt}.inspecting`).d('检验中')}</Tag>
          );
        }
        if (record!.get('inspectRequestStatus') === 'REINSPECTING') {
          return <Tag color="orange">{intl.get(`${modelPrompt}.reinspecting`).d('复检中')}</Tag>;
        }
        if (record!.get('inspectRequestStatus') === 'LAST_COMPLETED') {
          return <Tag color="gray">{intl.get(`${modelPrompt}.lastCompleted`).d('检验完成')}</Tag>;
        }
        return <Tag color="pink">{record!.get('inspectRequestStatusDesc')}</Tag>;
      },
    },
    { name: 'disposalFunctions', width: 120 },
    {
      name: 'urgentFlag',
      align: ColumnAlign.center,
      renderer: ({ record }) => (
        <Badge
          status={record?.get('urgentFlag') === 'Y' ? 'success' : 'error'}
          text={
            record?.get('urgentFlag') === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    { name: 'quantity', align: ColumnAlign.right, width: 120 },
    { name: 'uomName', width: 120 },
    { name: 'materialCode', width: 120 },
    { name: 'materialName', width: 180 },
    { name: 'revisionCode', width: 120 },
    { name: 'sourceObjectTypeDesc', width: 130 },
    {
      name: 'sourceNumber', width: 200, align: ColumnAlign.left,
      renderer: ({ record }) => (
        <>
          {record.get('sourceObjectCode')}{record.get('sourceObjectLineCode') && '#'}{record.get('sourceObjectLineCode')}
        </>
      ),
    },
    {
      name: 'inspectDocNum',
      width: 200,
      renderer: ({ record }) => (
        <a onClick={() => handleToInspectDetail(record)}>{record!.get('inspectDocNum')}</a>
      ),
    },
    { name: 'locatorCode', width: 150 },
    { name: 'locatorName', width: 150 },
    { name: 'lot', width: 150 },
    { name: 'supplierLot', width: 150 },
    { name: 'inspectReqUserName', width: 150 },
    { name: 'inspectReqCreationDate', width: 150, align: ColumnAlign.center },
    { name: 'okQty', width: 150, align: ColumnAlign.right },
    { name: 'ngQty', width: 150, align: ColumnAlign.right },
    { name: 'scrapQty', width: 150, align: ColumnAlign.right },
    { name: 'inspectorName', width: 150 },
    { name: 'inspectReqCompleteUserName', width: 150 },
    { name: 'inspectReqCompleteDate', width: 150, align: ColumnAlign.center },
    { name: 'inspectReqCancelUserName', width: 150 },
    { name: 'inspectReqCancelDate', width: 150, align: ColumnAlign.center },
    {
      title: intl.get(`tarzan.common.label.action`).d('操作'),
      lock: ColumnLock.right,
      align: ColumnAlign.center,
      renderer: ({ record }) => (
        <PermissionButton
          type="text"
          permissionList={[
            {
              code: `list.table.inspectDetail`,
              type: 'button',
              meaning: '列表页-报检明细按钮',
            },
          ]}
          onClick={() => handleOpenDetailModal(record)}
        >
          {intl.get(`${modelPrompt}.detail`).d('报检明细')}
        </PermissionButton>
      ),
    },
  ];

  // useEffect(() => {
  //   listener(true);
  //   return function clean() {
  //     listener(false);
  //   };
  // });

  // const listener = flag => {
  //   // 列表交互监听
  //   if (headerTableDs) {
  //     const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
  //     // 查询条件更新时操作
  //     handler.call(headerTableDs, 'load', handleDataSetSelectUpdate);
  //     // 头选中和撤销选中事件
  //     handler.call(headerTableDs, 'select', handleDataSetSelectUpdate);
  //     handler.call(headerTableDs, 'unSelect', handleDataSetSelectUpdate);
  //     handler.call(headerTableDs, 'selectAll', handleDataSetSelectUpdate);
  //     handler.call(headerTableDs, 'unSelectAll', handleDataSetSelectUpdate);
  //   }
  // };

  // const handleDataSetSelectUpdate = () => {
  //   const _inspectRequestIdList = headerTableDs.selected.some((mom) => mom.get('disposalFunction') !== '退货') ? [] : headerTableDs.selected
  //     .filter((ele) => ele.get('disposalFunction') === '退货')
  //     .map((item) => item.get('inspectRequestId'));
  //   setInspectRequestIds(_inspectRequestIdList)
  // };

  const handleOpenDetailModal = (record) => {
    modalMaterial = Modal.open({
      title: intl.get(`${modelPrompt}.detail`).d('报检明细'),
      className: 'hmes-style-modal',
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      onClose: () => {
        modalMaterial.close();
      },
      style: {
        width: 1080,
      },
      children: (
        <CreateMaterial
          inspectRequestId={record.data.inspectRequestId}
        />
      ),
      footer: (
        <Button onClick={() => modalMaterial.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  }

  const handleToInspectDetail = record => {
    props.history.push(`/hwms/inspect-doc-maintain/dist/${record.get('inspectDocId')}`);
  };

  const disposal = () => {
    setLoading(true)
    const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inspect-requests/disposal/execute/ui`;
    myInstance
      .post(url, {
        inspectRequestIdList: headerTableDs.selected.map((item) => item.get('inspectRequestId')),
      })
      .then(res => {
        setLoading(false)
        if (res.data.success) {
          notification.success({});
          headerTableDs.query();
        } else if (res.data.message) {
          notification.error({
            description: res.data.message,
          });
        }
      });
  };

  const getQueryParams = () => {
    if (headerTableDs.selected.length > 0) {
      return {
        inspectRequestIds: headerTableDs.selected.map(ele => ele.get('inspectRequestId')).join(',')
      }
    }
    const searchData = headerTableDs.queryDataSet.current.toData()
    delete searchData['siteLov']
    delete searchData['disposalFunctionLov']
    delete searchData['businessObj']
    delete searchData['inspectBusinessTypeObject']
    delete searchData['materialLov']
    delete searchData['inspectInfoUserLov']
    delete searchData['materialLot']
    return {
      ...searchData,
    }
  };

  // @ts-ignore
  return (
    <div className="hmes-style">
      <Spin spinning={loading}>
        <Header title={intl.get(`${modelPrompt}.title.InspectionManagement`).d('报检请求管理平台')}>
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={statusFlag}
            onClick={() => disposal()}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.dispose`).d('处置')}
          </PermissionButton>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/mt-inspect-requests/export`}
            queryParams={getQueryParams}
            buttonText={intl.get(`${modelPrompt}.`).d('导出')}
          />
        </Header>
        <Content className={styles['deliver-content']}>
          <Table
            searchCode="bjqqgl1"
            dataSet={headerTableDs}
            columns={tableColumns as any}
            highLightRow
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
          />,
        </Content>

      </Spin>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      return {
        headerTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  formatterCollections({ code: ['tarzan.hmes.inspection.inspection-management', 'tarzan.common'] }),
)(Order);
