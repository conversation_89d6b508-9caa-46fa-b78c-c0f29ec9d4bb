/**
 * @Description: 创建物料批drawer
 * @Author: <EMAIL>
 * @Date: 2022/4/20 11:17
 * @LastEditTime: 2023-05-18 16:15:31
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect } from 'react';
import intl from 'utils/intl';
import { Collapse } from 'choerodon-ui';
import { DataSet, DateTimePicker, Form, NumberField, Table, TextField } from 'choerodon-ui/pro';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import styles from './index.module.less';

const { Panel } = Collapse;
interface DetailModalProps {
  headDs: DataSet;
  tableDs: DataSet;
  instructionDocLineId: number;
  batchList: number[];
  customizeTable: any;
}

const modelPrompt = 'tarzan.soDelivery.soDeliveryPlatform';

const CreateMaterial: React.FC<DetailModalProps> = ({
  headDs,
  tableDs,
  instructionDocLineId,
  batchList,
  customizeTable,
}) => {
  useEffect(() => {
    handleQuery();
  }, [headDs]);

  const handleQuery = async () => {
    tableDs.setQueryParameter('instructionDocLineId', instructionDocLineId);
    await tableDs.query();
  };

  // 物料的表格columns
  const columns: ColumnProps[] = [
    {
      name: 'identification',
      align: ColumnAlign.left,
      width: 150,
      renderer: ({ record, value }) => {
        const { materialLotId } = record?.toData();
        return (
          <div>
            {batchList.indexOf(materialLotId) > -1 && (
              <div className={styles['icon-new']}>
                <div className={styles['icon-new-inner']}>
                  <div className={styles['icon-new-inner-text']}>NEW</div>
                </div>
              </div>
            )}
            {value}
          </div>
        );
      },
    },
    {
      name: 'primaryUomQty',
      align: ColumnAlign.right,
      width: 80,
    },
    {
      name: 'primaryUomCode',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'materialLotStatusDesc',
      align: ColumnAlign.left,
      width: 100,
    },
    {
      name: 'productionDate',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'lot',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'printTimes',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'createdByName',
      align: ColumnAlign.left,
      width: 100,
    },
    {
      name: 'creationDate',
      align: ColumnAlign.center,
      width: 150,
    },
  ];

  const Div = props => {
    return (
      <div data-name={props.name} className={styles['line-left']}>
        <TextField name="instructionDocNum" style={{ width: '80%' }} disabled />
        <TextField
          style={{ width: `calc(100% - 80%)` }}
          name="lineNumber"
          className={styles['line-right']}
          disabled
        />
      </div>
    );
  };

  return (
    <div className="hmes-style">
      <Form dataSet={headDs} labelLayout={LabelLayout.horizontal} labelWidth={112} columns={2}>
        <Div name="instructionDocNum" />
        <TextField disabled name="materialCode" />
        <TextField disabled name="revisionCode" />
        <TextField disabled name="materialName" />
        <TextField disabled name="materialLotStatusDesc" />
        <TextField disabled name="primaryUomCode" />
        <TextField disabled name="orderedQuantity" />
        <TextField disabled name="createdQuantity" />
        <NumberField name="materialLotQty" />
        <NumberField name="materialSheets" precision={0} />
        <DateTimePicker name="productionDate" />
        <TextField name="lot" />
      </Form>
      <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.material.information`).d('物料批信息')}
          key="basicInfo"
          dataSet={tableDs}
        >
          {customizeTable(
            {
              code: `${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_MATERIAL_LOT.CREATE`,
            },
            <Table dataSet={tableDs} columns={columns} />,
          )}
        </Panel>
      </Collapse>
    </div>
  );
};
export default CreateMaterial;
