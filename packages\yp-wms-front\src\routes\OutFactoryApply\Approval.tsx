import React, { useEffect } from 'react';
import { Table, DataSet} from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { observer } from 'mobx-react';
import { Content } from 'components/Page';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { BASIC } from '@utils/config';

import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { tableDS, lineTableDS } from './store/ListDS';



const modelPrompt = 'hwms.outFactoryApply.field';

const { Panel } = Collapse;


const TablePage = observer(props => {
  const { lineTableDs, tableDs, customizeTable, match: { params: { instructionDocNum } } } = props;

  useEffect(() => {
    if(tableDs) {
      tableDs.setQueryParameter('instructionDocNum', instructionDocNum);
      tableDs.query();
      tableDs.addEventListener('load', handleResetHeaderQuery);
    }
    return () => {
      if(tableDs) {
        tableDs.removeEventListener('load', handleResetHeaderQuery);
      }
    }
  }, [instructionDocNum]);

  const handleResetHeaderQuery = ({ dataSet }) => {
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    }
  };

  /** @type {ColumnProps} 头列 */
  const headerTableColumns = [
    { name: 'siteCode' },
    { name: 'instructionDocNum' },
    { name: 'statusMeaning' },
    { name: 'applicationTypeMeaning' },
    { name: 'attribute1' },
    { name: 'supplierName' },
    { name: 'driver' },
    { name: 'licenseNum' },
    { name: 'factoryDate' },
    { name: 'applyUserName' },
    { name: 'applyDate' },
  ];

  const lineTableColumns = [
    { name: 'lineNum' },
    { name: 'factoryTypeMeaning' },
    { name: 'factoryCode' },
    { name: 'factoryDescription' },
    { name: 'factoryInstructionDocNum' },
    { name: 'factoryQty' },
    { name: 'factoryUomCode' },
    { name: 'remark' },
  ];

  const headerRowClick = (record) => {
    lineTableDs.setQueryParameter('instructionDocNum', record.get('instructionDocNum'));
    lineTableDs.query();
  };

  return (
    <Content>
      {customizeTable(
        {
          filterCode: `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.QUERY`,
          code: `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.HEAD`,
        },
        <Table
          searchCode="ltlgzt1"
          customizedCode="ltlgzt1"
          dataSet={tableDs}
          columns={headerTableColumns}
          highLightRow
          queryBar="none"
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
        />,
      )}
      <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
          key="basicInfo"
          dataSet={lineTableDs}
        >
          {lineTableDs && (
            customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.LINE`,
              },
              <Table
                customizedCode="ltlgzt2"
                dataSet={lineTableDs}
                highLightRow={false}
                columns={lineTableColumns}
              />,
            )
          )}

        </Panel>
      </Collapse>
    </Content>
  );
});
export default flow(
  withProps(
    () => {
      const tableDs = new DataSet({ ...tableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        tableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.OUT_FACTORY_APPLY_LIST.LINE`,
    ],
  }),
  formatterCollections({ code: ['hwms.outFactoryApply','hzero.common'] }),
)(TablePage);
