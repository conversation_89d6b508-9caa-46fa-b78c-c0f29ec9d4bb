/**
 * @Description: 供应商成品库存实时报表
 * @Author: <<EMAIL>>
 * @Date: 2023-11-21
 * @LastEditTime: 2023-11-21
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import {DataSetSelection, FieldIgnore, FieldType} from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

// const BASIC = {
//   TARZAN_REPORT: '/yp-report-wms-40066',
// }

const modelPrompt = 'tarzan.wms.inventoryRealTimeReport.model';
const tenantId = getCurrentOrganizationId();

const TableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'ifaceId',
  autoLocateFirst: true,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-supplier-inventory-realTime/list/get/ui`,
        method: 'GET',
      };
    },
  },
  fields: [
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'locatorDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouse`).d('仓库'),
    },
    {
      name: 'okQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.okQty`).d("良品库存数量"),
    },
    {
      name: 'ngQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngQty`).d('不良品库存数量'),
    },
    {
      name: 'supplierDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierDate`).d('时间'),
    },
  ],
  queryFields: [
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierDesc`).d('供应商描述'),
      lovCode: 'MT.MODEL.SUPPLIER',
      multiple: ',',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierCodes',
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      multiple: ',',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialCodes',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'locator',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouse`).d('仓库'),
      lookupCode: 'WMS.SUPPLIER_LOCATOR',
      valueField: 'value',
      textField: 'meaning',
    },
  ],
});

export { TableDS };
