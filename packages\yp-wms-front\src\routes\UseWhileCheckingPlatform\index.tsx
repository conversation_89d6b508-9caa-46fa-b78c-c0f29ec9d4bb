import React, { useMemo, useCallback, useEffect } from 'react';
import { observer } from 'mobx-react';
import { isNil } from 'lodash';
import { Table, DataSet, Modal } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import ExcelExport from 'components/ExcelExport';
import { Record } from 'choerodon-ui/dataset';
import { getCurrentOrganizationId } from 'utils/utils';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { useDataSetEvent } from 'utils/hooks';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@utils/config';
import { headDS, lineDS, materialDetailDS } from './stores';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'useWhileCheckingPlatform';
const { Panel } = Collapse;

const UseWhileCheckingPlatformList = observer((props) => {
  const {
    headDs,
    lineDs,
  } = props;

  const materialDetailDs = useMemo(() => new DataSet(materialDetailDS()), []);

  useDataSetEvent(headDs, 'load', ({ dataSet }) => {
    if (!dataSet.length) {
      // 查询出来没有数据
      lineDs.loadData([]);
      return;
    }
    makeLineTableQuery(dataSet.current);
  })

  useEffect(() => {
    headDs.query(headDs.currentPage);
  }, [])

  const makeLineTableQuery = useCallback(
    (record: Record) => {
      lineDs.setQueryParameter('docId', record.get('docId'));
      lineDs.query();
    },
    [],
  )

  const headerRowClick = useCallback(
    (record) => {
      makeLineTableQuery(record)
    },
    [],
  )

  // 物料批明细
  const handlePreviewMaterialDetail = record => {
    materialDetailDs.setQueryParameter('materialLotCode', record.get('materialLotCode'));
    materialDetailDs.setQueryParameter('docId', headDs.current.get('docId'));
    materialDetailDs.query();
    Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.singlePieceCode.drawer`).d('单件码'),
      className: 'hmes-style-modal',
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 400,
      },
      okButton: false,
      children: (
        <Table
          dataSet={materialDetailDs}
          columns={[{ name: 'singlePieceCode' }]}
        />
      ),
    });
  };

  const headColumns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'useWhileCheckingDocNum' },
      { name: 'useWhileCheckingDocStatus' },
      { name: 'siteCode' },
      { name: 'createByName' },
      { name: 'creationReason' },
      { name: 'creationDate', align: ColumnAlign.center },
      { name: 'lastUpdateDate', align: ColumnAlign.center },
    ];
  }, []);

  const lineColumns: ColumnProps[] = useMemo(() => {
    return [
      { name: 'materialLotCode', width: 200, lock: ColumnLock.left },
      { name: 'materialCode', width: 200 },
      { name: 'materialName', width: 200 },
      { name: 'supplierCode', width: 160 },
      { name: 'supplierName', width: 160 },
      { name: 'quantity', width: 160, align: ColumnAlign.right },
      { name: 'uomCode', width: 160 },
      { name: 'singlePieceQty', width: 160, align: ColumnAlign.right },
      { name: 'lot', width: 160 },
      { name: 'qualityStatus', width: 160 },
      { name: 'containerCode', width: 160 },
      { name: 'warehouseCode', width: 160 },
      { name: 'locatorCode', width: 160 },
      { name: 'productionDate', width: 150, align: ColumnAlign.center },
      { name: 'expirationDate', width: 150, align: ColumnAlign.center },
      { name: 'inLocatorTime', width: 150, align: ColumnAlign.center },
      {
        name: 'poLineId',
        lock: ColumnLock.right,
        width: 180,
        align: ColumnAlign.center,
        title: intl.get(`${modelPrompt}.option`).d('操作'),
        renderer: ({ record }) => (
          <>
            <a
              style={{ marginLeft: '12px' }}
              onClick={() => {
                handlePreviewMaterialDetail(record);
              }}
            >
              {intl.get(`${modelPrompt}.details`).d('明细')}
            </a>
          </>
        ),
      },
    ];
  }, [headDs]);

  const getExportQueryParams = () => {
    if (headDs.selected.length) {
      const data = headDs.selected.map(ele => ele.get('docId')) || []
      return {
        docIds: data.join(','),
      }
    }
    if (!headDs.queryDataSet || !headDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = headDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    queryParmas.materialCode = (queryParmas.materialCode || []).join(',');
    queryParmas.supplierCode = (queryParmas.supplierCode || []).join(',');
    queryParmas.lot = (queryParmas.lot || []).join(',');
    queryParmas.wareHouseCode = (queryParmas.wareHouseCode || []).join(',');
    queryParmas.locatorCode = (queryParmas.locatorCode || []).join(',');
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('边检边用申请管理平台')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/wms-use-while-checking-docs/export`}
          queryParams={getExportQueryParams}
        />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          onRow={({ record }) => ({
            onClick: () => headerRowClick(record),
          })}
          dataSet={headDs}
          columns={headColumns}
          queryFieldsLimit={10}
          searchCode="useWhileCheckingPlatform"
          customizedCode="useWhileCheckingPlatform"
        />
        <Collapse bordered={false} defaultActiveKey={['lineTable']}>
          <Panel
            key="lineTable"
            header={intl.get(`${modelPrompt}.title.lineTable`).d('行表格')}
          >
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              customizedCode="useWhileCheckingPlatformLine"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
})

export default formatterCollections({
  code: [modelPrompt, 'tarzan.common'],
})(
  withProps(
    () => {
      const headDs = new DataSet(headDS());
      const lineDs = new DataSet(lineDS());
      return {
        headDs,
        lineDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(UseWhileCheckingPlatformList),
);
