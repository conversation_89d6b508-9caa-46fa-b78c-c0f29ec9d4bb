/**
 * 物料批多行创建抽屉
 * @date 2023-1-3
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState } from 'react';
import intl from 'utils/intl';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Collapse, Breadcrumb } from 'choerodon-ui';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import styles from '../index.module.less';

const { Panel } = Collapse;

interface DetailModalProps {
  headDs: DataSet;
  instructionDocLineId: number;
  batchList: number[];
  resultData: object[],
  tableDsArr: any,
  selectedItem: string[],
  customizeTable: any,
}

const modelPrompt = 'tarzan.hmes.purchase.sellOrder';

const CreateMaterial: React.FC<DetailModalProps> = ({
  headDs,
  batchList,
  resultData,
  tableDsArr,
  selectedItem,
  customizeTable,
}) => {
  useEffect(() => {
    handleQuery();
  }, []);
  const [currentCrumb, setCurrentCrumb] = useState()

  const handleQuery = () => {
    // @ts-ignore
    for (let i = 0; i < selectedItem.length; i++) {
      tableDsArr[i].setQueryParameter('poLineId', selectedItem[i])
      tableDsArr[i].query();
    }
  };

  // 物料的表格columns
  const columns: ColumnProps[] = [
    {
      name: 'identification',
      align: ColumnAlign.left,
      width: 130,
      renderer: ({ record, value }) => {
        const { materialLotId } = record?.toData();
        return (
          <div>
            {batchList.indexOf(materialLotId) > -1 && (
              <div className={styles['icon-new']}>
                <div className={styles['icon-new-inner']}>
                  <div className={styles['icon-new-inner-text']}>NEW</div>
                </div>
              </div>
            )}
            {value}
          </div>
        );
      },
    },
    {
      name: 'primaryUomQty',
      align: ColumnAlign.right,
      width: 70,
    },
    {
      name: 'primaryUomCode',
      align: ColumnAlign.left,
      width: 80,
    },
    {
      name: 'materialLotStatus',
      align: ColumnAlign.left,
      width: 100,
    },
    {
      name: 'supplierLot',
      width: 80,
    },
    {
      name: 'productionDate',
      align: ColumnAlign.center,
      width: 150,
    },
    {
      name: 'lot',
      align: ColumnAlign.left,
      width: 70,
    },
    {
      name: 'printTimes',
      align: ColumnAlign.left,
      width: 60,
    },
    {
      name: 'createdByName',
      align: ColumnAlign.left,
      width: 90,
    },
    {
      name: 'creationDate',
      align: ColumnAlign.center,
      width: 140,
    },
  ];

  const headColumns: ColumnProps[] = [
    {
      name: 'poNumber',
      align: ColumnAlign.right,
      width: 160,
      renderer: ({ record }) => `${record?.get('poNumber')}/${record?.get('poLineNumber')}`,
    },
    {
      name: 'materialLotQty',
      align: ColumnAlign.left,
      width: 80,
      editor: true,
    },
    {
      name: 'materialSheets',
      align: ColumnAlign.left,
      width: 100,
      editor: true,
    },
    {
      name: 'supplierLot',
      width: 120,
      editor: true,
    },
    {
      name: 'productionDate',
      align: ColumnAlign.center,
      width: 150,
      editor: true,
    },
    {
      name: 'lot',
      width: 120,
      editor: true,
    },
    {
      name: 'materialCode',
      align: ColumnAlign.left,
      width: 150,
    },
    {
      name: 'revisionCode',
      align: ColumnAlign.left,
      width: 150,
    },
    {
      name: 'materialName',
      align: ColumnAlign.left,
      width: 100,
    },
    {
      name: 'materialLotStatus',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'uomCode',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'orderedQuantity',
      align: ColumnAlign.center,
      width: 100,
    },
    {
      name: 'createdQuantity',
      align: ColumnAlign.center,
      width: 100,
    },
  ];

  const crumbClick = (e) => {
    setCurrentCrumb(e);
    document.getElementById("container-table")!.scroll({
      top: document.getElementById(`num${e}`)!.offsetTop - 37,
      behavior: "smooth",
    });
  }

  const renderCrumbItem = () => {
    return (resultData as any[]).map(item =>
      <Breadcrumb.Item
        onClick={() => crumbClick(item.poLineNumber)}
        className={currentCrumb === item.poLineNumber ? styles['cus-crumb'] : styles['cus-crumb-normal']}
      >
        {item.poNumber}/{item.poLineNumber}
      </Breadcrumb.Item>,
    )
  }

  const renderTable = () => {
    return tableDsArr.map((item, index) => (
      <>
        <p
          // @ts-ignore
          id={`num${resultData[index]?.poLineNumber}`}
          // @ts-ignore
          className={currentCrumb === resultData[index]?.poLineNumber && styles['cus-color']}
        >
          {/* @ts-ignore */}
          {resultData[index]?.poLineNumber}{intl.get(`${modelPrompt}.lineMlotInfo`).d('行物料批')}
        </p>
        {customizeTable(
          {
            code: `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_MATERIAL_LOT.QUERY`,
          },
          <Table dataSet={item} columns={columns} />,
        )}
      </>
    ));
  }

  return (
    <div className="hmes-style">
      <Table dataSet={headDs} columns={headColumns} />
      <Collapse bordered={false} defaultActiveKey={['basicInfo']}>
        <Panel
          header={intl.get(`${modelPrompt}.material.information`).d('物料批信息')}
          key="basicInfo"
        >
          <Breadcrumb className={styles['cus-bread-cru']} separator=' '>
            {renderCrumbItem()}
          </Breadcrumb>
          <div className={styles['container-table']} id='container-table'>
            {renderTable()}
            <div className={styles['empty-container']} />
          </div>
        </Panel>
      </Collapse>
    </div>
  );
};
export default CreateMaterial;
