/**
 * @Description: 事务明细报表
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-08 14:09:22
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { Table, DataSet, Dropdown, Menu } from 'choerodon-ui/pro';
import { Action } from 'choerodon-ui/es/trigger/enum';
import { isNil } from 'lodash';
import ExcelExport from 'components/ExcelExport';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { observer } from 'mobx-react';
import { BASIC } from '@utils/config';
import { useRequest } from '@components/tarzan-hooks';
import { tableDS } from './stories';
import { ChangeStatus } from './services';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.pack.empty.material.rack.report';

const EmptyMaterialRackReport = props => {
  const { tableDs } = props;

  const { run: changeStatus, loading: changeStatusLoading } = useRequest(ChangeStatus(), { manual: true });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'number',
        width: 150,
        renderer: ({ record }) => {
          return <span>{record!.index + 1}</span>;
        },
      },
      {
        name: 'displayStatus',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'containerTypeCode',
        width: 120,
      },
      {
        name: 'locatorCode',
        width: 120,
      },
      {
        name: 'toCode',
        width: 120,
      },
      {
        name: 'locatorName',
        width: 120,
      },
      {
        name: 'qty',
        width: 120,
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 150,
      },
      {
        name: 'lastUpdatedByName',
        width: 150,
      },
    ];
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return {
      ...queryParmas,
      inoutConfirmIds: tableDs.selected.map(item => item.toData().inoutConfirmId),
    };
  };

  const handleChangeStatus = ({ key }) => {
    changeStatus({
      params: { 
        inoutConfirmIdList: tableDs.selected.map(_record => _record?.get('inoutConfirmId')),
        statusCode: key,
      },
      onSuccess: () => {
        notification.success({});
        tableDs.query(tableDs.currentPage);
      },
    })
  }

  return (
    <div className="hmes-style">
      <Header
        title={intl
          .get('tarzan.pack.empty.material.rack.report.title.New.list')
          .d('PACK空料架需求报表')}
      >
        <Dropdown
          overlay={
            <Menu
              onClick={handleChangeStatus}
              // className={styles['split-menu']}
              style={{ width: '100px' }}
            >
              <Menu.Item key="CANCEL">
                <a>{intl.get('tarzan.common.button.cancel').d('取消')}</a>
              </Menu.Item>
              <Menu.Item key="COMPLETED">
                <a>{intl.get(`${modelPrompt}.complete`).d('完成')}</a>
              </Menu.Item>
            </Menu>
          }
          trigger={[Action.click]}
        >
          <PermissionButton
            permissionList={[
              {
                code: `dist.button.changeStatus`,
                type: 'button',
                meaning: '详情页-状态变更按钮',
              },
            ]}
            disabled={!tableDs.selected?.length || tableDs.selected?.some(_record => _record?.get('status') !== 'RELEASED')}
            loading={changeStatusLoading}
          >
            {intl.get(`${modelPrompt}.changeStatus`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <ExcelExport
          method="GET"
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-inout-confirm/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          queryFieldsLimit={6}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="emptyMaterialRackReport"
          customizedCode="emptyMaterialRackReport"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.pack.empty.material.rack.report', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(observer(EmptyMaterialRackReport)),
);
