/**
 * @Description: 销售发运平台 - 详情页面DS
 * @Author: <EMAIL>
 * @Date: 2022/2/10 9:15
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import uuid from 'uuid/v4';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import request from 'utils/request';

const modelPrompt = 'tarzan.soDelivery.soDeliveryPlatform';
const tenantId = getCurrentOrganizationId();
const url = `${BASIC.HMES_BASIC}/v1/${tenantId}/miscellaneous/identify/get/ui`;

// 允差类型下拉框数据源
const toleranceTypeOptionDs = new DataSet({
  autoQuery: true,
  dataKey: 'rows',
  paging: false,
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/limit-group/type`,
        method: 'POST',
        data: { typeGroup: 'INSTRUCTION_TOLERANCE_TYPE', module: 'GENERAL', tenantId },
      };
    },
  },
});

const detailHeaderDS = (): DataSetProps => ({
  autoQuery: false,
  dataKey: 'rows',
  fields: [
    {
      name: 'instructionDocId',
      type: FieldType.number,
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单号'),
    },
    {
      name: 'instructionDocTypeTag',
      type: FieldType.string,
    },
    {
      name: 'businessType',
      type: FieldType.string,
    },
    {
      name: 'instructionDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocStatus`).d('单据状态'),
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=INSTRUCTION_DOC_STATUS_SO_DELIVERY`,
      textField: 'description',
      valueField: 'statusCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      disabled: true,
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
      lookupCode: 'MT.SO_DELIVERY_RETURN_DOC_TYPE',
      required: true,
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
    },
    {
      name: 'instructionDocTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
    },
    {
      name: 'sourceSystemDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sourceSystemDesc`).d('来源系统'),
    },
    {
      name: 'siteObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteObj.siteId',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      bind: 'siteObj.siteCode',
    },
    {
      name: 'customerObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customer`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      textField: 'customerName',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      type: FieldType.number,
      bind: 'customerObj.customerId',
    },
    {
      name: 'customerName',
      type: FieldType.string,
      bind: 'customerObj.customerName',
    },
    {
      name: 'contactAddress',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactAddress`).d('送货地址'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      // name: 'expectedArrivalTime',
      name: 'demandTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.expectedArrivalTime`).d('计划发货日期'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      // name: 'demandTime',
      name: 'expectedArrivalTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.demandTime`).d('计划退货日期'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'RETURN';
        },
      },
    },
    {
      name: 'contactPerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactPerson`).d('收货人'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'contactTel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.contactTel`).d('收货人电话'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'contactFax',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerOrderNumber`).d('客户订单号'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
        required: ({ record }) => {
          const docType = record?.get('instructionDocType');
          const instructionDocList = record?.get('instructionDocTypeList');
          
          if (!docType) return false;
    
          const foundItem = instructionDocList?.find(item => item.value === docType);
    
          return foundItem?.tag === "DELIVERY";
        },
      },
    },
    {
      name: 'logisticsMode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.logisticsMode`).d('物流方式'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'logisticsCompanyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.logisticsCompanyCode`).d('物流公司编码'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'logisticsCompanyDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.logisticsCompanyDesc`).d('物流公司名称'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'paymentType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.paymentType`).d('付款方式'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record?.get('instructionDocTypeTag') !== 'DELIVERY';
        },
      },
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdBy`).d('创建人'),
      disabled: true,
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.createdDate`).d('创建时间'),
      disabled: true,
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-delivery-platform/instruction-doc/head/detail/for/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_DETAIL.HEAD,${BASIC.CUSZ_CODE_BEFORE}.SO_DELIVERY_DETAIL.LINE`,
        method: 'GET',
      };
    },
  },
});

const detailLineDS = (): DataSetProps => ({
  selection: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'instructionDocLineId',
      type: FieldType.number,
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'identifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifyType`).d('管理模式'),
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.BOM_MATERIAL',
      lovPara: {
        tenantId,
      },
      required: true,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteIds: dataSet.parent?.current?.get('siteId'),
          };
        },
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialObj.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      bind: 'materialObj.materialCode',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'materialObj.revisionFlag',
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
      bind: 'materialObj.currentRevisionCode',
      textField: 'description',
      valueField: 'description',
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
      lookupAxiosConfig: ({ record }) => {
        return {
          transformResponse(data) {
            let rows;
            if (Array.isArray(data)) {
              rows = data;
            } else {
              rows = JSON.parse(data).rows;
            }
            let firstlyQueryData: any = [];
            if (rows instanceof Array) {
              firstlyQueryData = rows.map(item => {
                return {
                  kid: uuid(),
                  description: item,
                };
              });
            }
            if (record) {
              if (firstlyQueryData.length > 0) {
                if (!record?.get('revisionCode')) {
                  // eslint-disable-next-line no-unused-expressions
                  record?.init('revisionCode', firstlyQueryData[0].description);
                }
              } else {
                // eslint-disable-next-line no-unused-expressions
                record?.init('revisionCode', null);
              }
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('materialId');
        },
        required({ record }) {
          return record?.get('revisionFlag') === 'Y' && record?.get('materialId');
        },
        lovPara: ({ record, dataSet }) => {
          return {
            tenantId,
            siteIds: dataSet.parent?.current?.get('siteId') || undefined,
            materialId: record?.get('materialId') || undefined,
            kid: record?.get('kid') || undefined,
          };
        },
      },
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialObj.materialName',
    },
    {
      name: 'needQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.needQty`).d('需求数量'),
      // min: 1,
      required: true,
      dynamicProps: {
        min: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
        precision: ({ record }) => {
          return record?.get('decimalNumber');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
      },
    },
    {
      name: 'decimalNumber',
      type: FieldType.number,
      bind: 'materialObj.decimalNumber',
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
      bind: 'materialObj.uomCode',
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('状态'),
    },
    {
      name: 'soNumberObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.soNumber`).d('销售订单号'),
      // lovCode: `${BASIC.LOV_CODE_BEFORE}.SO_NUMBER`,
      // dynamicProps: {
      //   lovPara: ({ dataSet }) => {
      //     return {
      //       tenantId,
      //       siteId: dataSet.parent?.current?.get('siteId'),
      //     };
      //   },
      // },
      // ignore: FieldIgnore.always,
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.SO_LINE`,
      // valueField: 'soId',
      valueField: 'soLineId',
      // textField: 'soNumber',
      noCache: true,
      dynamicProps: {
        lovPara: ({ dataSet }) => {
          return {
            tenantId,
            siteId: dataSet.parent?.current?.get('siteId'),
          };
        },
        disabled: ({ record , dataSet}) => {
          return !dataSet.parent?.current?.get('siteId') || record.get('instructionDocLineId');
        },
      },

    },
    {
      name: 'soId',
      type: FieldType.number,
      bind: 'soNumberObj.soId',
    },
    {
      name: 'soNumber',
      type: FieldType.string,
      bind: 'soNumberObj.soNumber',
    },
    // {
    //   name: 'soLineObj',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
    //   lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.SO_LINE`,
    //   valueField: 'soLineId',
    //   textField: 'soLineNum',
    //   dynamicProps: {
    //     disabled: ({ record }) => {
    //       return !record?.get('soNumber');
    //     },
    //     required: ({ record }) => {
    //       return record?.get('soNumber');
    //     },
    //     lovPara: ({ record }) => {
    //       return {
    //         tenantId,
    //         soNumber: record?.get('soNumber'),
    //       };
    //     },
    //   },
    //   ignore: FieldIgnore.always,
    // },
    {
      name: 'soLineId',
      type: FieldType.number,
      bind: 'soNumberObj.soLineId',
    },
    {
      name: 'soLineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.soLineNum`).d('销售订单行号'),
      bind: 'soNumberObj.soLineNum',
    },
    {
      name: 'targetSite',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.targetSiteCode`).d('目标站点'),
      lovCode: 'MT.MODEL.SITE',
      required: true,
      lovPara: {
        tenantId,
        siteType: 'MANUFACTURING',
      },
    },
    {
      name: 'targetSiteCode',
      type: FieldType.string,
      bind: 'targetSite.siteCode',
    },
    {
      name: 'targetSiteId',
      type: FieldType.number,
      bind: 'targetSite.siteId',
    },
    {
      name: 'targetLocator',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.targetLocatorCode`).d('目标仓库'),
      lovCode: 'MT.MODEL.LOCATOR',
      dynamicProps: {
        lovPara: ({ record, dataSet }) => {
          return {
            tenantId,
            siteId: record?.get('targetSiteId'),
            businessTypes: [dataSet.parent?.current?.get('businessType')],
            queryType:
              dataSet.parent?.current?.get('instructionDocTypeTag') === 'DELIVERY'
                ? 'SOURCE'
                : 'TARGET',
            locatorCategories: 'AREA',
          };
        },
        required: ({ dataSet }) => {
          if (dataSet.parent?.current?.get('instructionDocTypeTag') === 'DELIVERY') {
            return dataSet.parent?.current?.get('fromLocatorRequiredFlag') === 'Y';
          } if (dataSet.parent?.current?.get('instructionDocTypeTag') === 'RETURN') {
            return dataSet.parent?.current?.get('toLocatorRequiredFlag') === 'Y';
          }
        },
        disabled: ({ record }) => {
          return !record?.get('targetSiteCode');
        },
      },
    },
    {
      name: 'targetLocatorCode',
      type: FieldType.string,
      bind: 'targetLocator.locatorCode',
    },
    {
      name: 'targetLocatorId',
      type: FieldType.number,
      bind: 'targetLocator.locatorId',
    },
    {
      name: 'prepareQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.prepareQty`).d('备货数量'),
    },
    {
      name: 'deliveryQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.deliveryQty`).d('发货数量'),
    },
    {
      name: 'receivingQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.receivingQty`).d('接收数量'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'orderFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderFlag`).d('按单标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('soLineId');
        },
      },
    },
    {
      name: 'toleranceFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceFlag`).d('允差标识'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'toleranceType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceType`).d('允差类型'),
      options: toleranceTypeOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      dynamicProps: {
        required: ({ record }) => {
          return record?.get('toleranceFlag') === 'Y';
        },
      },
    },
    {
      name: 'toleranceTypeDesc',
      type: FieldType.string,
    },
    {
      name: 'toleranceMaxValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceMaxValue`).d('上允差值'),
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record?.get('toleranceType') || record?.get('toleranceType') === 'OVER_MATERIAL_LOT'
          );
        },
        required: ({ record }) => {
          return (
            record?.get('toleranceType') &&
            ['NUMBER', 'PERCENTAGE'].includes(record?.get('toleranceType'))
          );
        },
      },
    },
    {
      name: 'toleranceMinValue',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.toleranceMinValue`).d('下允差值'),
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record?.get('toleranceType') || record?.get('toleranceType') === 'OVER_MATERIAL_LOT'
          );
        },
        required: ({ record }) => {
          return (
            record?.get('toleranceType') &&
            ['NUMBER', 'PERCENTAGE'].includes(record?.get('toleranceType'))
          );
        },
      },
    },
  ],
  events: {
    update: ({ record, name, dataSet }) => {
      if (name === 'targetLocator' || name === 'materialObj' || name === 'targetSite') {
        const { materialObj, targetLocator, targetSite } = record.toData();
        const obj = {
          materialId: materialObj?.materialId,
          targetLocatorId: targetLocator?.locatorId,
          targetSiteId: targetSite?.siteId,
          siteId: dataSet.parent?.current?.get('siteId'),
        };
        const params = {};
        Object.entries(obj).forEach(item => {
          if (item[1] !== undefined) {
            params[item[0]] = item[1];
          }
        });
        request(url, {
          method: 'GET',
          query: { ...params },
        }).then(res => {
          if (res?.success) {
            if (res?.rows) {
              record.init('identifyType', res.rows);
            }
          }
        });
      }
    },
  },
  record: {
    dynamicProps: {
      disabled: record => {
        return (
          record.get('instructionDocLineId') &&
          (!record.get('permissionFlag') || record.get('permissionFlag') === 'N')
        );
      },
    },
  },
});

export { detailHeaderDS, detailLineDS };
