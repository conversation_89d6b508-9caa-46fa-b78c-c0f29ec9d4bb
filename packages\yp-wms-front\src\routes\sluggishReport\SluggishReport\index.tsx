/**
 * @Description: 呆滞报表
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-08 14:09:22
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo, useState, useEffect } from 'react';
import { Table, DataSet, Button, Modal, Form, Select, TextArea } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { isNil } from 'lodash';
// import { useDataSetEvent } from 'utils/hooks';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@utils/config';
import notification from 'utils/notification';
import request from 'utils/request';
import { getResponse } from '@utils/utils';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { tableDS } from './stories';
import getFormDsProps from './stories/FormDs.js';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.sluggishReport';

const SluggishReport = (props) => {

  const { tableDs } = props;

  const [selectDatas, setSelectDatas] = useState<any>([]);
  // 监听C7Npro列表选中操作
  useEffect(() => {
    queryDefultSite()
    if (tableDs) {
      tableDs.addEventListener('query', handleQuery);
      tableDs.addEventListener('select', handleDataSetSelectUpdate);
      tableDs.addEventListener('unSelect', handleDataSetSelectUpdate);
      tableDs.addEventListener('selectAll', handleDataSetSelectUpdate);
      tableDs.addEventListener('unSelectAll', handleDataSetSelectUpdate);
    }
    return () => {
      if (tableDs) {
        tableDs.removeEventListener('query', handleQuery);
        tableDs.removeEventListener('select', handleDataSetSelectUpdate);
        tableDs.removeEventListener('unSelect', handleDataSetSelectUpdate);
        tableDs.removeEventListener('selectAll', handleDataSetSelectUpdate);
        tableDs.removeEventListener('unSelectAll', handleDataSetSelectUpdate);
      }
    };
  }, []);

  const queryDefultSite = async() => {
    const result = await request(`${
      BASIC.TARZAN_MODEL
    }/v1/${getCurrentOrganizationId()}/mt-user-organization/user/default/site/ui`, {
      method: 'GET',
      query: {},
    });
    const res = getResponse(result);
    if (res) {
      // 设置用户默认工厂
      const defaultSiteInfo = {
        siteId: res.rows.siteId,
        siteCode: res.rows.siteCode,
        siteName: res.rows.siteName,
      };
      tableDs.queryDataSet.current.set('siteLov', defaultSiteInfo);
    } else {
      const defaultSiteInfo = {
        siteId: '',
        siteCode: '',
        siteName: '',
      };
      tableDs.queryDataSet.current.set('siteLov', defaultSiteInfo);
    }
  }
  const handleQuery = () => {
    setSelectDatas([])
  }

  // 处理选中条执行作业状态
  const handleDataSetSelectUpdate = () => {
    setSelectDatas(tableDs.selected);
  };

  // useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
  //   switch (name) {
  //     case 'materialId':
  //       record.set('revisionCode', null);
  //       break;
  //     case 'sourceOrderType':
  //       record.set('sourceOrderLov', null);
  //       break;
  //     case 'sourceOrderLov':
  //       record.set('sourceOrderLineLov', null);
  //       break;
  //     default:
  //       break;
  //   }
  // });

  // useDataSetEvent(tableDs, 'query', () => {
  //   // 由于查询头是「动态筛选条」在给ds.queryDataSet的字段重新赋值的时候，值变更会自动查询，所以会查询两遍
  //   const currentDate = new Date();
  //   currentDate.setMinutes(currentDate.getMinutes() + 1);
  //   tableDs!.queryDataSet!.current!.set('lastUpdateDateTo', currentDate);
  // });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'identification',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 200,
      },
      {
        name: 'sluggishType',
        width: 120,
      },
      {
        name: 'materialName',
        width: 200,
      },
      {
        name: 'supplierCode',
        width: 200,
      },
      {
        name: 'supplierName',
        width: 200,
      },
      {
        name: 'lot',
        width: 120,
      },
      {
        name: 'primaryUomQty',
        width: 120,
      },
      {
        name: 'primaryUomCode',
        width: 120,
      },
      {
        name: 'sluggishDate',
        width: 180,
      },
      {
        name: 'dullPeriodSeconday',
        width: 180,
      },
      {
        name: 'periodSeconday',
        width: 180,
      },
      {
        name: 'receipeTime',
        width: 180,
      },
      {
        name: 'inLocatorTime',
        width: 180,
      },
      {
        name: 'shelfLifeUomCode',
        width: 150,
      },
      {
        name: 'siteCode',
        width: 150,
      },
      {
        name: 'warehouseCode',
        width: 150,
      },
      {
        name: 'locatorCode',
        width: 150,
      },
      {
        name: 'qualityStatusDesc',
        width: 150,
      },
      {
        name: 'currentContainerIdentification',
        width: 150,
      },
      {
        name: 'topContainerIdentification',
        width: 150,
      },
      {
        name: 'freezeFlag',
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
    ];
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  };

  // 冻结
  const handleFreeze = () => {
    // this.props.FormDs.loadData([]);
    const FormDs = new DataSet(
      getFormDsProps(true),
    );
    FormDs.reset();
    const ModalContent = () => {
      return (
        <div>
          <Form
            dataSet={FormDs}
            columns={1}
          >
            <Select name="reason" clearButton />
            <TextArea name="remark" clearButton />
          </Form>
        </div>
      );
    };
    Modal.open({
      title: intl.get(`${modelPrompt}.title.wheter.freeze`).d('是否冻结'),
      children: <ModalContent />,
      okText: intl.get(`tarzan.common.button.confirm`).d('确定'),
      okProps: { disabled: false },
      autoCenter: true,
      onOk: () =>{
        return  new Promise(async (resolve) => {
          const checkHead = await FormDs.validate(false, false);
          if (!checkHead) {
            resolve(false);
            return;
          }
          const formData = FormDs.map((item) => {
            return {
              ...item.data,
            };
          });
          // 请求后台
          const materialLotIds = tableDs.selected.map((e) => e.data.materialLotId);
          const result = await request(
            `${
              BASIC.HMES_BASIC
            }/v1/${getCurrentOrganizationId()}/sluggish-material-lot/report/freeze/ui`,
            {
              method: 'POST',
              body: {
                ...formData[0],
                materialLotIds,
              },
            },
          );
          const res = getResponse(result);
          if (res) {
            notification.success({
              message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
            });
            tableDs.query();
            resolve(true);
          } else {
            resolve(false);
          }
        })
      },
      onCancel: () => {},
    });
  };

  const handleUnFreeze = () => {
    const FormDs = new DataSet(
      getFormDsProps(),
    );
    FormDs.reset();
    const ModalContent = () => {
      return (
        <div>
          <Form
            dataSet={FormDs}
            columns={1}
          >
            <Select name="reason" clearButton />
            <TextArea name="remark" clearButton />
          </Form>
        </div>
      );
    };
    Modal.open({
      title: intl.get(`${modelPrompt}.title.wheter.thaw`).d('是否解冻'),
      children: <ModalContent />,
      okText: intl.get(`tarzan.common.button.confirm`).d('确定'),
      okProps: { disabled: false },
      autoCenter: true,
      onOk: () =>{
        return  new Promise(async (resolve) => {
          const checkHead = await FormDs.validate(false, false);
          if (!checkHead) {
            resolve(false);
            return;
          }
          const formData = FormDs.map((item) => {
            return {
              ...item.data,
            };
          });
          // 请求后台
          const materialLotIds = tableDs.selected.map((e) => e.data.materialLotId);
          const result = await request(
            `${
              BASIC.HMES_BASIC
            }/v1/${getCurrentOrganizationId()}/sluggish-material-lot/report/unfreeze/ui`,
            {
              method: 'POST',
              body: {
                ...formData[0],
                materialLotIds,
              },
            },
          );
          const res = getResponse(result);
          if (res) {
            notification.success({
              message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
            });
            tableDs.query();
            resolve(true);
          } else {
            resolve(false);
          }
        })
      },
      onCancel: () => {},
    });
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.mes.sluggishReport.title.New.list').d('呆滞报表')}>
        <Button
          color={ButtonColor.primary}
          disabled={selectDatas.length === 0 || (selectDatas.filter((e) => e.data.freezeFlag === "Y").length > 0  && selectDatas.filter((e) => e.data.freezeFlag === "N").length > 0) || selectDatas.filter((e) => e.data.freezeFlag === "N").length > 0 }
          onClick={handleUnFreeze}>{intl.get(`${modelPrompt}.unfreeze`).d('解冻')}
        </Button>
        <Button
          color={ButtonColor.primary}
          disabled={selectDatas.length === 0 || (selectDatas.filter((e) => e.data.freezeFlag === "Y").length > 0  && selectDatas.filter((e) => e.data.freezeFlag === "N").length > 0) || selectDatas.filter((e) => e.data.freezeFlag === "Y").length > 0}
          onClick={handleFreeze}>{intl.get(`${modelPrompt}.freeze`).d('冻结')}
        </Button>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/sluggish-material-lot/report/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          queryFieldsLimit={11}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="sluggishReport1"
          customizedCode="sluggishReport1"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.mes.sluggishReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(SluggishReport),
);
