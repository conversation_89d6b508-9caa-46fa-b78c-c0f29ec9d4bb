/**
 * @Description: 销售发运平台 - 入口页面DS
 * @Author: <EMAIL>
 * @Date: 2022/2/9 18:02
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldType, FieldIgnore } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'hwms.packageProposalManagement';
const tenantId = getCurrentOrganizationId();

const tableDS = (url): DataSetProps => ({
  autoQuery: true,
  selection: DataSetSelection.multiple,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  cacheSelection: true,
  autoLocateFirst: true,
  queryFields: [
    {
      name: 'creationDateFrom',
      type: FieldType.dateTime,
      max: 'creationDateTo',
      label: intl.get(`${modelPrompt}.creationDateFrom`).d('创建时间从'),
    },
    {
      name: 'creationDateTo',
      type: FieldType.dateTime,
      min: 'creationDateFrom',
      label: intl.get(`${modelPrompt}.creationDateTo`).d('创建时间至'),
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      lovCode: 'MT.MODEL.SUPPLIER',
      multiple: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierIds',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('零件号'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      multiple: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'status',
      type: FieldType.string,
      lookupCode: 'WMS.PACKAGE_PROPOSAL_STATUS',
      label: intl.get(`${modelPrompt}.status`).d('提案状态'),
    },
    {
      name: 'lastUpdateDateFrom',
      type: FieldType.dateTime,
      max: 'lastUpdateDateTo',
      label: intl.get(`${modelPrompt}.lastUpdateDateFrom`).d('最后更新时间从'),
    },
    {
      name: 'lastUpdateDateTo',
      type: FieldType.dateTime,
      min: 'lastUpdateDateFrom',
      label: intl.get(`${modelPrompt}.lastUpdateTo`).d('最后更新时间至'),
    },
    {
      name: 'packageType',
      type: FieldType.string,
      lookupCode: 'WMS.PACKAGE_TYPE',
      label: intl.get(`${modelPrompt}.packageType`).d('包装种类'),
    },
  ],
  fields: [
    {
      name: 'number',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.orderSeq`).d('序号'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('状态'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('零件号'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('零件名称'),
    },
    {
      name: 'packageTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageTypeDesc`).d('包装种类'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createByName`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdateByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateByName`).d('最后更新人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}${url}`,
        method: 'POST',
      };
    },
  },
});

export { tableDS };
