/*
 * @Author: cqy <EMAIL>
 * @Date: 2023-12-26 11:23:41
 * @LastEditors: cqy <EMAIL>
 * @LastEditTime: 2023-12-28 17:46:42
 * @FilePath: \yp-wms-front\packages\yp-wms-front\src\routes\CustomerSuppliedParts\services\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @Description: 库存调拨平台-services
 * @Author: <EMAIL>
 * @Date: 2022/7/20 11:33
 * @LastEditTime: 2023-05-18 16:22:24
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

// 库存调拨平台-状态变更
export function HandleChangeStatus() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms_customer_supplied_parts/status/alter/for/ui`,
    method: 'POST',
  };
}

// 库存调拨平台-行取消
export function HandleDeleteLine() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms_customer_supplied_parts/line/cancel/for/ui`,
    method: 'POST',
  };
}

export function QueryRevision(): object {
  return {
    url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-material/site-material/limit/lov/ui`,
    method: 'GET',
  };
}

// 库存调拨平台-保存
export function HandleSaveInstruction() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms_customer_supplied_parts/save/ui`,
    method: 'POST',
  };
}

// 库存调拨平台-查询允差信息
export function GetToleranceInfo() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/inventory-send-receive/stock-instruction-tolerance/get/for/ui`,
    method: 'GET',
  };
}
// 打印
export function Print() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms_customer_supplied_parts/doc/print`,
    method: 'POST',
  };
}

export function AvailableQuantity() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms_customer_supplied_parts/selectIdentifyType`,
    method: 'GET',
  };
}
