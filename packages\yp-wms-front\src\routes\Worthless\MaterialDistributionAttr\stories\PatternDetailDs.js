/**
 * @feature 物料配送属性维护-配送明细
 * @date 2021-3-8
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.product.materialDistributionNew.model.materialDistributionNew';

const tenantId = getCurrentOrganizationId();

/**
 * 配送模式明细-定时补货
 */
const timingDS = () => ({
  autoQuery: false,
  selection: false,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'distributionCycle',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.distributionCycle`).d('配送周期T'),
      min: 0,
      step: 1,
    },
    {
      name: 'materialConsumeRate',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.materialConsumeRate`).d('物料消耗速率'),
    },
    {
      name: 'materialConsumeRateUomId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialConsumeRateUom`).d('物料消耗速率单位'),
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-pfep-distribution/pfep/rate-uom/ui`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      textField: 'uomName',
      valueField: 'uomId',
      required: true,
    },
    {
      name: 'bufferInventory',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.bufferInventory`).d('安全库存'),
    },
  ],
  transport: {},
});

/**
 * 配送模式明细-顺序补货
 */
const sequenceDS = () => ({
  autoQuery: false,
  selection: false,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'distributionCycle',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.distributionCycle`).d('配送周期T'),
      min: 0,
      step: 1,
    },
    {
      name: 'materialConsumeRate',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.materialConsumeRate`).d('物料消耗速率'),
    },
    {
      name: 'materialConsumeRateUomId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialConsumeRateUom`).d('物料消耗速率单位'),
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-pfep-distribution/pfep/rate-uom/ui`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      textField: 'uomName',
      valueField: 'uomId',
      required: true,
    },
    {
      name: 'bufferPeriod',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.bufferPeriod`).d('缓冲时间'),
      min: 0,
      step: 1,
    },
    {
      name: 'calendar',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.calendar`).d('时段日历'),
      lovCode: 'MT.MODEL.CALENDAR',
      required: true,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'calendarId',
      bind: 'calendar.calendarId',
    },
    {
      name: 'calendarCode',
      bind: 'calendar.calendarCode',
    },
    {
      name: 'instructionCreatedByEoFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionCreatedByEoFlag`).d('按作业拆分指令'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'fromScheduleRateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromScheduleRateFlag`).d('采用生产速率'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  transport: {},
});

/**
 * 配送模式明细-订单补货
 */
const orderDS = () => ({
  autoQuery: false,
  selection: false,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'distributionCycle',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.distributionCycle`).d('配送周期T'),
      min: 0,
      step: 1,
    },
    {
      name: 'materialConsumeRate',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.materialConsumeRate`).d('物料消耗速率'),
    },
    {
      name: 'materialConsumeRateUomId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialConsumeRateUom`).d('物料消耗速率单位'),
      lookupUrl: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-pfep-distribution/pfep/rate-uom/ui`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      textField: 'uomName',
      valueField: 'uomId',
      required: true,
    },
    {
      name: 'bufferInventory',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.bufferInventory`).d('安全库存'),
    },
    {
      name: 'fromScheduleRateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.fromScheduleRateFlag`).d('采用生产速率'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  transport: {},
});

/**
 * 配送模式明细-定量补货
 */
const rationDS = () => ({
  autoQuery: false,
  selection: false,
  autoCreate: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'maxInventory',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.maxInventory`).d('最大库存'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('minInventory');
        },
      },
    },
    {
      name: 'bufferInventory',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.bufferInventory`).d('安全库存'),
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('minInventory') || !record.get('maxInventory');
        },
      },
    },
    {
      name: 'minInventory',
      type: FieldType.number,
      required: true,
      label: intl.get(`${modelPrompt}.minInventory`).d('最小库存'),
      min: 0,
    },
  ],
  transport: {},
});

export { timingDS, sequenceDS, orderDS, rationDS };
