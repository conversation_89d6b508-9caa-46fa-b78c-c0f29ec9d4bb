import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import intl from 'utils/intl';
import { isNil } from 'lodash';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { getCurrentOrganizationId } from 'utils/utils';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import ExcelExport from 'components/ExcelExport';
import { BASIC } from '@utils/config';
import { listDS, detailDS } from './stores/listDS';

// const BASIC = {
//   TARZAN_REPORT: '/yp-report-wms-44212',
// }

const modelPrompt = 'tarzan.hmes.purchase.freeze';
const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

const Freeze = props => {
  const {
    listDs,
    detailDs,
  } = props;

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'siteCode',
      },
      {
        name: 'warehouseCode',
      },
      {
        name: 'materialCode',
      },
      {
        name: 'materialDesc',
      },
      {
        name: 'number',
      },
      {
        name: 'status',
      },
      {
        name: 'supplierCode',
      },
      {
        name: 'supplierName',
      },
    ];
  }, []);

  const detailColumns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'locatorCode',
      },
      {
        name: 'materialLotCode',
      },
      {
        name: 'materialCode',
      },
      {
        name: 'materialDesc',
      },
      {
        name: 'supplierCode',
      },
      {
        name: 'supplierName',
      },
      {
        name: 'primaryUomQty',
      },
      {
        name: 'primaryUomCode',
      },
      {
        name: 'lot',
      },
      {
        name: 'reason',
      },
      {
        name: 'receipeTime',
      },
      {
        name: 'lastUpdateDate',
      },
      {
        name: 'lastUpdateByName',
      },
    ];
  }, []);

  // 点击汇总行展示相应明细行数据
  const clickHandle = async record => {
    const id = record.toData().id;
    if (id) {
      detailDs.setQueryParameter('id', id);
      await detailDs.query();
    }
  };

  const getExportQueryParams = () => {
    if (listDs.selected.length > 0) {
      const queryParams = {
        id: listDs.selected.map((item => item.toData().id)),
      }
      return queryParams;
    }
    if (!listDs.queryDataSet || !listDs.queryDataSet.current) {
      return {};
    }
    const queryParams = listDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    })
    return queryParams;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.list`).d('库存冻结、不良明细报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-inventory-freeze-poor/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={listDs}
          columns={columns}
          onRow={({ record }) => ({
            onClick: () => clickHandle(record),
          })}
          searchCode="FreezeAndDefectReport"
          customizedCode="FreezeAndDefectReport"
        />
        <Collapse
          bordered={false}
          defaultActiveKey={['detailInfo']}
          style={{ position: 'relative' }}
        >
          <Panel
            header={intl.get(`${modelPrompt}.title.detailInfo`).d('行信息')}
            key="detailInfo"
            dataSet={detailDs}
          >
            <Table
              queryBar={TableQueryBarType.none}
              searchCode="FreezeAndDefectReportDetail"
              customizedCode="FreezeAndDefectReportDetail"
              dataSet={detailDs}
              highLightRow={false}
              columns={detailColumns}
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: [modelPrompt, 'tarzan.aps.common'],
})(
  withProps(
    () => {
      const listDs = new DataSet({
        ...listDS(),
      });
      const detailDs = new DataSet({
        ...detailDS(),
      });
      return {
        listDs,
        detailDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(Freeze),
);
