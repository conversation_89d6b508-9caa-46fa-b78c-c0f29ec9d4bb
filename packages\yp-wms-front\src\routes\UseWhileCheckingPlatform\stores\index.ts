import intl from 'utils/intl';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import DataSet, { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { searchCopy } from '@utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'useWhileCheckingPlatform';
const tenantId = getCurrentOrganizationId();

const headDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: true,
  selection: DataSetSelection.multiple,
  dataKey: 'content', // 列表数据在接口返回json中的相对路径
  totalKey: 'totalElements',
  primaryKey: 'docId',
  queryDataSet: new DataSet({
    events: {
      update({ record, name, value }) {
        searchCopy(
          [
            'materialLotCodes',
          ],
          name,
          record,
          value,
        );
      }
    },
    fields: [
      {
        name: 'useWhileCheckingDocNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.useWhileCheckingDocNum`).d('边检边用申请编码'),
      },
      {
        name: 'useWhileCheckingDocStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.useWhileCheckingDocStatus`).d('申请状态'),
        lovPara: {
          tenantId,
        },
        lookupCode: 'WMS.FACTORY_APPLICATION_STATUS',
      },
      {
        name: 'materialLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.materialCode`).d('物料'),
        lovCode: 'MT.METHOD.MATERIAL',
        lovPara: { tenantId },
        ignore: FieldIgnore.always,
        multiple: true,
      },
      {
        name: 'materialCode',
        bind: 'materialLov.materialCode',
      },
      {
        name: 'materialLotCodes',
        multiple: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.materialLotCodes`).d('物料批编码'),
      },
      {
        name: 'supplier',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
        lovCode: 'MT.MODEL.SUPPLIER',
        multiple: true,
        ignore: FieldIgnore.always,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'supplierCode',
        type: FieldType.string,
        bind: 'supplier.supplierCode',
      },
      {
        name: 'lot',
        type: FieldType.string,
        multiple: true,
        label: intl.get(`${modelPrompt}.lot`).d('批次'),
      },
      {
        name: 'wareHouseLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
        lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
        multiple: true,
        lovPara: {
          tenantId,
          locatorCategory: ['AREA'],
        },
      },
      {
        name: 'wareHouseCode',
        bind: 'wareHouseLov.wareHouseCode',
      },
      {
        name: 'locatorLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
        lovCode: 'MT.MODEL.LOCATOR',
        ignore: FieldIgnore.always,
        multiple: true,
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'locatorCode',
        bind: 'locatorLov.locatorCode',
      },
      {
        name: 'createdByLov',
        type: FieldType.object,
        label: intl.get(`${modelPrompt}.createdByLov`).d('申请人'),
        lovCode: 'MT.USER.ORG',
        lovPara: {
          tenantId,
        },
        ignore: FieldIgnore.always,
      },
      {
        name: 'createBy',
        bind: 'createdByLov.id',
      },
      {
        name: 'productionDateFrom',
        type: FieldType.dateTime,
        max: 'productionDateTo',
        label: intl.get(`${modelPrompt}.productionDateFrom`).d('生产日期从'),
        // defaultValue: moment(new Date()).format('YYYY-MM-DD 00:00:00'),
      },
      {
        name: 'productionDateTo',
        type: FieldType.dateTime,
        min: 'productionDateFrom',
        label: intl.get(`${modelPrompt}.productionDateTo`).d('生产日期至'),
        // defaultValue: moment(new Date()).format('YYYY-MM-DD 23:59:59'),
      },
      {
        name: 'inLocatorTimeFrom',
        type: FieldType.dateTime,
        max: 'inLocatorTimeTo',
        label: intl.get(`${modelPrompt}.inLocatorTimeFrom`).d('入库时间从'),
        // defaultValue: moment(new Date()).format('YYYY-MM-DD 00:00:00'),
      },
      {
        name: 'inLocatorTimeTo',
        type: FieldType.dateTime,
        min: 'inLocatorTimeFrom',
        label: intl.get(`${modelPrompt}.inLocatorTimeTo`).d('入库时间至'),
        // defaultValue: moment(new Date()).format('YYYY-MM-DD 23:59:59'),
      },
      {
        name: 'singlePieceCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.singlePieceCode`).d('单件码'),
      },
    ]
  }),
  fields: [
    {
      name: 'useWhileCheckingDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.useWhileCheckingDocNum`).d('边检边用申请编码'),
    },
    {
      name: 'useWhileCheckingDocStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.useWhileCheckingDocStatus`).d('申请状态'),
      lovPara: {
        tenantId,
      },
      lookupCode: 'WMS.FACTORY_APPLICATION_STATUS',
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
    },
    {
      name: 'createByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createByName`).d('申请人'),
    },
    {
      name: 'creationReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationReason`).d('申请原因'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最近更新时间'),
    },
  ],
  transport: {
    read: ({ data }) => {
      data.materialLotCodes = data.materialLotCodes.join()
      return {
        data,
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-use-while-checking-docs/query-doc`,
        method: 'POST',
      };
    },
  },
});

const lineDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'content', // 列表数据在接口返回json中的相对路径
  totalKey: 'totalElements',
  primaryKey: 'lineId',
  fields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批条码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'quantity',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.quantity`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'singlePieceQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.singlePieceQty`).d('单件码数量'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器'),
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
    },
    {
      name: 'productionDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
    },
    {
      name: 'expirationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.expirationDate`).d('到期日期'),
    },
    {
      name: 'inLocatorTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTime`).d('入库时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-use-while-checking-lines/query-line`,
        method: 'GET',
      };
    },
  },
});

const materialDetailDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  dataKey: 'content', // 列表数据在接口返回json中的相对路径
  totalKey: 'totalElements',
  fields: [
    {
      name: 'singlePieceCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.singlePieceCode`).d('单件码'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-use-while-checking-lines/query-code`,
        method: 'GET',
        transformResponse: val => {
          const { content = [] } = JSON.parse(val);
          const dataList: any[] = [];
          content.forEach(item => {
            if (item) {
              dataList.push({ singlePieceCode: item });
            }
          });
          return dataList;
        },
      };
    },
  },
});

export { headDS, lineDS, materialDetailDS };
