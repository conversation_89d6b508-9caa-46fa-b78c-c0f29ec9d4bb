/**
 * @Description: 事务明细报表-主表格-DS
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-25 14:54:49
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.mes.sluggishReport';
const tenantId = getCurrentOrganizationId();

export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  // selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.siteLov`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      required: true,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'siteId',
      type: FieldType.number,
      bind: 'siteLov.siteId',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.materialLov`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record?.get('siteId') || null,
          };
        },
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'sluggishType',
      type: FieldType.string,
      lookupCode: 'YP.WMS.DULL_PERIOD_TYPE',
      label: intl.get(`${modelPrompt}.sluggishType`).d('呆滞类型'),
    },
    {
      name: 'lotList',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.lot`).d('批次'),
      multiple: true,
    },
    {
      name: 'warehouseObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.warehouseObj`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('siteId');
        },
        lovPara: () => {
          return {
            tenantId,
            locatorCategories: 'AREA',
          };
        },
      },
    },
    {
      name: 'warehouseId',
      type: FieldType.number,
      bind: 'warehouseObj.locatorId',
    },
    {
      name: 'locatorObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.form.locatorCode`).d('货位编码'),
      lovCode: 'MT.MODEL.SUB_LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record?.get('siteId') || !record?.get('warehouseId') ;
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            locatorCategory: ["INVENTORY", "LOCATION"],
            locatorIds: [record.get('warehouseId')],
          };
        },
      },
    },
    {
      name: 'locatorId',
      type: FieldType.number,
      bind: 'locatorObj.locatorId',
    },
    {
      name: 'materialLotIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.materialLotIdentification`).d('物料批标识'),
    },
    {
      name: 'topContainerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.topContainerIdentification`).d('顶层容器标识'),
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.freezeFlag`).d('是否冻结'),
      lookupCode: 'MT.YES_NO',
    },
    {
      name: 'identifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.form.identifyType`).d('物料管理模式'),
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=IDENTITY_TYPE`,
      defaultValue: 'MATERIAL_LOT',
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          const myDatas = rows.filter((e) => e.typeCode !== 'MAT');

          return myDatas;
        },
      },
    },
    {
      name: 'inLocatorTimeFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.inLocatorTimeFrom`).d('入库时间从'),
      max: 'inLocatorTimeTo',
    },
    {
      name: 'inLocatorTimeTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.form.inLocatorTimeTo`).d('入库时间至'),
      min: 'inLocatorTimeFrom',
    },
  ],
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'sluggishType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sluggishType`).d('呆滞类型'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'primaryUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('单位'),
    },
    {
      name: 'sluggishDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sluggishDate1`).d('一级呆滞期'),
    },
    {
      name: 'dullPeriodSeconday',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dullPeriodSeconday`).d('二级呆滞期'),
    },
    {
      name: 'periodSeconday',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.periodSeconday`).d('呆滞天数'),
    },
    {
      name: 'receipeTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receipeTime`).d('收货时间'),
    },
    {
      name: 'inLocatorTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inLocatorTime`).d('入库日期'),
    },
    {
      name: 'shelfLifeUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shelfLifeUomCode`).d('时间单位'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'currentContainerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentContainerIdentification`).d('当前容器'),
    },
    {
      name: 'topContainerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerIdentification`).d('顶层容器'),
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('冻结标识'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/sluggish-material-lot/report/query/ui`,
        method: 'GET',
      };
    },
  },
});
