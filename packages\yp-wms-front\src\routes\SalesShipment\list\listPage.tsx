import React, { FC, useEffect } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import ExcelExport from 'components/ExcelExport';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { isNil } from 'lodash';
import notification from 'utils/notification';
import { API_HOST, BASIC } from '@utils/config';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import axios from 'axios';
import { onRowProps } from 'choerodon-ui/pro/lib/table/Table';
import listPageFactory from '../stores/listPage';
import listPageLineFactory from '../stores/listLinePage';

const tenantId = getCurrentOrganizationId();

const { Panel } = Collapse;

interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
  lineDs: DataSet;
}

const modelPrompt = 'tarzan.aps.salesShipment';

const ListPageComponent: FC<ListPageProps> = ({ listDs, lineDs }) => {
  useDataSetEvent(listDs, 'load', () => {
    console.log(listDs.toData());
    if (listDs.toData().length > 0) {
      const { saleOrderNum } = listDs.toData()[0];
      lineDs.queryParameter = {
        saleOrderNum,
      };
      lineDs.query();
    }else{
      lineDs.loadData([])
    }
    // setSelectedDetailLength(detailDs.selected.length);
  });

  useEffect(() => {
    listDs.query();
  }, []);

  const columns: ColumnProps[] = [
    {
      name: 'saleOrderNum',
      width: 180,
    },
    {
      name: 'saleOrderType',
      width: 120,
    },
    {
      name: 'status',
    },
    {
      name: 'message',
      width: 150,
    },
    {
      name: 'customCode',
    },
    {
      name: 'saleOrg',
    },
    {
      name: 'saleRouter',
    },
    {
      name: 'productGroup',
    },
  ];

  const columnsLine: ColumnProps[] = [
    {
      name: 'saleOrderLineNum',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'qty',
    },
    {
      name: 'plantCode',
    },
    {
      name: 'locatorCode',
    },
    {
      name: 'lot',
      width: 120,
    },
    {
      name: 'cancelFlag',
    },
    {
      name: 'creationDate',
      width: 150,
    },
    {
      name: 'createByName',
    },
    {
      name: 'lastUpdateDate',
      width: 150,
    },
    {
      name: 'lastUpdateByName',
    },
  ];

  const onHandleReturn = async () => {
    if (listDs.selected.length > 0) {
      const ids = listDs.selected.map(item => item.get('saleOrderNum'));
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wms-so-create/execute`;
      const res: any = await axios.post(url, ids);
      if (res && res.success) {
        notification.success({});
        listDs.query();
        lineDs.loadData([]);
      } else {
        notification.error({
          message: res.message,
        });
      }
    } else {
      notification.error({
        message: '请至少勾选一条采购需求',
      });
    }
  };

  const onRow = (props: onRowProps) => {
    return {
      onClick: () => {
        const saleOrderNum = props.record!.get('saleOrderNum');
        if (saleOrderNum) {
          lineDs.queryParameter = {
            saleOrderNum,
          };
          lineDs.query();
        }
      },
    };
  };

  const getExportQueryParams = () => {
    if (listDs.selected.length > 0) {
      const data = listDs.selected.map(ele => ele.get('saleOrderNum'));
      return {
        saleOrderNums: data,
      };
    }
    if (!listDs.queryDataSet || !listDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = listDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    });
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.salesShipment`).d('销售发运接口记录报表')}>
        <Button color={ButtonColor.primary} onClick={onHandleReturn}>
          {intl.get(`${modelPrompt}.button.purchaseRequest`).d('重新回传')}
        </Button>
        <ExcelExport
          method="GET"
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/wms-so-create/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.onTime.export`).d('查询导出')}
        />
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          queryBar={TableQueryBarType.filterBar}
          queryFieldsLimit={5}
          onRow={onRow}
          searchCode="salesShipment"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          customizedCode="salesShipment"
        />
        <Collapse bordered={false} collapsible="icon" defaultActiveKey={['basicInfo']}>
          <Panel
            header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
            key="basicInfo"
            dataSet={lineDs}
          >
            <Table
              dataSet={lineDs}
              columns={columnsLine}
              queryBar={TableQueryBarType.none}
              customizable
              virtual
              customizedCode="salesShipmentLine"
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    const lineDs = listPageLineFactory();
    return {
      listDs,
      lineDs,
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.aps.common', 'tarzan.aps.salesShipment'],
})(ListPage);
