import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.ass.inOutStorage';

const executeFactory = () =>
  new DataSet({
    primaryKey: 'inOutStorageId',
    selection: false,
    paging: false,
    autoQuery: false,
    forceValidate: true,
    fields: [
      {
        name: 'lineNumber',
        label: intl.get(`${modelPrompt}.form.line`).d('行号'),
        type: FieldType.string,
      },
      {
        name: 'materialCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
      },
      {
        name: 'materialName',
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
        type: FieldType.string,
      },
      {
        name: 'locatorCodeLov',
        label: intl.get(`${modelPrompt}.form.locatorCode`).d('执行仓库'),
        type: FieldType.object,
        required:true,
        lovCode: 'MT.MODEL.LOCATOR',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId: getCurrentOrganizationId(),
              locatorCategories: 'AREA',
              siteId: record.get('siteId')
            }
          },
        }

      },
      {
        name: 'locatorCode',
        type: FieldType.string,
        bind: 'locatorCodeLov.locatorCode'
      },
      {
        name: 'locatorId',
        type: FieldType.number,
        bind: 'locatorCodeLov.locatorId'
      },
      {
        name: 'quantity',
        label: intl.get(`${modelPrompt}.form.quantity`).d('需求数量'),
        type: FieldType.number,
      },
      {
        name: 'executeQuantity',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.form.executeQuantity`).d('已执行数量'),
      },
      {
        name: 'residueQty',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.form.residueQty`).d('剩余可执行数量'),
      },
      {
        name: 'qty',
        type: FieldType.number,
        required: true,
        label: intl.get(`${modelPrompt}.form.executeQty`).d('本次执行数量'),
        dynamicProps:{
          max:({record})=>Number(record?.get('quantity')||0)-Number(record?.get('executeQuantity')||0)
        }
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-network-demand/query/execute/data`,
        };
      },
    },
  });

export default executeFactory;
