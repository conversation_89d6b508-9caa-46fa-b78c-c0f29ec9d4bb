// 报废单查询
import React, { Component, Fragment } from 'react';
import { DataSet, Table, Button } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import { isNil, flow } from 'lodash';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import ExcelExport from 'components/ExcelExport';
import notification from 'utils/notification';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import { API_HOST, BASIC } from '@utils/config';
import { observer } from 'mobx-react';
import request from 'utils/request';
import { headTableDS, lineTableDS } from './stores/ScrapFormQueryDS';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.workshop.varianceAdjustment';
const Panel = Collapse.Panel;

@observer
@formatterCollections({ code: ['tarzan.workshop.varianceAdjustment', 'tarzan.common'] })
export default class ScrapFormQuery extends Component {
  constructor(props) {
    super(props);
    this.state = { loading: false, printLoading: false, selected: [] };
    this.headTableDs = new DataSet(headTableDS()); // 头表格dateSet
    this.lineTableDs = new DataSet(lineTableDS()); // 行表格dateSet
  }
  componentDidMount() {
    this.queryDeatil(true);
  }
  queryDeatil = (flag) => {

    // 列表交互监听
    if (this.headTableDs) {
      const handler = flag ? this.headTableDs.addEventListener : this.headTableDs.removeEventListener;
      // 头选中和撤销选中事件
      // 列表加载事件
      handler.call(this.headTableDs, 'batchSelect', this.handleLineTableChange);
      handler.call(this.headTableDs, 'batchUnSelect', this.handleLineTableChange);
    }
  };
  handleLineTableChange = () => {
    const newData = []
    this.headTableDs.selected.forEach((itme) => {
      newData.push(itme.data)
    })
    this.setState({ selected: newData })
  }
  // 查询行
  headerRowClick = record => {
    const varianceDocId = record.get('varianceDocId');
    this.lineTableDs.setQueryParameter('varianceDocId', varianceDocId);
    this.lineTableDs.query();
  };

  getExportQueryParams = () => {
    if (!this.headTableDs.queryDataSet || !this.headTableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = this.headTableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i]) || typeof queryParmas[i] === 'object') {
        delete queryParmas[i];
      }
    })
    delete queryParmas.__dirty;
    return queryParmas;
  };

  inventoryAdjustment = () => {
    this.setState({ loading: true });
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-inventory-variance-docs`, {
      method: 'POST',
      body: { varianceDocId: this.headTableDs.selected[0]?.get('varianceDocId') },
    }).then(res => {
      const response = getResponse(res)
      if (response) {
        notification.success({
          message: intl.get(`${modelPrompt}.notification.save.success`).d('保存成功!'),
        });
        this.headTableDs.query(this.headTableDs.currentPage)
      }
    }).finally(() => {
      this.setState({ loading: false });
    })
  }

  handlePrint = () => {
    console.log('---打印模板验证')
    this.setState({
      printLoading: true
    })
    return request(`/wms/v1/${tenantId}/wms-inventory-variance-docs/print`, {
      method: 'POST',
      body: this.headTableDs.selected.map(ele => ele.get('varianceDocId')),
      responseType: 'blob',
    }).then(res => {
      if (res.type === 'application/json') {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: '打印成功',
          });
        } else {
          notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
        }
      }
    }).finally(() => {
      this.setState({
        printLoading: false
      })
    })
  }

  render() {
    const headColumns = [
      { name: 'siteCode', align: 'center' },
      { name: 'flowNum', align: 'center' },
      { name: 'statusDescription', align: 'center' },
      { name: 'costcenterCode', align: 'center' },
      { name: 'requestByRealName', align: 'center' },
      { name: 'remark', align: 'center' },
      { name: 'createdRealName', align: 'center' },
      { name: 'creationDate', align: 'center' },
      { name: 'lastUpdatedRealName', align: 'center' },
      { name: 'lastUpdateDate', align: 'center' },
    ];
    const lineColumns = [
      { name: 'lineNum', align: 'center' },
      { name: 'materialCode', align: 'center' },
      { name: 'materialName', align: 'center' },
      { name: 'materialLotCode', align: 'center' },
      { name: 'lineStatus', align: 'center' },
      { name: 'qty', align: 'center' },
      { name: 'actualQty', align: 'center' },
      { name: 'adjustQty', align: 'center' },
      { name: 'adjustReason', align: 'center' },
      { name: 'createdRealName', align: 'center' },
      { name: 'creationDate', align: 'center' },
      { name: 'lastUpdatedRealName', align: 'center' },
      { name: 'lastUpdateDate', align: 'center' },
    ];
    return (
      <Fragment>
        <Header title={intl.get(`${modelPrompt}.title.parent`).d("盘点差异调整")}>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/wms-inventory-variance-docs/export`}
            queryParams={this.getExportQueryParams}
            buttonText={intl.get(`${modelPrompt}.button.export`).d('导出')}
          />
          <Button
            loading={this.state.loading}
            disabled={this.state.selected.length !== 1 || this.headTableDs.selected.some(r => r.get('status') !== 'TO_BE_ADJUSTEED')}
            onClick={this.inventoryAdjustment}>{intl.get(`${modelPrompt}.inventoryAdjustment`).d('盘点调整')}
          </Button>
          <Button
            type="c7n-pro"
            color={'primary'}
            icon="print"
            loading={this.state.printLoading}
            disabled={this.state.selected.length === 0}
            onClick={this.handlePrint}
          >
            {intl.get(`${modelPrompt}.button.printTemplate`).d('打印模板')}
          </Button>
        </Header>
        <Content>
          <Table
            dataSet={this.headTableDs}
            columns={headColumns}
            queryBar="filterBar"
            lineColumns
            queryBarProps={{
              fuzzyQuery: false,
            }}
            searchCode="headScrapFormQuery"
            customizedCode="headScrapFormQuery"
            onRow={({ record }) => {
              return {
                onClick: () => {
                  this.headerRowClick(record);
                },
              };
            }}
          />
          <Collapse defaultActiveKey={['1']}>
            <Panel header={intl.get(`${modelPrompt}.title.line`).d("行表格")} key="1">
              <Table
                dataSet={this.lineTableDs}
                columns={lineColumns}
                customizedCode="lineScrapFormQuery"
              />
            </Panel>
          </Collapse>
        </Content>
      </Fragment>
    );
  }
}
