/**
 * @feature 超期报表DS
 * @date 2022-10-17
 * <AUTHOR> <<EMAIL>>
 */
import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';

const modelPrompt = 'tarzan.hmes.report.overDue';
const tenantId = getCurrentOrganizationId();

const entranceDS = (): {
  dataKey: string; selection: string; queryFields: ({ name: string; lovCode: string; ignore: FieldIgnore; label: string; type: FieldType; lovPara: { tenantId: any }; required: boolean } | { bind: string; name: string } | { lookupCode: string; name: string; label: string; type: FieldType; lovPara: { tenantId: any }; valueField: string; textField: string } | { dynamicProps: { disabled: ({record}: { record: any }) => boolean; lovPara: ({record}: { record: any }) => { tenantId: any; siteId: any } }; name: string; lovCode: string; ignore: FieldIgnore; label: string; type: FieldType } | { bind: string; name: string } | { name: string; multiple: boolean; label: string; type: FieldType } | { dynamicProps: { disabled: ({record}: { record: any }) => boolean; lovPara: ({record}: { record: any }) => { locatorCategories: string; tenantId: any; siteId: any } }; name: string; lovCode: string; ignore: FieldIgnore; label: string; type: FieldType } | { bind: string; name: string } | { dynamicProps: { disabled: ({record}: { record: any }) => boolean; lovPara: ({record}: { record: any }) => { locatorCategories: string; tenantId: any; siteId: any } }; name: string; lovCode: string; ignore: FieldIgnore; label: string; type: FieldType } | { bind: string; name: string } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { max: string; name: string; label: string; type: FieldType } | { min: string; name: string; label: string; type: FieldType } | { name: string; options: DataSet; label: string; type: FieldType; valueField: string; textField: string } | { defaultValue: string; name: string; options: DataSet; label: string; type: FieldType; valueField: string; textField: string })[]; transport: { read: ({data}: { data: any }) => { method: string; data: any; url: string } }; totalKey: string; fields: ({ name: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { lookupCode: string; name: string; label: string; type: FieldType; valueField: string; textField: string } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType } | { name: string; label: string; type: FieldType })[]; primaryKey: string
} => ({
  primaryKey: 'uuid',
  selection: 'multiple',
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点编码'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'overdueType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overdueType`).d('超期类型'),
      lovPara: { tenantId },
      lookupCode: 'WMS.OVERDUE_TYPE',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'lotList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
      multiple: true,
    },
    {
      name: 'areaLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.areaLocatorCode`).d('仓库编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            locatorCategories: 'AREA',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'warehouseId',
      bind: 'areaLocatorLov.locatorId',
    },
    // 货位编码
    {
      name: 'inventoryLocatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位编码'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            locatorCategories: ["INVENTORY", "LOCATION"].join(','),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'locatorId',
      bind: 'inventoryLocatorLov.locatorId',
    },
    {
      name: 'materialLotIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotIdentification`).d('物料批标识'),
    },
    {
      name: 'topContainerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerTag`).d('顶层容器标识'),
    },
    {
      name: 'expirationDateStart',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startTime`).d('到期时间从'),
      max: 'expirationDateEnd',
    },
    {
      name: 'expirationDateEnd',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endTime`).d('到期时间至'),
      min: 'expirationDateStart',
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeStatus`).d('是否冻结'),
      options: new DataSet({
        data: [
          { value: 'Y', key: intl.get(`tarzan.common.label.yes`).d('是') },
          { value: 'N', key: intl.get(`tarzan.common.label.no`).d('否') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
    },
    {
      name: 'identifyType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.managementMode`).d('物料管理模式'),
      options: new DataSet({
        data: [
          { value: 'MATERIAL_LOT', key: intl.get(`${modelPrompt}.materialManagement`).d('实物管理') },
          { value: 'LOT', key: intl.get(`${modelPrompt}.lotManagement`).d('批次管理') },
        ],
      }),
      textField: 'key',
      valueField: 'value',
      defaultValue: 'MATERIAL_LOT',
    },
  ],
  fields: [
    {
      name: 'uuid',
      type: FieldType.string,
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('物料版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商描述'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'overdueType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overdueType`).d('超期类型'),
      lookupCode: 'WMS.OVERDUE_TYPE',
      textField: 'meaning',
      valueField: 'value',
    },
    {
      name: 'primaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'receipeTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.receipeTime`).d('收货时间'),
    },
    {
      name: 'productionDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
    },
    {
      name: 'expirationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.expirationDate`).d('到期日期'),
    },
    {
      name: 'shelfLife',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shelfLife`).d('保质期'),
    },
    {
      name: 'earlyWarningLeadTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.earlyWarningLeadTime`).d('预警提前期'),
    },
    {
      name: 'shelfLifeUomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shelfLifeUomCode`).d('时间单位'),
    },
    {
      name: 'expirationDayNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.expirationDayNumber`).d('超预警期/超期天数'),
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.storageLocator`).d('库位'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量状态'),
    },
    {
      name: 'currentContainerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.currentContainerIdentification`).d('当前容器'),
    },
    {
      name: 'topContainerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerIdentification`).d('顶层容器'),
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('冻结标识'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-overdue-material-lot-report/material-lot/ui`,
        method: 'GET',
        data: {
          ...data,
        },
      };
    },
  },
});

export { entranceDS };
