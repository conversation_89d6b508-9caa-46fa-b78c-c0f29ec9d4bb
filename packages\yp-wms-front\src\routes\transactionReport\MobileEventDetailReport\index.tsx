/**
 * @Description: 移动事件明细报表
 * @Author: <<EMAIL>>
 * @Date: 2022-10-17 15:24:51
 * @LastEditTime: 2023-05-08 14:09:32
 * @LastEditors: <<EMAIL>>
 */

import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { isNil } from 'lodash';
import { useDataSetEvent } from 'utils/hooks';
import ExcelExport from 'components/ExcelExport';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { API_HOST, BASIC } from '@utils/config';
import { tableDS } from './stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.mes.event.mobileEventDetailReport';

const MobileEventDetailReport = (props) => {

  const { tableDs } = props;

  useDataSetEvent(tableDs.queryDataSet, 'update', ({ name, record }) => {
    switch (name) {
      case 'sourceOrderType':
        record.set('sourceOrderLov', null);
        break;
      case 'sourceOrderLov':
        record.set('sourceOrderLineLov', null);
        break;
      default:
        break;
    }
  });

  // useDataSetEvent(tableDs, 'query', () => {
  //   // 由于查询头是「动态筛选条」在给ds.queryDataSet的字段重新赋值的时候，值变更会自动查询，所以会查询两遍
  //   const currentDate = new Date();
  //   currentDate.setMinutes(currentDate.getMinutes() + 1);
  //   tableDs!.queryDataSet!.current!.set('lastUpdateDateTo', currentDate);
  // });

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'eventId',
        width: 120,
      },
      {
        name: 'eventTypeCode',
        width: 120,
      },
      {
        name: 'eventTypeCodeDesc',
        width: 120,
      },
      {
        name: 'businessTypeCode',
        width: 120,
      },
      {
        name: 'businessTypeCodeDesc',
        width: 120,
      },
      {
        name: 'dtlFlag',
        width: 120,
      },
      {
        name: 'message',
        width: 150,
      },
      {
        name: 'siteCode',
        width: 150,
      },
      {
        name: 'locatorCode',
        width: 150,
      },
      {
        name: 'sourceSiteCode',
        width: 150,
      },
      {
        name: 'sourceLocatorCode',
        width: 150,
      },
      {
        name: 'materialCode',
        width: 150,
      },
      {
        name: 'revisionCode',
        width: 150,
      },
      {
        name: 'primaryUomQty',
        width: 150,
      },
      {
        name: 'primaryUomCode',
        width: 150,
      },
      {
        name: 'secondaryUomQty',
        width: 150,
      },
      {
        name: 'secondaryUomCode',
        width: 150,
      },
      {
        name: 'eventTime',
        width: 150,
      },
      {
        name: 'transAccount',
        width: 150,
      },
      {
        name: 'transReasonCode',
        width: 150,
      },
      {
        name: 'materialLotCode',
        width: 150,
      },
      {
        name: 'containerCode',
        width: 150,
      },
      {
        name: 'lot',
        width: 150,
      },
      {
        name: 'workcellCode',
        width: 150,
      },
      {
        name: 'prodLineCode',
        width: 150,
      },
      {
        name: 'sourceOrderType',
        width: 150,
      },
      {
        name: 'sourceOrder',
        width: 150,
      },
      {
        name: 'sourceOrderLineNum',
        width: 150,
      },
      {
        name: 'sourceOrderCompLineNum',
        width: 150,
      },
      {
        name: 'instructionDocTypeDesc',
        width: 150,
      },
      {
        name: 'instructionDocNum',
        width: 150,
      },
      {
        name: 'lineNumber',
        width: 150,
      },
      {
        name: 'instructionNum',
        width: 150,
      },
      {
        name: 'supplierCode',
        width: 150,
      },
      {
        name: 'supplierSiteCode',
        width: 150,
      },
      {
        name: 'customerCode',
        width: 150,
      },
      {
        name: 'customerSiteCode',
        width: 150,
      },
      {
        name: 'operationSequence',
        width: 150,
      },
      {
        name: 'rsnum',
        width: 150,
      },
      {
        name: 'rspos',
        width: 150,
      },
      {
        name: 'ownerTypeDesc',
        width: 150,
      },
      {
        name: 'ownerCode',
        width: 150,
      },
      {
        name: 'ownerLineCode',
        width: 180,
      },
      {
        name: 'sourceOwnerTypeDesc',
        width: 150,
      },
      {
        name: 'sourceOwnerCode',
        width: 150,
      },
      {
        name: 'sourceOwnerLineCode',
        width: 180,
      },
      {
        name: 'reservedObjectTypeDesc',
        width: 150,
      },
      {
        name: 'reservedObjectCode',
        width: 150,
      },
      {
        name: 'sourceReservedObjectTypeDesc',
        width: 150,
      },
      {
        name: 'sourceReservedObjectCode',
        width: 150,
      },
      {
        name: 'remark',
        width: 150,
      },
      {
        name: 'creationDate',
        width: 150,
      },
      {
        name: 'createdByName',
        width: 150,
      },
      {
        name: 'lastUpdateDate',
        width: 150,
      },
      {
        name: 'lastUpdatedByName',
        width: 150,
      },
      {
        name: 'attribute1',
        width: 150,
      },
      {
        name: 'attribute2',
        width: 150,
      },
      {
        name: 'attribute3',
        width: 150,
      },
      {
        name: 'attribute4',
        width: 150,
      },
      {
        name: 'attribute5',
        width: 150,
      },
    ];
  }, []);

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const queryParmas = tableDs.queryDataSet.current.toData();
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get('tarzan.mes.event.mobileEventDetailReport.title.list').d('移动事件明细报表')}>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-material-event-info/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={tableDs}
          columns={columns}
          searchCode="mobileEventDetailReport"
          customizedCode="mobileEventDetailReport"
        />
      </Content>
    </div>
  );
}

export default formatterCollections({
  code: ['tarzan.mes.event.mobileEventDetailReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(MobileEventDetailReport),
);
