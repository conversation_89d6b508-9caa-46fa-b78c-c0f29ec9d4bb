import React, { FC, useEffect, useState, } from 'react';
import { Header, Content } from 'components/Page';
import { Table, DataSet, Button, Menu, Dropdown, Modal, } from 'choerodon-ui/pro';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { RouteComponentProps } from 'react-router';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { BASIC } from '@utils/config';
import notification from 'utils/notification'
import { compact } from 'lodash';
import { useDataSetEvent } from 'utils/hooks';
import { getCurrentOrganizationId } from 'utils/utils';
import listPageFactory from '../stores/listPageDs';
import listLineFactory from '../stores/listLinePageDs';
import executeFactory from '../stores/excuteDs';
import { useDataSet } from 'utils/hooks';
import axios from 'axios';
import { Collapse } from 'choerodon-ui';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import { Action } from 'choerodon-ui/pro/lib/trigger/enum';
import request from 'utils/request';

const Panel = Collapse.Panel;
interface ListPageProps extends RouteComponentProps {
  listDs: DataSet;
  modalDs: DataSet;
  lineDs: DataSet;
}

const modelPrompt = 'tarzan.ass.demandManage';

const ListPageComponent: FC<ListPageProps> = ({ listDs, lineDs, history }) => {

  const [statuss, setStatuss] = useState<Array<string>>([])

  const executeModalDs = useDataSet(executeFactory, 'networkDemandexecute');

  useEffect(() => {
    listDs.query();
  }, []);

  useDataSetEvent(listDs, 'load', () => {
    if (listDs.toData().length > 0) {
      lineDs.setQueryParameter('instructionDocId', listDs.toData()[0].instructionDocId);
      lineDs.query()
    } else {
      lineDs.loadData([])
    }
    if (listDs.selected.length === 0) {
      setStatuss([])
    }
  });

  useDataSetEvent(listDs, 'select', () => {
    eventStatus()
  });

  useDataSetEvent(listDs, 'selectAll', () => {
    eventStatus()
  });

  useDataSetEvent(listDs, 'unSelectAll', () => {
    eventStatus()
  });

  useDataSetEvent(listDs, 'unselect', () => {
    eventStatus()
  });

  const eventStatus = () => {
    if (listDs.selected.length > 0) {
      const status = [...new Set(listDs.selected.map(item => {
        if (['RELEASED', 'PROCESSING'].includes(item.get('instructionDocStatus'))) {
          return item.get('instructionDocStatus')
        }
      }))]
      console.log(status)
      setStatuss(compact(status))
    } else {
      setStatuss([])
    }
  }

  const columns: ColumnProps[] = [
    {
      name: 'instructionDocNum',
      renderer: ({ record }) => {
        if (record?.get('instructionDocStatus') === 'RELEASED') {
          return <a onClick={() => { onHandleDetail(record?.get('instructionDocId')) }}>{record?.get('instructionDocNum')}</a>
        } else {
          return record?.get('instructionDocNum')
        }
      }
    },
    {
      name: 'locatorCode',
      width: 150
    },
    {
      name: 'instructionDocType',
    },
    {
      name: 'demandTime',
      width: 150
    },
    {
      name: 'instructionDocStatus',
    },
    {
      name: 'createdByName',
    },
    {
      name: 'lastUpdatedByName',
    },
    {
      name: 'lastUpdateDate',
      width: 150
    },
  ]

  const lineColumns: ColumnProps[] = [
    {
      name: 'lineNumber',
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'quantity',
    },
    {
      name: 'uomCode',
    },
    {
      name: 'lineStatus',
    },
    {
      name: 'lastUpdateDate',
    },
  ]

  const executeColumns: ColumnProps[] = [
    {
      name: 'lineNumber',
      width: 60
    },
    {
      name: 'materialCode',
    },
    {
      name: 'materialName',
    },
    {
      name: 'locatorCodeLov',
      editor: true
    },
    {
      name: 'quantity',
      width: 90
    },
    {
      name: 'executeQuantity',
    },
    {
      name: 'residueQty',
      width: 120,
      renderer: ({ record }) => {
        return Number(record?.get('quantity') || 0) - Number(record?.get('executeQuantity') || 0)
      }
    },
    {
      name: 'qty',
      width: 110,
      editor: true,
    },
  ]

  const onHandleDetail = (id) => {
    history.push(`/ass/demandManage/detail/${id}`)
  }

  // const handleCreate = () => {
  //   history.push(`/ass/demandManage/detail/create`)
  // }



  const headerRowClick = (record) => {
    lineDs.setQueryParameter('instructionDocId', record.get('instructionDocId'));
    lineDs.query();
  };


  const onHandleStatus = (key) => {
    if (listDs.selected.length > 0) {
      if (key.key === '2') {
        const flag = listDs.selected.every(item => item.get('instructionDocStatus') === 'PROCESSING')
        if (flag) {
          Modal.open({
            destroyOnClose: true,
            drawer: true,
            closable: true,
            keyboardClosable: true,
            children: <div>{intl.get(`${modelPrompt}.title.confirmClose`).d('该需求未完成，是否确认关闭?')}</div>,
            onOk: () => {
              onSubmit(key.key)
            }
          })
        } else {
          onSubmit(key.key)
        }
      } else {
        onSubmit(key.key)
      }
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.title.select`).d('请至少选择一条订单')
      })
    }
  }

  const onSubmit = async (key) => {
    const ids = listDs.selected.map(item => item.get('instructionDocId'))
    const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-demand-manage-platform/changeStatus`
    const res: any = await axios.post(url, { instructionDocIds: ids, status: key === '1' ? 'CANCEL' : 'CLOSED' })
    if (res && res.success) {
      notification.success({})
      listDs.query()
    } else {
      notification.error({
        message: res.message
      });
    }
  }

  const menu = (
    <Menu onClick={onHandleStatus}>
      {statuss[0] === 'RELEASED' && <Menu.Item key="1">
        {intl.get(`${modelPrompt}.title.cancel`).d('取消')}
      </Menu.Item>}
      {statuss[0] === 'PROCESSING' && <Menu.Item key="2">
        {intl.get(`${modelPrompt}.title.close`).d('关闭')}
      </Menu.Item>}
    </Menu>
  );

  const handleExecute = async () => {
    if (lineDs.selected.length > 0) {
      const ids = lineDs.selected.map(item => item.get('instructionDocLineId'))
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-demand-manage-platform/query/execute/data`
      const res: any = await request(url, {
        method: 'POST',
        body: { instructionDocLineIds: ids },
      })
      if (res && res.failed) {
        notification.error({
          message: res.message
        });
      } else {
        executeModalDs.loadData(res)
        Modal.open({
          title: '',
          key: 'executeModal',
          destroyOnClose: true,
          drawer: false,
          style: { width: '850px' },
          closable: true,
          okText: intl.get(`${modelPrompt}.title.execute`).d('执行'),
          children: <div style={{ height: '450px' }}>
            <Table
              dataSet={executeModalDs}
              columns={executeColumns}
              key="executeModal"
              queryBar={TableQueryBarType.none}
              queryFieldsLimit={4} // 头部显示的查询字段的数量
              searchCode="executeModal" // 动态筛选条后端接口唯一编码
              customizedCode="executeModal" // 个性化编码
            />
          </div>,
          onOk: async () => {
            const validate = await executeModalDs.validate()
            if (validate) {
              const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-demand-manage-platform/execute`;
              const res: any = await axios.post(url, executeModalDs.toData());
              if (res && (res.message || !res.success)) {
                notification.error({
                  message: res.message,
                });
                return false;
              } else {
                notification.success({});
                listDs.query();
              }
            } else {
              return false
            }
          }
        });
      }
    } else {
      notification.error({
        message: intl.get(`${modelPrompt}.title.select`).d('请至少勾选一条行订单')
      });
    }
  }


  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.demandManage`).d('售后需求管理')}>
        {/* <Button color={ButtonColor.primary} onClick={handleCreate}>{intl.get(`${modelPrompt}.title.create`).d('新建')}</Button> */}
        {/* <Dropdown trigger={[Action.click]} overlay={menu} placement={Placements.bottomCenter}>
          <Button disabled={statuss.length !== 1}>{intl.get(`${modelPrompt}.title.status`).d('状态变更')}</Button>
        </Dropdown> */}
        <Button  onClick={handleExecute}   >
          {intl.get(`${modelPrompt}.title.execute`).d('执行')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={listDs}
          columns={columns}
          key="demandManage"
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false, // 是否开启模糊查询
          }}
          onRow={({ record }) => {
            return {
              onClick: () => {
                headerRowClick(record);
              },
            };
          }}
          queryFieldsLimit={4} // 头部显示的查询字段的数量
          searchCode="demandManage" // 动态筛选条后端接口唯一编码
          customizedCode="demandManage" // 个性化编码
        />
        <Collapse bordered={false} defaultActiveKey={['1']} >
          <Panel header={intl.get(`${modelPrompt}.title.lineMessage`).d('行信息')} key="1">
            <Table
              dataSet={lineDs}
              columns={lineColumns}
              key="demandManageLine"
              queryBar={TableQueryBarType.none}
              queryFieldsLimit={4} // 头部显示的查询字段的数量
              searchCode="demandManageLine" // 动态筛选条后端接口唯一编码
              customizedCode="demandManageLine" // 个性化编码
            />
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
};

const ListPage = withProps(
  () => {
    const listDs = listPageFactory();
    const lineDs = listLineFactory();

    return {
      listDs,
      lineDs
    };
  },
  { cacheState: true },
)(ListPageComponent);
export default formatterCollections({
  code: ['tarzan.ass.demandManage'],
})(ListPage);
