import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import { BASIC } from '@utils/config';
import notification from 'utils/notification';

const Host = `${BASIC.HMES_BASIC}`
// const Host = '/yp-mes-20000'
const modelPrompt = 'tarzan.mes.event.badRecordPlatform';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  paging: false,
  autoCreate: true,
  selection: false,
  primaryKey: 'ncIncidentId',
  dataKey: 'rows',
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/wms-nc-incident/platform/detail/query/ui`,
        method: 'GET',
        transformResponse: val => {
          const { rows = {}, success, message } = JSON.parse(val);
          if (!success) {
            notification.error({
              message: message || intl.get('hzero.common.notification.error').d('操作失败'),
            });
          }
          return {
            ...rows,
            // disposalList,
          };
        },
      };
    },
  },
  fields: [
    {
      name: 'ncIncidentId',
      type: FieldType.number,
    },
    {
      name: 'ncIncidentNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncIncidentNum`).d('不良记录编码'),
      disabled: true,
    },
    {
      name: 'ncRecordType',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.ncRecordType`).d('不良记录类型'),
      lookupCode: 'WMS.NC_RECORD_TYPE',
      lovPara: { tenantId },
      valueField: 'value',
      textField: 'meaning',
      dynamicProps: {
        disabled: ({ record }) => record.get('ncIncidentId'),
      },
    },
    {
      name: 'ncIncidentStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncIncidentStatus`).d('不良记录状态'),
      disabled: true,
      textField: 'description',
      valueField: 'statusCode',
      defaultValue: 'NEW',
      lovPara: { tenantId },
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=NC_RECORD_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteName`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      textField: 'siteCode',
      ignore: FieldIgnore.always,
      required: true,
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => record.get('ncIncidentId'),
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteName',
      bind: 'siteLov.siteName',
    },
    {
      name: 'materialObj',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
      required: true,
      lovCode: 'MT.METHOD.MATERIAL',
      textField: 'materialCode',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId: getCurrentOrganizationId(),
            siteId: record?.get('siteId'),
          };
        },
        disabled: ({ record }) => !record?.get('siteId'),
      },
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.componentRevisionCode`).d('版本'),
      dynamicProps: {
        disabled: ({ record }) => !record?.get('siteId')|| !record?.get('materialId'),
      },
    },
    {
      name: 'materialId',
      type: FieldType.number,
      bind: 'materialObj.materialId',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      bind: 'materialObj.materialCode',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
      bind: 'materialObj.materialName',
    },
    {
      name: 'materialName',
      type: FieldType.string,
      bind: 'materialObj.materialName',
      disabled: true,
      label: intl.get(`${modelPrompt}.materialNameAndRevision`).d('物料名称/版本'),
    },
    {
      name: 'operationLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ncOperation`).d('不良工艺'),
      lovCode: 'MT.METHOD.OPERATION',
      textField: 'operationName',
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: () => ({
          tenantId,
        }),
        required: ({ record }) => record?.get('ncRecordType') === 'EO_ALL_NC',
      },
    },
    {
      name: 'operationId',
      bind: 'operationLov.operationId',
    },
    {
      name: 'operationName',
      bind: 'operationLov.operationName',
    },
    // {
    //   name: 'stage',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.stage`).d('阶段'),
    //   lookupCode: 'YP.QIS.NC_REPORT_STAGE',
    //   valueField: 'value',
    //   textField: 'meaning',
    //   required: true,
    // },
    // {
    //   name: 'quantityCharacter',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.quantityCharacter`).d('数量特性'),
    //   lookupCode: 'YP.QIS.NC_REPORT_QUANTITY_CHARACTER',
    //   valueField: 'value',
    //   textField: 'meaning',
    //   required: true,
    // },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
});

const ncDetailLineDS: () => DataSetProps = () => ({
  autoCreate: false,
  selection: false,
  dataKey: 'rows',
  primaryKey: 'ncRecordDetailId',
  paging: false,
  fields: [
    {
      name: 'ncRecordDetailId',
      type: FieldType.number,
    },
    {
      name: 'lineNumber',
      type: FieldType.number,
      label: intl.get('tarzan.common.label.serialNumber').d('序号'),
      defaultValue: 10,
    },
    {
      name: 'ncCodeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ncCode`).d('不良代码'),
    },
    {
      name: 'rootCauseWorkcellLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.rootCauseWorkcellName`).d('不良产生工作单元'),
      lovCode: 'MT.MODEL.WORKCELL',
      ignore: FieldIgnore.always,
      dynamicProps: {
        // disabled: ({ dataSet }) => !dataSet.parent?.current?.get('siteId'),
        lovPara: () => ({
          tenantId,
          workCellType: 'STATION',
        }),
      },
      textField: 'workcellName',
    },
    {
      name: 'rootCauseWorkcellName',
      bind: 'rootCauseWorkcellLov.workcellName',
    },
    {
      name: 'rootCauseWorkcellId',
      bind: 'rootCauseWorkcellLov.workcellId',
    },
    {
      type: FieldType.string,
      name: 'rootCauseEquipmentCode',
      label: intl.get(`${modelPrompt}.rootCauseEquipmentCode`).d('不良产生设备'),
    },
    {
      name: 'rootCauseEquipmentId',
    },
    {
      name: 'rootCauseOperationId',
    },
    {
      name: 'rootCauseOperationCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.rootCauseOperationCode`).d('不良产生工艺'),
    },
    {
      name: 'responsibleUserLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.responsibleUserName`).d('不良责任人'),
      lovCode: 'YP.QIS.USER.ORG',
      ignore: FieldIgnore.always,
      textField: 'realName',
      lovPara: { tenantId },
    },
    {
      name: 'responsibleUserId',
      bind: 'responsibleUserLov.userId',
    },
    {
      name: 'responsibleUserName',
      bind: 'responsibleUserLov.realName',
    },
    {
      name: 'responsibleApartmentLov',
      type: FieldType.object,
      lovCode: 'YP.QIS.COMPANY_UNIT',
      textField: 'unitName',
      label: intl.get(`${modelPrompt}.responsibleApartment`).d('不良责任部门'),
    },
    {
      name: 'unitId',
      bind: 'responsibleApartmentLov.unitId',
    },
    {
      name: 'unitCode',
      bind: 'responsibleApartmentLov.unitCode',
    },
    {
      name: 'unitName',
      bind: 'responsibleApartmentLov.unitName',
    },

    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
    {
      name: 'enclosure',
      type: FieldType.attachment,
      label: intl.get(`${modelPrompt}.enclosure`).d('附件'),
      bucketName: 'mes',
      bucketDirectory: 'nc-record-platform-file',
      accept: ['.deb', '.txt', '.pdf', 'image/*'],
      viewMode: 'popup',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${Host}/v1/${tenantId}/wms-nc-incident/platform/line/query/ui`,
        method: 'GET',
      };
    },
    submit: ({ data }) => {
      return {
        url: `${Host}/v1/${tenantId}/wms-nc-incident/platform/line/save/ui`,
        method: 'POST',
        data: data[0],
        transformResponse: response => {
          let parsedData;
          try {
            parsedData = JSON.parse(response);
          } catch (e) {
            // 不做处理，使用默认的错误处理
          }
          if (parsedData) {
            return [getResponse(parsedData)];
          }
        },
      };
    },
  },
});


const scanFormDS: () => DataSetProps = () => ({
  autoCreate: true,
  fields: [
    {
      name: 'materialLots',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'materialLotLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      lovCode: 'WMS.NC_MATERIAL_LOT',
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        // required: ({record}) => record?.get('ncRecordType') === 'RM_NC'&&!record?.get('materialLots'),
        lovPara: ({ record }) => ({
          tenantId,
          siteId: record?.get('siteId'),
          // ncRecordType: record?.get('ncRecordType'),
          materialId: record?.get('materialId'),
          // revisionCode: record?.get('revisionCode'),
        }),
      },
    },
    {
      name: 'materialLotId',
      bind: 'materialLotLov.materialLotId',
    },
    {
      name: 'ncRecordIdMaterialLot',
      bind: 'materialLotLov.ncRecordId',
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      bind: 'materialLotLov.materialLotCode',
      multiple: true,
    },
    // {
    //   name: 'eos',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    //   dynamicProps: {
    //     // required: ({record}) => record?.get('ncRecordType') === 'EO_ALL_NC'&&record?.get('eoId')&&record?.get('eoId').length===0,
    //   },
    // },
    // {
    //   name: 'eoLov',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    //   lovCode: 'YP_MES.MES.RECORD_EO',
    //   textField: 'eoNum',
    //   ignore: FieldIgnore.always,
    //   multiple: true,
    //   dynamicProps: {
    //     // disabled: ({ record }) => !record?.get('siteId'),
    //     // required: ({record}) => record?.get('ncRecordType') === 'EO_ALL_NC'&&!record?.get('eos'),
    //     lovPara: ({ record }) => ({
    //       tenantId,
    //       siteId: record?.get('siteId'),
    //       ncRecordType: record?.get('ncRecordType'),
    //       operationId: record?.get('operationId'),
    //       materialId: record?.get('materialId'),
    //       revisionCode: record?.get('revisionCode'),
    //     }),
    //   },
    // },
    // {
    //   name: 'eoId',
    //   bind: 'eoLov.eoId',
    // },
    // {
    //   name: 'eoNum',
    //   bind: 'eoLov.eoNum',
    // },
  ],
});

export { detailDS, ncDetailLineDS, scanFormDS };
