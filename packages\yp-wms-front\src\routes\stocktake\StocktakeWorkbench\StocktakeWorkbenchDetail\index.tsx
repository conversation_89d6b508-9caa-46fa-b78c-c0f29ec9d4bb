/**
 * @Description: 盘点工作台-详情页
 * @Author: <<EMAIL>>
 * @Date: 2022-02-09 10:47:34
 * @LastEditTime: 2023-05-18 16:10:35
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo, useEffect } from 'react';
import {
  Button,
  Form,
  TextField,
  Select,
  DataSet,
  Lov,
  Dropdown,
  Menu,
  SelectBox,
  Modal,
  Table,
  Row,
  Col,
  DateTimePicker,
} from 'choerodon-ui/pro';
import { Spin} from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { isEmpty } from 'lodash';
import intl from 'utils/intl';
import notification from 'utils/notification';
import {ButtonColor} from 'choerodon-ui/pro/lib/button/enum';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { ViewMode as SelectViewModel } from 'choerodon-ui/pro/lib/radio/enum';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentUser, getCurrentOrganizationId } from 'utils/utils';
import { ExpandCardC7n, drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
import {ColumnAlign} from "choerodon-ui/pro/lib/table/enum";
import ExcelExport from 'components/ExcelExport';
import {
  detailDS,
  locatorRangeTableDS,
  materialRangeTableDS,
  stocktakeDetailTableDS,
  stocktakeBarcodeDetailDS,
  inputLovDS,
} from '../stores/StocktakeWorkbenchDetailDS';
import LocatorRangeTable from './LocatorRangeTable';
import MaterialRangeTable from './MaterialRangeTable';
import StocktakeDetail from './StocktakeDetail';
import StockTakeBarcodeDrawer from './StocktakeDetail/StockTakeBarcodeDrawer';
import {
  FetchStocktageDetail,
  SaveStocktageDetail,
  BatchAddByAreaLocator,
  BatchAddByLocatorRange,
  ChangeStatus,
  CreateWarehouseSpace,
  // AdjustDiffer,
} from '../services';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.hmes.stocktake.stocktakeWorkbench';

// /**
//  * 取差集 arr1 - arr2
//  *
//  * @param {any[]} arr1
//  * @param {any[]} arr2
//  * @return {any[]}
//  */
// const getDefferenceSet = (arr1: any[], arr2: any[]) => {
//   return arr1.filter(x => arr2.every(y => y !== x));
// };
// 定义弹窗
let _modal;
let warehouseSpaceDrawer;

const MaterialSitePropertiesDetail = props => {
  const { history } = props;
  const { id } = props.match.params;
  const createFlag = useMemo(() => id === 'create', [id]);
  const {
    match: { path },
    customizeForm,
  } = props;

  const [canEdit, setCanEdit] = useState(false);
  const [selectedSiteId, setSelectedSiteId] = useState<number | null>(null);
  // 表格选中的行库位Ids
  const [selectedLocatorTableIds, setSelectedLocatorTableIds] = useState<number[]>([]);
  // 表格选中的行物料Ids
  const [selectedMaterialTableIds, setSelectedMaterialTableIds] = useState<number[]>([]);
  // Lov选中的仓库Id
  const [selectedAreaLocatorId, setSelectedAreaLocatorId] = useState<number | null>(null);
  // 盘点单的当前状态
  const [currentStocktakeStatus, setCurrentStocktakeStatus] = useState<string | null>(null);
  const [ barCodeExportQueryParams, setBarCodeExportQueryParams ] = useState<any>({});
  const [ detailExportQueryParams, setDetailExportQueryParams ] = useState<any>({});
  // 详情页展示的视图类型
  const [viewType, setViewType] = useState<'basicProperties' | 'stocktakeDetail'>(
    'basicProperties',
  );
  // 盘点明细表格选中的数据
  const [selectedStocktakeBarcodeList, setSelectedStocktakeBarcodeList] = useState<any[]>([]);
  // 获取盘点单数据
  const fetchStocktageDetail = useRequest(FetchStocktageDetail(), { manual: true });
  // 保存盘点单数据
  const saveStocktageDetail = useRequest(SaveStocktageDetail(), { manual: true });
  // 根据仓库添加仓库下的所有库位到库位范围
  const batchAddByAreaLocator = useRequest(BatchAddByAreaLocator(), { manual: true });
  // 根据库位批量新建物料数据
  const batchAddByLocatorRange = useRequest(BatchAddByLocatorRange(), { manual: true });
  // 盘点单状态变更
  const changeStatus = useRequest(ChangeStatus(), { manual: true });
  // 根据立库内货位新建
  const createWarehouseSpace = useRequest(CreateWarehouseSpace(), { manual: true });
  // 差异调整
  // const adjustDiffer = useRequest(AdjustDiffer(), { manual: true });

  const detailDs = useMemo(() => new DataSet({ ...detailDS() }), []);
  const locatorRangeTableDs = useMemo(() => new DataSet(locatorRangeTableDS()), []);
  const materialRangeTableDs = useMemo(() => new DataSet(materialRangeTableDS()), []);
  const stocktakeDetailTableDs = useMemo(() => new DataSet(stocktakeDetailTableDS()), []);
  const stocktakeBarcodeDetailDs = useMemo(() => new DataSet(stocktakeBarcodeDetailDS()), []);
  const inputLovDs = useMemo(() => new DataSet(inputLovDS()), []);

  useEffect(() => {
    if (id === 'create') {
      setCanEdit(true);
      detailDs.current!.set('stocktakeStatus', 'NEW');
      return;
    }
    initPageData();
  }, [id]);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 条码明细抽屉表格事件监听
  useEffect(() => {
    barcodeListener(true);
    return function clean() {
      barcodeListener(false);
    };
  }, [_modal]);

  // 生成行列表DS查询项
  const listener = flag => {
    // 表单交互件套
    if (detailDs) {
      const handler = flag ? detailDs.addEventListener : detailDs.removeEventListener;
      // 更新事件
      handler.call(detailDs, 'update', handleDetailDsChange);
      handler.call(detailDs, 'update', handleDetailDsChange);
    }
    // 列表交互监听
    if (locatorRangeTableDs) {
      const handler = flag
        ? locatorRangeTableDs.addEventListener
        : locatorRangeTableDs.removeEventListener;
      // 选中和撤销选中事件
      handler.call(locatorRangeTableDs, 'batchSelect', handleLocatorRangeTableChange);
      handler.call(locatorRangeTableDs, 'batchUnSelect', handleLocatorRangeTableChange);
    }
    if (materialRangeTableDs) {
      const handler = flag
        ? materialRangeTableDs.addEventListener
        : materialRangeTableDs.removeEventListener;
      // 选中和撤销选中事件
      handler.call(materialRangeTableDs, 'batchSelect', handleMaterialRangeChange);
      handler.call(materialRangeTableDs, 'batchUnSelect', handleMaterialRangeChange);
    }
  };

  const barcodeListener = flag => {
    if (stocktakeBarcodeDetailDs) {
      const handler = flag
        ? stocktakeBarcodeDetailDs.addEventListener
        : stocktakeBarcodeDetailDs.removeEventListener;
      // 选中和撤销选中事件
      handler.call(stocktakeBarcodeDetailDs, 'batchSelect', handleStocktakeBarcodeRangeChange);
      handler.call(stocktakeBarcodeDetailDs, 'batchUnSelect', handleStocktakeBarcodeRangeChange);
      // 查询事件
      handler.call(stocktakeBarcodeDetailDs, 'query', handleStocktakeBarcodeQuery);
    }
  };

  // 如果仓库和库位都被清空了，则清空物料范围
  const handleDetailDsChange = ({ record, name }) => {
    if (['areaLocatorLov', 'tableLocatorRangeLov'].includes(name)) {
      if (!record.get('areaLocatorId') && !record.get('locatorIds').length) {
        materialRangeTableDs.loadData([]);
        setSelectedMaterialTableIds([]);
        record.init('tableMaterialRangeLov', undefined);
      }
    }
  };

  // 库位范围表格行选中事件
  const handleLocatorRangeTableChange = ({ dataSet }) => {
    const _selectedTableLineIds: number[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLineIds.push(item.toData().locatorId);
    });
    setSelectedLocatorTableIds(_selectedTableLineIds);
  };

  // 物料范围表格行选中事件
  const handleMaterialRangeChange = ({ dataSet }) => {
    const _selectedTableLineIds: number[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLineIds.push(item.toData().locatorId);
    });
    setSelectedMaterialTableIds(_selectedTableLineIds);
  };

  // 条码明细表格行选中事件
  const handleStocktakeBarcodeRangeChange = ({ dataSet }) => {
    const _selectedTableLine: any[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLine.push(item.toData());
    });
    if (_modal) {
      _modal.update({
        title: renderDrawerTitle(_selectedTableLine),
      });
    }
  };

  const handleStocktakeBarcodeQuery = () => {
    if (_modal) {
      _modal.update({
        title: renderDrawerTitle([]),
      });
    }
  };

  const initPageData = () => {
    fetchStocktageDetail.run({
      params: {
        stocktakeId: id,
      },
      onSuccess: res => {
        const { locatorRangeList = [], materialRangeList = [], ...others } = res;
        detailDs.loadData([others]);
        if (locatorRangeList) {
          detailDs.current!.set('tableLocatorRangeLov', locatorRangeList);
          locatorRangeTableDs.loadData(locatorRangeList);
          stocktakeDetailTableDs.setQueryParameter(
            'locatorRangeIds',
            locatorRangeList.map(item => item.locatorId),
          );
        }
        if (materialRangeList) {
          detailDs.current!.set('tableMaterialRangeLov', materialRangeList);
          materialRangeTableDs.loadData(materialRangeList);
          stocktakeDetailTableDs.setQueryParameter(
            'materialRangeIds',
            materialRangeList.map(item => item.materialId),
          );
        }
        setCurrentStocktakeStatus(res.stocktakeStatus);
        stocktakeDetailTableDs.setQueryParameter('stocktakeId', id);
        stocktakeDetailTableDs.setQueryParameter('stocktakeStatus', res.stocktakeStatus);
        setDetailExportQueryParams(stocktakeDetailTableDs.queryParameter)
        stocktakeDetailTableDs.query();
      },
    });
  };

  const handleCancel = () => {
    if (id === 'create') {
      history.push('/hwms/inventory/inventory-workbench/list');
    } else {
      setCanEdit(prev => !prev);
      initPageData();
    }
  };

  const handleSave = async () => {
    const validate = await detailDs.validate();
    if (!validate) {
      return;
    }
    const { areaLocatorId, locatorIds = [] } = detailDs.current!.toData();
    if (!areaLocatorId && !locatorIds.length) {
      notification.warning({
        message: intl
          .get(`${modelPrompt}.atLeastOneIsRequired`)
          .d('仓库和库位范围二者至少必输其一'),
      });
      return;
    }
    saveStocktageDetail.run({
      params: {
        ...detailDs.current!.toData(),
        locatorRangeList: locatorRangeTableDs.toData(),
        materialRangeList: materialRangeTableDs.toData(),
      },
      onSuccess: res => {
        if (res.toString() === id) {
          initPageData();
        } else {
          history.push(`/hwms/inventory/inventory-workbench/detail/${res}`);
        }
        notification.success({});
        setSelectedSiteId(null);
        setSelectedAreaLocatorId(null);
        setSelectedLocatorTableIds([]);
        setSelectedMaterialTableIds([]);
        setCanEdit(false);
      },
    });
  };

  const handleChangeSite = values => {
    if (values) {
      setSelectedSiteId(values.siteId);
    } else {
      setSelectedSiteId(null);
    }
    setSelectedLocatorTableIds([]);
    setSelectedMaterialTableIds([]);
    setSelectedAreaLocatorId(null);
    detailDs.current!.init('areaLocatorLov', {});
    detailDs.current!.init('tableLocatorRangeLov', undefined);
    detailDs.current!.init('tableMaterialRangeLov', undefined);
    locatorRangeTableDs.loadData([]);
    materialRangeTableDs.loadData([]);
  };

  const handleChangeAreaLocator = values => {
    if (values) {
      setSelectedAreaLocatorId(values.locatorId);
    } else {
      setSelectedAreaLocatorId(null);
    }
  };

  const handleSelectedRange = (type: 'locator' | 'material', values) => {
    if (type === 'locator') {
      locatorRangeTableDs.batchUnSelect(locatorRangeTableDs.selected);
      setSelectedLocatorTableIds([]);
      locatorRangeTableDs.loadData([]);
      (values || []).forEach(item => {
        locatorRangeTableDs.create({ ...item, locatorTypeDesc: item.typeDesc });
      });
    } else {
      if (!detailDs.current!.get('areaLocatorId') && !locatorRangeTableDs.toData().length) {
        notification.warning({
          message: intl.get(`${modelPrompt}.locatorInNeed`).d('需要首先选择仓库或者库位'),
        });
        return;
      }
      materialRangeTableDs.batchUnSelect(materialRangeTableDs.selected);
      setSelectedMaterialTableIds([]);
      materialRangeTableDs.loadData([]);
      values.forEach(item => {
        materialRangeTableDs.create(item);
      });
    }
  };

  const deleteEnent = async (ds, type: 'locator' | 'material') => {
    // 删除 仓库范围/库位范围 Lov中记录的已选id
    if (type === 'locator') {
      const _selectedIds = ds.selected.map(item => {
        return item.toData().locatorId;
      });
      // 删除库位范围Lov的
      const _tableLocatorRangeLov = detailDs.current!.toData().tableLocatorRangeLov.filter(item => {
        return !_selectedIds.includes(item.locatorId);
      });
      detailDs.current!.set('tableLocatorRangeLov', _tableLocatorRangeLov);
      // 删除下方表格的记录
      await ds.delete(ds.selected, false);
      setSelectedLocatorTableIds([]);
      ds.batchUnSelect(ds.selected);
    } else {
      const _selectedIds = ds.selected.map(item => {
        return item.toData().materialId;
      });
      // 删除库位范围Lov的
      const _tableMaterialRangeLov = detailDs
        .current!.toData()
        .tableMaterialRangeLov.filter(item => {
          return !_selectedIds.includes(item.materialId);
        });
      detailDs.current!.set('tableMaterialRangeLov', _tableMaterialRangeLov);
      // 删除下方表格的记录
      await ds.delete(ds.selected, false);
      setSelectedMaterialTableIds([]);
      ds.batchUnSelect(ds.selected);
    }
  };

  const handleDeleteTableLine = (type: 'locator' | 'material') => {
    if (type === 'locator') {
      deleteEnent(locatorRangeTableDs, type);
    } else {
      deleteEnent(materialRangeTableDs, type);
    }
  };

  // 输入选择弹窗确定
  const handleToOkInputLov = () => {
    let data = inputLovDs.selected.map((item) => item.toData());
    if (data.length < 1) {
      data = inputLovDs.records.map((item) => item.toData());
    }
    const locatorCodes = data.map((ele) => ele.locator);
    createWarehouseSpace.run({
      params: locatorCodes,
      onSuccess: (res) => {
        if (!res.length) {
          notification.warning({
            message: intl.get(`${modelPrompt}.noLocatorInWarehouseSpace`).d('该立库货位下不存在库位！'),
          });
          return;
        }
        // 找出不存在库位范围内的新库位
        const _locatorTableIds = locatorRangeTableDs.toData().map((item: any) => item.locatorId);
        const _newLocatorList = res.filter(item => {
          return !_locatorTableIds.includes(item.locatorId);
        });
        warehouseSpaceDrawer.close();
        // 添加到库位范围表格内
        _newLocatorList.forEach(item => {
          locatorRangeTableDs.create(item);
        });
        // 添加到Lov中
        detailDs.current!.set(
          'tableLocatorRangeLov',
          detailDs.current!.get('tableLocatorRangeLov').concat(_newLocatorList),
        );
      },
    });
  };

  const handleToChangeInputLov = (value) => {
    if (!isEmpty(value)) {
      const data = value.split(/[\s\n]/).filter(item => item !== '');
      inputLovDs.loadData((data || []).map((item) => ({
        locator: item,
      })));
    }
  };

  // 选择弹窗的查询列
  const renderInputLovBar = () => {
    const { queryDataSet } = inputLovDs;
    return (
      <>
        <div
          style={{
            display: 'flex',
            marginBottom: '0.1rem',
            alignItems: 'flex-start',
            marginTop: '0.1rem',
          }}
        >
          <div style={{ marginTop: '0.1rem' }}>
            <Row>
              <Col span={24}>
                <Form dataSet={queryDataSet} labelWidth={100}>
                  <TextField name="locator" onChange={(value) => handleToChangeInputLov(value)} />
                </Form>
              </Col>
            </Row>
          </div>
          <div style={{ flexShrink: 0, marginTop: '20px', marginLeft: '20px' }}>
            <Button
              disabled={inputLovDs.data.length < 1}
              color={ButtonColor.primary}
              onClick={() => handleToOkInputLov()}
            >
              确定
            </Button>
          </div>
        </div>
      </>
    );
  };

  const handleCreateWarehouseSpace = () => {
    warehouseSpaceDrawer = Modal.open({
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.warehouseSpace`).d('立库内货位'),
      drawer: false,
      closable: true,
      footer: false,
      style: {
        width: 600,
      },
      children: (
        <>
          <Table
            dataSet={inputLovDs}
            queryBar={renderInputLovBar}
            style={{ height: '4rem' }}
            columns={[
              {
                name: 'locator',
                align: ColumnAlign.center,
              },
            ]}
          />
        </>
      ),
    });
  }

  const handleBatchAddByAreaLocator = () => {
    batchAddByAreaLocator.run({
      params: {
        areaLocatorId: selectedAreaLocatorId,
      },
      onSuccess: res => {
        if (!res.length) {
          notification.warning({
            message: intl.get(`${modelPrompt}.noLocotar`).d('该仓库下不存在库位！'),
          });
          return;
        }
        // 找出不存在库位范围内的新库位
        const _locatorTableIds = locatorRangeTableDs.toData().map((item: any) => item.locatorId);
        const _newLocatorList = res.filter(item => {
          return !_locatorTableIds.includes(item.locatorId);
        });
        // 添加到库位范围表格内
        _newLocatorList.forEach(item => {
          locatorRangeTableDs.create(item);
        });
        // 添加到Lov中
        detailDs.current!.set(
          'tableLocatorRangeLov',
          detailDs.current!.get('tableLocatorRangeLov').concat(_newLocatorList),
        );
      },
    });
  };

  const handleBatchAddByLocatorRange = () => {
    batchAddByLocatorRange.run({
      params: {
        locatorIds: detailDs!.current!.get('locatorIds').join(','),
        siteId: selectedSiteId,
      },
      onSuccess: res => {
        if (!res.length) {
          notification.warning({
            message: intl.get(`${modelPrompt}.noMaterial`).d('库位下不存在关联物料！'),
          });
          return;
        }
        // 找出不存在库位范围内的新库位
        const _materialTableIds = materialRangeTableDs.toData().map((item: any) => item.materialId);
        const _newMaterialList = res.filter(item => {
          return !_materialTableIds.includes(item.materialId);
        });
        // 添加到物料表格内
        _newMaterialList.forEach(item => {
          materialRangeTableDs.create(item);
        });
        // 添加到Lov中
        detailDs.current!.set(
          'tableMaterialRangeLov',
          detailDs.current!.get('tableMaterialRangeLov').concat(_newMaterialList),
        );
      },
    });
  };

  const handleChangeStatus = (sourceStatus: string | null, targetStatus: string) => {
    const stockTakeDocList = [
      { sourceStatus: detailDs.current!.get('stocktakeStatus'), stocktakeId: id },
    ];
    changeStatus.run({
      params: {
        // sourceStatus,
        stocktakeIds: [id],
        stockTakeDocList,
        targetStatus,
      },
      onSuccess: () => {
        notification.success({});
        initPageData();
      },
    });
  };

  const menu = (
    <Menu style={{ width: '100px' }}>
      <Menu.Item disabled={currentStocktakeStatus !== 'NEW'}>
        <a onClick={() => handleChangeStatus('NEW', 'RELEASED')}>
          {intl.get(`${modelPrompt}.released`).d('下达')}
        </a>
      </Menu.Item>
      <Menu.Item
        disabled={
          currentStocktakeStatus !== 'FIRSTSTCOUNTING' && currentStocktakeStatus !== 'RELEASED'
        }
      >
        <a onClick={() => handleChangeStatus('FIRSTSTCOUNTING, RELEASED', 'FIRSTCOUNTCOMPLETED')}>
          {intl.get(`${modelPrompt}.firstComplete`).d('初盘完成')}
        </a>
      </Menu.Item>
      <Menu.Item
        disabled={
          currentStocktakeStatus !== 'RELEASED' &&
          currentStocktakeStatus !== 'FIRSTSTCOUNTING' &&
          currentStocktakeStatus !== 'FIRSTCOUNTCOMPLETED' &&
          currentStocktakeStatus !== 'RECOUNTSTCOUNTING'
        }
      >
        <a onClick={() => handleChangeStatus('RELEASED, FIRSTSTCOUNTING, FIRSTCOUNTCOMPLETED, RECOUNTSTCOUNTING', 'COUNTCOMPLETED')}>
          {intl.get(`${modelPrompt}.actualComplete`).d('实盘完成')}
        </a>
      </Menu.Item>
      <Menu.Item disabled={currentStocktakeStatus !== 'COUNTCOMPLETED'}>
        <a onClick={() => handleChangeStatus('COUNTCOMPLETED', 'COMPLETED')}>
          {intl.get(`${modelPrompt}.completed`).d('完成')}
        </a>
      </Menu.Item>
      <Menu.Item>
        <a onClick={() => handleChangeStatus(null, 'CLOSED')}>
          {intl.get(`${modelPrompt}.closed`).d('关闭')}
        </a>
      </Menu.Item>
    </Menu>
  );

  // const handleAdjustDiffer = (list: any[]) => {
  //   adjustDiffer.run({
  //     params: {
  //       diffAdjustInfoList: list,
  //       stocktakeId: id,
  //     },
  //     onSuccess: () => {
  //       notification.success({});
  //       stocktakeBarcodeDetailDs.batchUnSelect(stocktakeBarcodeDetailDs.selected);
  //       stocktakeBarcodeDetailDs.query();
  //     },
  //   });
  // };

  const renderDrawerTitle = (list: any[]) => {
    let total = 0;
    let adjustNumber = 0; // 差异数绝对值相加
    let differNumber = 0; // 差异数相加
    let adjustByName = '';
    if (list.length) {
      total = list.length;
      list.forEach(item => {
        const { firstCountQty, reCountQty, currentQuantity } = item;
        if (!firstCountQty && !reCountQty) {
          // 没有进行初复的数据也是有差异的
          adjustNumber += Math.abs(currentQuantity);
          differNumber += -Math.abs(currentQuantity);
        } else {
          adjustNumber +=
            typeof item.reCountDiffQty === 'number'
              ? Math.abs(item.reCountDiffQty)
              : Math.abs(item.firstCountDiffQty);
          differNumber +=
            typeof item.reCountDiffQty === 'number' ? item.reCountDiffQty : item.firstCountDiffQty;
        }
      });
      adjustByName = getCurrentUser().realName;
    }
    return (
      <div className={styles['barcode-drawer-title-wapper']}>
        <div className={styles['barcode-drawer-title']}>
          {intl.get(`${modelPrompt}.barcodeDetail`).d('条码明细')}
        </div>
        <div className={styles['barcode-drawer-title-content']}>
          {list.length ? (
            <>
              <div className={styles['content-item']}>
                {`${intl.get(`${modelPrompt}.total`).d('合计')}: ${total}条`}
              </div>
              <div className={styles['content-item']}>
                {`${intl.get(`${modelPrompt}.adjustNumber`).d('调整数')}: ${adjustNumber}`}
              </div>
              <div className={styles['content-item']}>
                {`${intl.get(`${modelPrompt}.differNumber`).d('差异数')}: ${differNumber}`}
              </div>
              <div className={styles['content-item']}>
                {`${intl.get(`${modelPrompt}.adjustCountByName`).d('调整人')}: ${adjustByName}`}
              </div>
            </>
          ) : null}
          <ExcelExport
            method="POST"
            exportAsync
            fakePost
            requestUrl={`${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wms-stocktake-doc/bar-code/detail/export/ui`}
            queryParams={{
              ...barCodeExportQueryParams,
              ...stocktakeBarcodeDetailDs.queryDataSet?.toData()[0],
            }}
            buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
          />
          {/* <PermissionButton
            // loading={}
            disabled={!list.length}
            onClick={() => handleAdjustDiffer(list)}
            type="c7n-pro"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.differenceAdjust`).d('差异调整')}
          </PermissionButton> */}
        </div>
      </div>
    );
  };

  const handleOpenStocktakeBarcodeDrawer = () => {
    // 没有选中数据，查全部
    const _allBarcodeList = selectedStocktakeBarcodeList.length
      ? selectedStocktakeBarcodeList
      : stocktakeDetailTableDs.toData();
    const _stocktakeStatus = detailDs.current!.get('stocktakeStatus');
    stocktakeBarcodeDetailDs.setQueryParameter('stocktakeStatus', _stocktakeStatus);
    stocktakeBarcodeDetailDs.setQueryParameter('stocktakeId', detailDs.current!.get('stocktakeId'));
    if (_stocktakeStatus === 'NEW') {
      // 新建传这个
      stocktakeBarcodeDetailDs.setQueryParameter(
        'stockRanges',
        _allBarcodeList.map(item => {
          return {
            locatorId: item.locatorId,
            materialId: item.materialId,
            revisionCode: item.revisionCode,
          };
        }),
      );
    } else {
      // 除了新建以外传这个
      // let _stockActualIds: number[] = [];
      let locatorIds: number[] = [];
      let materialIds: number[] = [];
      _allBarcodeList.forEach(item => {
        locatorIds.push(item.locatorId);
        materialIds.push(item.materialId);
      });
      stocktakeBarcodeDetailDs.queryDataSet?.loadData([{materialIds:[...new Set(materialIds)],locatorIds:[...new Set(locatorIds)]}])
    }
    setBarCodeExportQueryParams(stocktakeBarcodeDetailDs.queryParameter)
    stocktakeBarcodeDetailDs.query();
    _modal = Modal.open({
      ...drawerPropsC7n({ canEdit, ds: stocktakeBarcodeDetailDs }),
      key: Modal.key(),
      title: renderDrawerTitle([]),
      style: {
        width: 1080,
      },
      children: (
        <StockTakeBarcodeDrawer
          stocktakeStatus={detailDs.current!.get('stocktakeStatus')}
          ds={stocktakeBarcodeDetailDs}
          selectedStocktakeBarcodeList={_allBarcodeList}
        />
      ),
      okButton: false,
      cancelText: intl.get('tarzan.common.button.back').d('返回'),
    });
  };

  return (
    <div className={`hmes-style ${styles['stocktake-workbench-page']}`}>
      <Spin
        spinning={
          fetchStocktageDetail.loading || saveStocktageDetail.loading || changeStatus.loading
        }
      >
        <Header
          title={intl.get(`${modelPrompt}.inventoryWorkbench`).d('盘点工作台')}
          backPath="/hwms/inventory/inventory-workbench/list"
        >
          {canEdit && (
            <>
              <Button color={ButtonColor.primary} icon="save" onClick={handleSave}>
                {intl.get('tarzan.common.button.save').d('保存')}
              </Button>
              <Button icon="close" onClick={handleCancel}>
                {intl.get('tarzan.common.button.cancel').d('取消')}
              </Button>
            </>
          )}
          {!canEdit && (
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="edit-o"
              onClick={() => {
                setCanEdit(prev => !prev);
                setViewType('basicProperties');
              }}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '详情页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </PermissionButton>
          )}
          <Dropdown
            overlay={menu}
            placement={Placements.bottomRight}
            disabled={createFlag || canEdit}
          >
            <PermissionButton
              type="c7n-pro"
              icon="cached"
              disabled={createFlag || canEdit}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.statusChange`).d('状态变更')}
            </PermissionButton>
          </Dropdown>
          {viewType === 'stocktakeDetail' && (
            <>
              <PermissionButton
                type="c7n-pro"
                icon="file-text"
                onClick={handleOpenStocktakeBarcodeDrawer}
                permissionList={[
                  {
                    code: `${path}.button.edit`,
                    type: 'button',
                    meaning: '列表页-编辑新建删除复制按钮',
                  },
                ]}
              >
                {intl.get(`${modelPrompt}.barcodeDetail`).d('条码明细')}
              </PermissionButton>
              <ExcelExport
                method="post"
                exportAsync
                fakePost
                requestUrl={`${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wms-stocktake-doc/detail/export/ui`}
                queryParams={detailExportQueryParams}
                buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
              />
            </>
          )}
        </Header>
        <Content>
          {!canEdit && (
            <SelectBox
              className={styles['select-box-view-type']}
              mode={SelectViewModel.button}
              value={viewType}
              onChange={value => {
                setViewType(value);
              }}
            >
              <SelectBox.Option value="basicProperties">
                {intl.get(`${modelPrompt}.basicInfo`).d('基础信息')}
              </SelectBox.Option>
              <SelectBox.Option value="stocktakeDetail">
                {intl.get(`${modelPrompt}.stocktakeDetail`).d('盘点明细')}
              </SelectBox.Option>
            </SelectBox>
          )}
          {viewType === 'basicProperties' ? (
            <>
              <ExpandCardC7n
                showExpandIcon
                title={intl.get(`${modelPrompt}.basicInfo`).d('基础信息')}
              >
                {customizeForm(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_DETAIL.BASIC`,
                  },
                  <Form labelWidth={112} disabled={!canEdit} dataSet={detailDs} columns={3}>
                    <TextField name="stocktakeNum" />
                    <Select name="openFlag" />
                    <Select name="stocktakeStatus" disabled={createFlag} />
                    <TextField name="identification" />
                    <Lov name="siteLov" onChange={handleChangeSite} noCache />
                    <Lov name="areaLocatorLov" onChange={handleChangeAreaLocator} noCache />
                    <TextField name="remark" />
                    <DateTimePicker name="scheduledStartTime" />
                    <DateTimePicker name="scheduledEndTime" />
                  </Form>,
                )}
              </ExpandCardC7n>
              <ExpandCardC7n
                showExpandIcon
                title={intl.get(`${modelPrompt}.locatorRange`).d('库位范围')}
                extra={
                  createFlag && (
                    <>
                      <Button
                        disabled={!selectedSiteId}
                        onClick={() => handleCreateWarehouseSpace()}
                      >
                        {intl.get(`${modelPrompt}.createWarehouseSpace`).d('根据立库内货位新建')}
                      </Button>
                      <Button
                        disabled={!selectedAreaLocatorId}
                        onClick={handleBatchAddByAreaLocator}
                      >
                        {intl.get(`${modelPrompt}.createByAreaLocator`).d('根据仓库批量新建')}
                      </Button>
                      <Button
                        disabled={!selectedLocatorTableIds.length}
                        onClick={() => handleDeleteTableLine('locator')}
                      >
                        {intl.get('tarzan.common.button.delete').d('删除')}
                      </Button>
                      <Lov
                        color={selectedSiteId ? ButtonColor.primary : ButtonColor.gray}
                        dataSet={detailDs}
                        name="tableLocatorRangeLov"
                        mode={ViewMode.button}
                        clearButton={false}
                        autoSelectSingle={false}
                        onChange={values => handleSelectedRange('locator', values)}
                        noCache
                      >
                        {intl.get('tarzan.common.button.create').d('新建')}
                      </Lov>
                    </>
                  )
                }
              >
                <LocatorRangeTable ds={locatorRangeTableDs} id={id} />
              </ExpandCardC7n>
              <ExpandCardC7n
                showExpandIcon
                title={intl.get(`${modelPrompt}.materialRange`).d('物料范围')}
                extra={
                  createFlag && (
                    <>
                      <Button
                        disabled={!locatorRangeTableDs.records.length}
                        onClick={handleBatchAddByLocatorRange}
                      >
                        {intl
                          .get(`${modelPrompt}.createByLocatorRange`)
                          .d('根据物料库位关系批量新增')}
                      </Button>
                      <Button
                        disabled={!selectedMaterialTableIds.length}
                        onClick={() => handleDeleteTableLine('material')}
                      >
                        {intl.get('tarzan.common.button.delete').d('删除')}
                      </Button>
                      <Lov
                        color={selectedSiteId ? ButtonColor.primary : ButtonColor.gray}
                        dataSet={detailDs}
                        name="tableMaterialRangeLov"
                        mode={ViewMode.button}
                        clearButton={false}
                        onChange={values => handleSelectedRange('material', values)}
                        noCache
                      >
                        {intl.get('tarzan.common.button.create').d('新建')}
                      </Lov>
                    </>
                  )
                }
              >
                <MaterialRangeTable ds={materialRangeTableDs} id={id} />
              </ExpandCardC7n>
            </>
          ) : (
            <StocktakeDetail
              ds={stocktakeDetailTableDs}
              stocktakeStatus={detailDs.current!.get('stocktakeStatus')}
              setSelectedStocktakeBarcodeList={setSelectedStocktakeBarcodeList}
            />
          )}
        </Content>
      </Spin>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.hmes.stocktake.stocktakeWorkbench', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_DETAIL.BASIC`],
    // @ts-ignore
  })(MaterialSitePropertiesDetail),
);
