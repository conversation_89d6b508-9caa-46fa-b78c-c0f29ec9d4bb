/**
 * @Description: 盘点工作台详情页-盘点明细Tab-条码明细drawer
 * @Author: <<EMAIL>>
 * @Date: 2022-02-14 16:05:10
 * @LastEditTime: 2023-01-10 11:00:49
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useEffect } from 'react';
import intl from 'utils/intl';
import { Table, DataSet } from 'choerodon-ui/pro';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { Badge, Tag } from 'choerodon-ui';
import expandTableBar from './ExpandTableBar';

export default ({ ds, stocktakeStatus, selectedStocktakeBarcodeList }) => {
  useEffect(() => {
    if (['NEW', 'RELEASED'].includes(stocktakeStatus)) {
      ds.queryDataSet.getField('diffFlag').set('disabled', true);
      ds.queryDataSet.getField('adjustFlag').set('disabled', true);
    }
    const materialList: any = [];
    const locatorList: any = [];
    // 将选中盘点明细行的物料和库位信息取出来，用作查询
    selectedStocktakeBarcodeList.forEach(item => {
      const _materialId = `${item.materialId}`;
      const _locatorId = `${item.locatorId}`;
      if (_materialId && !materialList.some(it => it.materialId === _materialId)) {
        materialList.push({
          materialId: _materialId,
          materialCode: item.materialCode,
        });
      }
      if (_locatorId && !locatorList.some(it => it.locatorId === _locatorId)) {
        locatorList.push({
          locatorId: _locatorId,
          locatorCode: item.locatorCode,
        });
      }
    });
    const materialDs = new DataSet({ paging: false });
    const locatorDs = new DataSet({ paging: false });
    materialDs.loadData(materialList);
    locatorDs.loadData(locatorList);
    ds.queryDataSet.getField('materialIds').set('options', materialDs);
    ds.queryDataSet.getField('locatorIds').set('options', locatorDs);
  }, []);

  const getColumns = () => {
    const columns: ColumnProps[] = [
      {
        name: 'stocktakeNum',
      },
      {
        name: 'identification',
        width: 120,
      },
      {
        name: 'lot',
        width: 120,
      },
      {
        name: 'materialCode',
        width: 120,
      },
      {
        name: 'materialName',
        width: 120,
      },
      {
        name: 'revisionCode',
        width: 120,
      },
      {
        name: 'locatorCode',
        width: 120,
      },
      {
        name: 'locatorName',
        width: 120,
      },
      {
        name: 'materialLotStatusDesc',
        width: 120,
      },
      {
        name: 'qualityStatusDesc',
        width: 120,
      },
      {
        name: 'currentQuantity',
        width: 120,
      },
      {
        name: 'uomCode',
        width: 120,
      },
    ];
    const dynamicColumns: ColumnProps[] = [
      {
        name: 'diffFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'adjustFlag',
        width: 120,
        align: ColumnAlign.center,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          />
        ),
      },
      {
        name: 'firstCountQty',
        width: 120,
      },
      {
        name: 'firstCountDiffQty',
        width: 120,
        renderer: ({ value }) => {
          return (
            value && <Tag color={value < 0 ? 'red' : 'green'}>{value}</Tag>
          );
        },
      },
      {
        name: 'reCountQty',
        width: 120,
      },
      {
        name: 'reCountDiffQty',
        width: 120,
        renderer: ({ value }) => {
          return (
            value && <Tag color={value < 0 ? 'red' : 'green'}>{value}</Tag>
          );
        },
      },
      {
        name: 'firstLocatorCode',
        width: 120,
      },
      {
        name: 'firstLocatorName',
        width: 120,
      },
      {
        name: 'reLocatorCode',
        width: 120,
      },
      {
        name: 'reLocatorName',
        width: 120,
      },
      {
        name: 'firstCountRemark',
        width: 120,
      },
      {
        name: 'firstCountByName',
        width: 120,
      },
      {
        name: 'firstCountDate',
        width: 120,
      },
      {
        name: 'reCountRemark',
        width: 120,
      },
      {
        name: 'reCountByName',
        width: 120,
      },
      {
        name: 'reCountDate',
        width: 120,
      },
      // {
      //   name: 'adjustQty',
      //   width: 120,
      // },
      // {
      //   name: 'adjustCountByName',
      //   width: 120,
      // },
    ];
    if (['NEW', 'RELEASED'].includes(stocktakeStatus)) {
      return columns;
    }
    return columns.concat(dynamicColumns);
  };

  const [currentColumns, setCurrentColumns] = useState(getColumns()); // 实际columns
  const changeColumn = val => setCurrentColumns(val);

  return (
    <Table
      key="materialLotId"
      columns={currentColumns}
      dataSet={ds}
      queryBar={expandTableBar({
        keyStr: 'stocktakeBarcodeList', // 全局唯一，后台查询的key
        className: 'stocktakeBarcodeList',
        lockString: '', // 固定列，无法修改
        columnProps: getColumns(),
        dataSet: ds,
        type: 'pro',
        changeColumn,
      })}
      virtual
      virtualCell
      style={{
        height: 720,
      }}
      pagination={{
        showPager: true, // 显示数字按钮
        pageSizeOptions: ['1000', '2000', '5000', '10000',],
      }}
    />
  );
};
