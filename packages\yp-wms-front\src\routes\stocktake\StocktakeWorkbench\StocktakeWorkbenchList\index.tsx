/**
 * @Description: 盘点工作台-列表页
 * @Author: <<EMAIL>>
 * @Date: 2022-02-08 16:50:04
 * @LastEditTime: 2023-05-18 16:09:44
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Table, Dropdown, Menu, Modal, Lov } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import ExcelExport from 'components/ExcelExport';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Placements } from 'choerodon-ui/pro/lib/dropdown/enum';
import notification from 'utils/notification';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { drawerPropsC7n } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { API_HOST, BASIC } from '@utils/config';
import { ViewMode } from "choerodon-ui/pro/lib/lov/enum";
import { tableDS, batchCreateDS, batchCreateTableDS, warehouseInventoryLovDS } from '../stores/StocktakeWorkbenchListDS';
import { ChangeStatus, BatchAdd, WarehouseInventory } from '../services';
import BatchCreateDrawer from './BatchCreateDrawer';
import { Action } from 'choerodon-ui/es/trigger/enum';

const modelPrompt = 'tarzan.hmes.stocktake.stocktakeWorkbench';
const tenantId = getCurrentOrganizationId();

function isAllEqualWithKeyWord(array: string[], keyWord: string[]) {
  if (array.length > 0) {
    return !array.some(value => {
      return !keyWord.includes(value);
    });
  }
  return false;
}

function getClosedDisabled(array: string[]) {
  if (array.length > 0) {
    return array.some(value => {
      return value === 'CLOSED';
    });
  }
  return true;
}

const ControlChartList = (props: any) => {
  const {
    tableDs,
    match: { path },
    customizeTable,
  } = props;

  const [loading, setLoading] = useState(false);
  const [selectedTableLineIds, setSelectedTableLineIds] = useState<number[]>([]);
  const [selectedTalbeLineStatus, setSelectedTalbeLineStatus] = useState<string[]>([]);
  const [selectedTableLines, setSelectedTableLines] = useState<object[]>([]);
  const warehouseInventory = useRequest(WarehouseInventory(), { manual: true });
  const changeStatus = useRequest(ChangeStatus(), { manual: true, needPromise: true });
  const batchAdd = useRequest(BatchAdd(), {
    manual: true,
    needPromise: true,
  });
  const batchCreateDs = useMemo(() => new DataSet(batchCreateDS()), []);
  const batchCreateTableDs = useMemo(() => new DataSet(batchCreateTableDS()), []);
  const warehouseInventoryLovDs = useMemo(() => new DataSet(warehouseInventoryLovDS()), []);

  useEffect(() => {
    tableDs.query(props.tableDs.currentPage);
  }, []);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (tableDs.queryDataSet) {
      const handler = flag
        ? tableDs.queryDataSet.addEventListener
        : tableDs.queryDataSet.removeEventListener;
      handler.call(tableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (tableDs) {
      const handler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(tableDs, 'batchSelect', handleLineTableChange);
      handler.call(tableDs, 'batchUnSelect', handleLineTableChange);
    }
  };

  // 切换站点时清空站点相关Lov
  const handleQueryDataSetUpdate = event => {
    if (event && event.name === 'siteLov') {
      event.record.init('areaLocatorLov', undefined);
      event.record.init('locatorRangeLov', undefined);
      event.record.init('materialRangeLov', undefined);
    }
    if (event && event.name === 'areaLocatorLov') {
      event.record.init('locatorRangeLov', undefined);
    }
  };

  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    const _selectedTableLineIds: number[] = [];
    const _selectedTableLineStatus: string[] = [];
    const _selectedTableLines: object[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLineIds.push(item.toData().stocktakeId);
      _selectedTableLineStatus.push(item.toData().stocktakeStatus);
      _selectedTableLines.push(item.toData());
    });
    setSelectedTableLineIds(_selectedTableLineIds);
    setSelectedTalbeLineStatus(_selectedTableLineStatus);
    setSelectedTableLines(_selectedTableLines);
    if (dataSet.selected.length === 1) {
      warehouseInventoryLovDs.getField('warehouseInventoryLov')?.set('lovPara', {
        tenantId,
        areaLocatorId: dataSet.selected[0].toData().areaLocatorId,
        locatorType: ['LOCATOR_BOTH', 'LOCATOR_OUT'],
      });
    }
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'stocktakeNum',
        width: 240,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return <a onClick={() => handleClickToDetailPage(record)}>{value}</a>;
        },
      },
      {
        name: 'identification',
        width: 240,
      },
      {
        name: 'remark',
        width: 240,
      },
      {
        name: 'stocktakeStatusDesc',
        width: 100,
        align: ColumnAlign.center,
      },
      {
        name: 'openFlag',
        width: 130,
        align: ColumnAlign.center,
        renderer: ({ value, record }) => {
          return (
            <Tag color={value === 'Y' ? 'orange' : 'blue'}>
              {record?.addField('openFlag').getText(value)}
            </Tag>
          );
        },
      },
      {
        name: 'siteCode',
        width: 200,
      },
      {
        name: 'areaLocatorCode',
        width: 200,
      },
      {
        name: 'createByName',
        width: 180,
      },
      {
        name: 'createDate',
        align: ColumnAlign.center,
        width: 180,
      },
      {
        name: 'lastUpdateByName',
        width: 180,
      },
      {
        name: 'lastUpdateDate',
        align: ColumnAlign.center,
        width: 180,
      },
    ],
    [],
  );

  const handleClickToDetailPage = record => {
    props.history.push(`/hwms/inventory/inventory-workbench/detail/${record.get('stocktakeId')}`);
  };

  const handleDrawerConfirm = async () => {
    const {
      openFlag,
      remark,
      siteId,
      areaLocatorIds,
      locatorIds,
    } = batchCreateDs.current!.toData();
    if (!(await batchCreateDs.validate())) {
      return false;
    }
    if (!batchCreateTableDs.toData().length) {
      notification.warning({
        message: intl.get(`${modelPrompt}.selectItemOperation`).d('请选择仓库和库位后再进行新建'),
      });
      return false;
    }
    return batchAdd
      .run({
        params: {
          openFlag,
          remark,
          siteId,
          areaLocatorIds,
          locatorIds,
          stocktakeStatus: 'NEW',
        },
      })
      .then(res => {
        if (res && res.success) {
          notification.success({});
          tableDs.query();
        } else {
          return Promise.resolve(false);
        }
      });
  };

  const handleBatchCreate = () => {
    batchCreateTableDs.loadData([]);
    Modal.open({
      ...drawerPropsC7n({ ds: batchCreateDs }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.batchCreate`).d('批量新建'),
      style: {
        width: 1080,
      },
      children: <BatchCreateDrawer formDs={batchCreateDs} tableDs={batchCreateTableDs} />,
      onOk: handleDrawerConfirm,
      okText: intl.get('tarzan.common.button.create').d('新增'),
    });
  };

  const handleCreate = () => {
    props.history.push('/hwms/inventory/inventory-workbench/detail/create');
  };

  const handleChangeStatus = async (sourceStatus: string | null, targetStatus: string, confirmExecuteFlag: string = 'N') => {
    setLoading(true)
    const stockTakeDocList = selectedTableLines.map((item: any) => {
      return {
        ...item,
        sourceStatus: item.stocktakeStatus,
      };
    });
    const res = await changeStatus.run({
      params: {
        // sourceStatus,
        stocktakeIds: selectedTableLineIds,
        stockTakeDocList,
        targetStatus,
        confirmExecuteFlag,
      },
    });
    if (!res?.rows) {
      notification.success({});
      tableDs.batchUnSelect(tableDs.selected);
      setSelectedTableLineIds([]);
      setSelectedTalbeLineStatus([]);
      tableDs.query();
    } else {
      Modal.confirm({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: (
          <p>
            {res?.rows || ''}
          </p>
        ),
      }).then(button => {
        if (button === 'ok') {
          handleChangeStatus(sourceStatus, targetStatus, 'Y');
        }
      });
    }
    setLoading(false)
  };

  const menu = (
    <Menu style={{ width: '100px' }}>
      <Menu.Item disabled={!isAllEqualWithKeyWord(selectedTalbeLineStatus, ['NEW'])}>
        <a onClick={() => handleChangeStatus('NEW', 'RELEASED')}>
          {intl.get(`${modelPrompt}.released`).d('下达')}
        </a>
      </Menu.Item>
      <Menu.Item
        disabled={!isAllEqualWithKeyWord(selectedTalbeLineStatus, ['FIRSTSTCOUNTING', 'RELEASED'])}
      >
        <a onClick={() => handleChangeStatus('FIRSTSTCOUNTING, RELEASED', 'FIRSTCOUNTCOMPLETED')}>
          {intl.get(`${modelPrompt}.firstComplete`).d('初盘完成')}
        </a>
      </Menu.Item>
      <Menu.Item
        disabled={
          !isAllEqualWithKeyWord(selectedTalbeLineStatus, [
            'RELEASED',
            'FIRSTSTCOUNTING',
            'FIRSTCOUNTCOMPLETED',
            'RECOUNTSTCOUNTING',
          ])
        }
      >
        <a
          onClick={() =>
            handleChangeStatus(
              'RELEASED, FIRSTSTCOUNTING, FIRSTCOUNTCOMPLETED, RECOUNTSTCOUNTING',
              'COUNTCOMPLETED',
            )
          }
        >
          {intl.get(`${modelPrompt}.actualComplete`).d('实盘完成')}
        </a>
      </Menu.Item>
      <Menu.Item disabled={!isAllEqualWithKeyWord(selectedTalbeLineStatus, ['COUNTCOMPLETED'])}>
        <a onClick={() => handleChangeStatus('COUNTCOMPLETED', 'COMPLETED')}>
          {intl.get(`${modelPrompt}.completed`).d('完成')}
        </a>
      </Menu.Item>
      <Menu.Item disabled={getClosedDisabled(selectedTalbeLineStatus)}>
        <a onClick={() => handleChangeStatus(null, 'CLOSED')}>
          {intl.get(`${modelPrompt}.closed`).d('关闭')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const getExportQueryParams = () => {
    const queryParams = tableDs.queryDataSet.current.toData();
    delete queryParams.locatorLov;
    delete queryParams.locatorRangeLov;
    delete queryParams.materialRangeLov;
    delete queryParams.siteLov;
    queryParams.stocktakeIds = selectedTableLineIds;
    return queryParams;
  };

  // 立库盘点出库
  const handleWarehouseInventory = (value) => {
    const selectLine = tableDs.selected[0].toData();
    if (value) {
      const paramsList = {
        stocktakeId: selectLine.stocktakeId,
        locatorId: value.locatorId,
        locatorCode: value.locatorCode,
        fromWarehouseCode: selectLine.areaLocatorCode,
        siteCode: selectLine.siteCode,
      };
      warehouseInventory.run({
        params: paramsList,
        onSuccess: () => {
          notification.success({});
          tableDs.batchUnSelect(tableDs.selected);
          setSelectedTableLineIds([]);
          setSelectedTalbeLineStatus([]);
          tableDs.query();
        },
      });

    }
    warehouseInventoryLovDs.reset();
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.inventoryWorkbench`).d('盘点工作台')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={handleCreate}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get('tarzan.common.button.create').d('新建')}
        </PermissionButton>
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={handleBatchCreate}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '列表页-编辑新建删除复制按钮',
            },
          ]}
        >
          {intl.get(`${modelPrompt}.batchCreate`).d('批量新建')}
        </PermissionButton>
        <Dropdown
          overlay={menu}
          trigger={[Action.click]}
          placement={Placements.bottomRight}
          disabled={!(selectedTableLineIds.length && !loading)}
        >
          <PermissionButton
            type="c7n-pro"
            icon="cached"
            disabled={!(selectedTableLineIds.length && !loading)}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.statusChange`).d('状态变更')}
          </PermissionButton>
        </Dropdown>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-stocktake-doc/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        <Lov
          mode={ViewMode.button}
          disabled={!(tableDs.selected.length === 1 && tableDs.selected[0].toData().stocktakeStatus === 'RELEASED')}
          dataSet={warehouseInventoryLovDs}
          name="warehouseInventoryLov"
          clearButton={false}
          onChange={(value) => handleWarehouseInventory(value)}
        >
          {intl.get(`${modelPrompt}.button.warehouseInventoryOutOfTheWarehouse`).d('立库盘点出库')}
        </Lov>
      </Header>
      <Content>
        {customizeTable(
          {
            filterCode: `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.LIST`,
          },
          <Table
            queryBar={TableQueryBarType.filterBar}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            dataSet={tableDs}
            columns={columns}
            searchCode="pdgzt1"
            customizedCode="pdgzt1"
          />,
        )}
      </Content>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.QUERY`,
      `${BASIC.CUSZ_CODE_BEFORE}.STOCKTAKE_LIST.LIST`,
    ],
  }),
  formatterCollections({ code: ['tarzan.hmes.stocktake.stocktakeWorkbench', 'tarzan.common'] }),
)(ControlChartList);
