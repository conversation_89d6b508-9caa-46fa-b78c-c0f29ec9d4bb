import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.ass.demandManage';

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'instructionDocId',
    selection: DataSetSelection.multiple,
    paging: true,
    autoQuery: false,
    forceValidate: true,
    events: {
      update({ name, record }) {
        if (name === 'siteCode' && !record.get('siteCode')) {
          record.set('locatorCode', undefined)
        }
      },
    },
    fields: [
      {
        name: 'instructionDocNum',
        disabled: true,
        label: intl.get(`${modelPrompt}.form.instructionDocNum`).d('需求单号'),
        type: FieldType.string,
      },
      {
        name: 'instructionDocType',
        type: FieldType.string,
        required: true,
        lookupCode: 'WMS.DEMAND_MANAGE_TYPE_WMS',
        label: intl.get(`${modelPrompt}.form.instructionDocType`).d('需求类型'),
      },
      {
        name: 'siteCode',
        type: FieldType.object,
        ignore: FieldIgnore.always,
        required: true,
        label: intl.get(`${modelPrompt}.form.siteCode`).d('站点'),
        lovCode: 'MT.MODEL.SITE',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'siteId',
        type: FieldType.number,
        dynamicProps: {
          bind: ({ record }) => {
            if (record.get('siteCode') && record.get('siteCode').siteId) {
              return 'siteCode.siteId'
            }
          }
        }
      },
      {
        name: 'locatorCode',
        required: true,
        label: intl.get(`${modelPrompt}.form.locatorCode`).d('需求仓库'),
        type: FieldType.object,
        ignore: FieldIgnore.always,
        lovCode: 'MT.MODEL.LOCATOR',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              tenantId,
              locatorCategories: 'AREA',
              siteId: record.get('siteId')
            }
          },
        }

      },
      {
        name: 'locatorId',
        type: FieldType.string,
        bind: 'locatorCode.locatorId'
      },
      {
        name: 'demandTime',
        required: true,
        type: FieldType.dateTime,
        label: intl.get(`${modelPrompt}.form.demandTime`).d('需求日期'),
      },
      {
        name: 'instructionDocStatus',
        type: FieldType.string,
        lookupCode: 'ASS.INSTRUCTION_DOC_STATUS',
        defaultValue: 'RELEASED',
        disabled: true,
        label: intl.get(`${modelPrompt}.form.instructionDocStatus`).d('需求状态'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-demand-manage-platform/detail`,
          // transformResponse: value => {
          //   let listData: any = {};
          //   try {
          //     listData = JSON.parse(value);
          //   } catch (err) {
          //     listData = {
          //       message: err,
          //     };
          //   }
          //   if (!listData.success) {
          //     return {
          //       ...listData,
          //       failed: true,
          //     };
          //   }
          //   return listData;
          // },
        };
      },
    },
  });

export default listPageFactory;
