/**
 * @Description: 库存调拨平台 - 详情页
 * @Author: <EMAIL>
 * @Date: 2022/3/8 10:12
 * @LastEditTime: 2023-05-18 16:22:21
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useMemo, useState } from 'react';
import request from 'utils/request';
import {
  DataSet,
  DateTimePicker,
  Form,
  Lov,
  NumberField,
  Select,
  Switch,
  Table,
  TextField,
  Button,
} from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import { observer } from 'mobx-react-lite';
import { Badge, Collapse, Popover, Icon, Popconfirm } from 'choerodon-ui';
import { Content, Header } from 'components/Page';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
import intl from 'utils/intl';
// import moment from 'moment';
import formatterCollections from 'utils/intl/formatterCollections';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC } from '@utils/config';
// import request from 'utils/request';
// import { getCurrentOrganizationId } from 'utils/utils';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { headerTableDS, lineTableDS } from '../stores/DeliveryDetailDS';
import { QueryRevision, HandleSaveInstruction, GetToleranceInfo, AvailableQuantity } from '../services';

const { Panel } = Collapse;
// const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.customerSuppliedParts';

const customerSuppliedParts = observer(props => {
  const {
    match: {
      path,
      params: { id },
    },
    customizeForm,
    customizeTable,
  } = props;
  const tableDs = useMemo(() => new DataSet(lineTableDS()), []);
  const formDs: DataSet = useMemo(
    () =>
      new DataSet({
        ...headerTableDS(),
      }),
    [],
  );
  const [headerState, setHeaderState] = useState(''); // 用于存储头上的指令单据状态
  const [revisionList, setRevisionList] = useState<any>([]);
  const [canEdit, setCanEdit] = useState(false); // "编辑"按钮标识
  const createPage = useMemo(() => id === 'create', []); // “新建”标识
  const [lineAdd, setLineAdd] = useState(false); // 当头上必输项全部输入时行上可新增
  const [isSubmit, setIsSubmit] = useState(false); // 是否保存成功标识
  const [toleranceInfo, setToleranceInfo] = useState<any>({}); // 允差信息
  // const [lineLength, setLineLength] = useState<number>(0); // 用于存储行上的数据条数
  const [permissionFlag, setPermissionFlag] = useState<boolean>(true); // 用于判断当前界面是否有编辑权限

  const { run: handleSaveInstruction, loading: changeSaveLoading } = useRequest(
    HandleSaveInstruction(),
    {
      manual: true,
      needPromise: true,
    },
  );

  const { run: getToleranceInfo } = useRequest(GetToleranceInfo(), {
    manual: true,
    needPromise: true,
  });

  const availableQuantity = useRequest(AvailableQuantity(), {
    manual: true,
  });
  const { run: queryRevision } = useRequest(QueryRevision(), {
    manual: true,
    needPromise: true,
  });


  useEffect(() => {
    if (id !== 'create') {
      queryDetail();
    } else {
      setCanEdit(true);
    }
  }, [id]);

  const queryDetail = async () => {
    formDs.setQueryParameter('instructionDocId', id);
    formDs.query().then(res => {
      if (res.success) {
        setLineAdd(true);
        const content = res.rows || {};
        const { deliveryDocLineList = [] } = content;
        setHeaderState(content.deliveryDoc.instructionDocStatus);
        // 当行上有某行没有库位权限时，界面不可编辑
        if (deliveryDocLineList.length) {
          deliveryDocLineList.forEach(item => {
            if (!item.permissionFlag || item.permissionFlag === 'N') {
              setPermissionFlag(false);
            }
          });
          tableDs.loadData(deliveryDocLineList);
        }
        // 进入详情界面时查允差信息并存到state里面
        return getToleranceInfo({
          params: {
            instructionDocType: content?.deliveryDoc?.instructionDocType,
            lovCode: 'MT.SEND_RECEIVE_EXECUTE_DOC_TYPE',
          },
        }).then(response => {
          if (response && response.success) {
            setToleranceInfo({ ...response.rows[0] });
          }
        });
      }
    });
  };

  // 取消
  const handleCancel = () => {
    if (id !== 'create') {
      queryDetail();
      setCanEdit(false);
    } else {
      props.history.push({
        pathname: `/hwms/customer-supplied-parts/list`,
        state: {
          queryFlag: isSubmit,
        },
      });
    }
  };

  // 返回
  const handleBack = () => {
    props.history.push({
      pathname: `/hwms/customer-supplied-parts/list`,
      state: {
        queryFlag: isSubmit,
      },
    });
  };

  // 保存
  const handleSave = async () => {
    const headerData: any = formDs.toData()[0];
    const lines: any = tableDs.toData();
    // 保存时校验单据行，没有时报错
    if (lines.length === 0) {
      notification.warning({
        description: intl
          .get(`${modelPrompt}.notification.lineLengthIsZero`)
          .d('未创建单据行，请检查！'),
      });
      return;
    }
    const lineRequired = await tableDs.validate();
    if (!lineRequired) {
      return;
    }
    formDs.current!.set('newDate', new Date());
    tableDs.current!.set('newDate', new Date());
    getResult().then(response => {
      if (response) {
        // 过滤不必要的入参
        const _lines: any = [];
        lines.forEach(item => {
          const _lineItem = {};
          Object.keys(item).forEach(i => {
            if (!i.includes('Lov')) {
              _lineItem[i] = item[i];
            }
          });
          _lines.push({ ..._lineItem });
        });
        const _header = {};
        Object.keys(headerData).forEach(i => {
          if (!i.includes('Lov')) {
            _header[i] = headerData[i];
          }
        });
        return handleSaveInstruction({
          params: {
            mtInstructionDocDTO2: _header,
            instructionList: _lines,
          },
        }).then(res => {
          if (res && res.success) {
            notification.success({});
            setCanEdit(false);
            setLineAdd(false);
            setIsSubmit(true);
            if (id === 'create') {
              props.history.push(`/hwms/customer-supplied-parts/detail/${res.rows}`);
            } else {
              queryDetail();
            }
          }
        });
      }
    });
  };

  const getResult = async () => {
    const validate = await formDs.validate(false, true);
    const pointValidate = await tableDs.validate();
    return validate && pointValidate;
  };

  // 新增
  const handleAddLine = () => {
    const listData = tableDs.toData();
    let maxNumber = 0;
    listData.forEach(item => {
      const { lineNumber } = item as any;
      if (lineNumber && lineNumber > maxNumber) {
        maxNumber = lineNumber;
      }
    });
    tableDs.create(
      {
        lineNumber: Math.floor(maxNumber / 10) * 10 + 10,
        toleranceFlag: toleranceInfo.toleranceFlag,
        toleranceType: toleranceInfo.toleranceType,
        toleranceTypeDesc: toleranceInfo.toleranceTypeDesc,
        toleranceMaxValue: toleranceInfo.toleranceMaxValue,
        toleranceMinValue: toleranceInfo.toleranceMinValue,
        fromLocatorRequiredFlag: toleranceInfo.fromLocatorRequiredFlag,
        toLocatorRequiredFlag: toleranceInfo.toLocatorRequiredFlag,
        instructionTypeTag: tableDs?.current?.toData().instructionTypeTag,
        customerCode: formDs.current?.get('customerCode'),
      },
      0,
    );
    // setLineLength(tableDs.toData().length);
  };

  // 删除按钮的回调
  const deleteRecord = record => {
    tableDs.remove(record);
    // setLineLength(tableDs.toData().length);
  };

  // 行上“允差标识”Switch变化的回调
  const handleToleranceChange = () => {
    tableDs!.current!.init('toleranceType');
    tableDs!.current!.init('toleranceMaxValue');
    tableDs!.current!.init('toleranceMinValue');
  };

  // 行上“允差类型”Select变化的回调
  const handleToleranceTypeChange = () => {
    tableDs!.current!.init('toleranceMaxValue');
    tableDs!.current!.init('toleranceMinValue');
  };

  // 获取管理模式
  const getIdentifyType = record => {
    record.set('identifyType', null);
    const value = record.toData();
    availableQuantity.run({
      params: {
        materialId: value.materialId,
        siteId: formDs?.current?.toData().siteId,
        organizationId: value.toLocatorId,
      },
      onSuccess: res => {
        record.set('identifyType', res.identifyType);
      },
      onFailed: () => {
        record.set('identifyType', null);
      },
    });
    request(`${BASIC.TARZAN_METHOD}/v1/${getCurrentOrganizationId()}/mt-material/site-material/limit/lov/ui`, {
      method: 'get',
      query: {
        tenantId: getCurrentOrganizationId(),
        siteIds: formDs.current?.get('siteId'),
        materialId: record?.get('materialId'),
      },
    }).then(res => {
      if (res && res.success) {
        const newRes = res?.rows.map(item => ({value: item, meaning: item}))
        record.getField('revisionCode').set('options', new DataSet({
          data: newRes,
        }))
        record.getField('revisionCode')?.set('required', newRes.length > 0);
      }else{
        record.getField('revisionCode')?.set('required', false);
        record.getField('revisionCode').set('options', new DataSet({
          data: [],
        }))
      }
    });
  };

  // 头上“调拨单类型”变化回调
  const handleTypeChange = async val => {
    if (val && val !== '') {
      return getToleranceInfo({
        params: {
          instructionDocType: val.value,
          lovCode: 'MT.SEND_RECEIVE_EXECUTE_DOC_TYPE',
        },
      }).then(res => {
        if (res && res.success) {
          setToleranceInfo({ ...res.rows[0] });
          // 新增界面--切换类型时需要更新行上的允差信息
          const lineList = tableDs.toData();
          if (lineList.length > 0) {
            const newLine: any = [];
            lineList.forEach((item: any) => {
              const _item = {
                ...item,
                toleranceFlag: res.rows[0].toleranceFlag,
                toleranceType: res.rows[0].toleranceType,
                toleranceTypeDesc: res.rows[0].toleranceTypeDesc,
                toleranceMaxValue: res.rows[0].toleranceMaxValue,
                toleranceMinValue: res.rows[0].toleranceMinValue,
                fromLocatorRequiredFlag: res.rows[0].fromLocatorRequiredFlag,
                toLocatorRequiredFlag: res.rows[0].toLocatorRequiredFlag,
              };
              newLine.push(_item);
            });
            tableDs.loadData(newLine);
          }
        }
      });
    }
  };

  const lineTableColumns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          disabled={!(canEdit && lineAdd)}
          onClick={handleAddLine}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <Popconfirm
          title={intl.get(`tarzan.common.message.confirm.delete`).d('是否确认删除?')}
          onConfirm={() => deleteRecord(record)}
          okText={intl.get('tarzan.common.button.confirm').d('确认')}
          cancelText={intl.get('tarzan.common.button.cancel').d('取消')}
        >
          <PermissionButton
            type="c7n-pro"
            icon="remove"
            disabled={record?.get('instructionDocLineId')}
            funcType="flat"
            shape="circle"
            size="small"
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          />
        </Popconfirm>
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'lineNumber',
      align: ColumnAlign.left,
      width: 100,
    },
    {
      name: 'identifyType',
      renderer: ({ record }) => {
        return record?.get('identifyType')?(record?.get('identifyType') === 'MATERIAL_LOT'?
          intl.get(`${modelPrompt}.physicalManagement`).d('实物管理'):
          intl.get(`${modelPrompt}.nonPhysicalManagement`).d('非实物管理')):''
      },
    },
    {
      name: 'materialLov',
      width: 150,
      renderer: ({record}) => record?.get('materialCode'),
      editor: record => canEdit && record?.status === 'add' && <Lov onChange={() => getIdentifyType(record)} />,
    },
    {
      name: 'revisionCode',
      width: 140,
      editor: record => canEdit && record?.status === 'add' && <Select />,
    },
    {
      name: 'materialName',
      width: 140,
    },
    {
      name: 'quantity',
      width: 140,
      editor: (record) => canEdit && record?.status === 'add',
    },
    {
      name: 'uomCode',
    },
    {
      name: 'wareHouseLov',
      width: 140,
      editor: record => canEdit && <Lov onChange={() => getIdentifyType(record)} />,
    },
    {
      name: 'urgentFlag',
      align: ColumnAlign.center,
      editor: record => !record?.get('instructionDocLineId') && record?.status === 'add' && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: ColumnAlign.center,
      editor: record =>
        !record?.get('instructionDocLineId') &&
        record?.status === 'add' && <Switch onChange={handleToleranceChange} />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      width: 140,
      editor: record =>
        !record?.get('instructionDocLineId') &&
        record?.get('toleranceFlag') === 'Y' &&
        record?.status === 'add' && <Select onChange={handleToleranceTypeChange} />,
      // renderer: ({ record }) => record?.get('toleranceTypeDesc'),
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
      align: ColumnAlign.right,
      editor: record =>
        !record?.get('instructionDocLineId') &&
        record?.get('toleranceFlag') === 'Y' &&
        record?.status === 'add' &&
        ['NUMBER', 'PERCENTAGE'].includes(record?.get('toleranceType')) && <NumberField />,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
      align: ColumnAlign.right,
      editor: record =>
        !record?.get('instructionDocLineId') &&
        record?.get('toleranceFlag') === 'Y' &&
        record?.status === 'add' &&
        ['NUMBER', 'PERCENTAGE'].includes(record?.get('toleranceType')) && <NumberField />,
    },
    {
      name: 'remark',
      editor: () => canEdit,
    },
  ];

  // 判断头信息必输字段是否输入
  const handleCheck = () => {
    const obj = formDs?.current?.toData();
    if (obj.siteId && obj.customerId && obj.instructionDocType && obj.customerPo) {
      setLineAdd(true);
    } else {
      setLineAdd(false);
    }
  };

  return (
    <div className="hmes-style">
      <Header
        title={intl.get(`${modelPrompt}.title.customerSuppliedParts`).d('客供件管理平台')}
        backPath="/hwms/customer-supplied-parts/list"
        onBack={handleBack}
      >
        {canEdit && (
          <>
            <PermissionButton
              type="c7n-pro"
              color={ButtonColor.primary}
              icon="save"
              onClick={handleSave}
              loading={changeSaveLoading}
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get('tarzan.common.button.save').d('保存')}
            </PermissionButton>
            <Button icon="close" onClick={handleCancel}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </Button>
          </>
        )}
        {!canEdit && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="edit-o"
            disabled={headerState !== 'RELEASED' || !permissionFlag}
            onClick={() => {
              setCanEdit(prev => !prev);
            }}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '详情页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.edit').d('编辑')}
          </PermissionButton>
        )}
      </Header>
      <Content>
        <Collapse bordered={false} defaultActiveKey={['basicInfo', 'location']}>
          <Panel
            header={intl.get(`${modelPrompt}.title.header`).d('头信息')}
            key="basicInfo"
            dataSet={formDs}
          >
            {customizeForm(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.HEAD`,
              },
              <Form disabled={!canEdit||tableDs.records.some(item => item.status === 'add')} dataSet={formDs} columns={3} labelWidth={110}>
                <TextField disabled name="instructionDocNum" />
                <Select
                  name="instructionTypeObj"
                  disabled={!createPage}
                  onChange={value => {
                    handleTypeChange(value);
                    handleCheck();
                  }}
                />
                <Select name="instructionDocStatus" disabled={!createPage} />
                <TextField name="customerPo" onChange={handleCheck} disabled={!createPage} />
                <Lov name="siteLov" disabled={!createPage} onChange={handleCheck} />
                <DateTimePicker name="expectedArrivalTime" disabled={!createPage} />
                <DateTimePicker name="demandTime" disabled={!createPage} />
                <Lov name="customerLov" disabled={!createPage} onChange={handleCheck} />
                <TextField name="remark" />
              </Form>,
            )}
          </Panel>
          <Panel
            header={
              <>
                {intl.get(`${modelPrompt}.title.line`).d('行信息')}
                <Popover
                  placement="topLeft"
                  content={intl
                    .get(`${modelPrompt}.line.tooltipMessage`)
                    .d(
                      '若您没有勾选的行上仓库的权限，该行将会为置灰状态，不能编辑，您可以前往人员仓库权限分配维护权限。',
                    )}
                >
                  <Icon
                    style={{ fontSize: 16, color: '#8c8c8c', marginBottom: 2, marginLeft: 2 }}
                    type="help"
                  />
                </Popover>
              </>
            }
            key="location"
            dataSet={tableDs}
          >
            {customizeTable(
              {
                code: `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.LINE`,
              },
              <Table
                filter={record => {
                  return record.status !== 'delete';
                }}
                dataSet={tableDs}
                highLightRow={false}
                columns={lineTableColumns}
                style={{ height: 350 }}
              />,
            )}
          </Panel>
        </Collapse>
      </Content>
    </div>
  );
});

export default formatterCollections({
  code: ['tarzan.inLibrary.sendReceiveDoc', 'tarzan.common'],
})(
  withCustomize({
    unitCode: [
      `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.HEAD`,
      `${BASIC.CUSZ_CODE_BEFORE}.SEND_RECEIVE_DETAIL.LINE`,
    ],
    // @ts-ignore
  })(customerSuppliedParts),
);
