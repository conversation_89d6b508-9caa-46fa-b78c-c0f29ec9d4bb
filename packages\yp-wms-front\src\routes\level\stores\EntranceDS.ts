/**
 * @Description: 库存查询 - 入口页DS
 * @Author: <EMAIL>
 * @Date: 2022/7/6 15:39
 * @LastEditTime: 2022-11-21 13:37:27
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import uuid from 'uuid/v4';
import { getCurrentOrganizationId } from 'utils/utils';
import { DataSet } from 'choerodon-ui/pro';
import { getCurrentSiteInfo } from '@utils/utils';

const modelPrompt = 'tarzan.inventory.query.model.query';
const tenantId = getCurrentOrganizationId();

const entranceDS = (): DataSetProps => ({
  autoQuery: true,
  selection: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  paging: 'server',
  pageSize: 10,
  // primaryKey: 'uuid',
  // parentField: 'parentUuid',
  // idField: 'uuid',
  expandField: 'expand',
  // modifiedCheck: false,
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCodes`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: { tenantId },
      ignore: FieldIgnore.always,
      required: true,
      dynamicProps: {
        defaultValue: () => {
          const siteInfo = getCurrentSiteInfo();
          if (siteInfo.siteId) {
            return { ...siteInfo };
          }
          return undefined;
        },
      },
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialIds`).d('物料编码'),
      lovCode: 'MT.METHOD.MATERIAL.PERMISSION',
      multiple: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            enableFlag: 'Y',
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'materialIds',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.queryLocators`).d('仓库编码'),
      lovCode: 'WMS.INVENTORY_DETAILS_LOCATOR',
      multiple: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')],
            type: 'LOCATOR',
            queryLocatorCategoryList: ['AREA'],
            locatorCategoryList: ['AREA', 'INVENTORY'],
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'warehouseId',
      bind: 'locatorLov.warehouseId',
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.queryLocators`).d('供应商编码'),
      lovCode: 'MT.MODEL.SUPPLIER',
      multiple: true,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')],
            type: 'LOCATOR',
            queryLocatorCategoryList: ['AREA'],
            locatorCategoryList: ['AREA', 'INVENTORY'],
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierCode',
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'specifiedLevelLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.specifiedLevelLov`).d('档位'),
      lovCode: `WMS.MATERIAL_SPECIFIED_LEVEL`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'specifiedLevel',
      type: FieldType.string,
      bind: 'specifiedLevelLov.specifiedLevel',
    },
    {
      name: 'usableFlag',
      label: intl.get(`${modelPrompt}.usableFlag`).d('是否可用'),
      lookupCode: 'MT.YES_NO',
      type: FieldType.string,
    },
    {
      name: 'freezeFlag',
      lookupCode: 'MT.YES_NO',
      label: intl.get(`${modelPrompt}.freezeFlag`).d('是否冻结'),
      type: FieldType.string,
    },
    {
      name: 'pendingFlag',
      lookupCode: 'MT.YES_NO',
      label: intl.get(`${modelPrompt}.pendingFlag`).d('是否待检'),
      type: FieldType.string,
    },
    {
      name: 'ngFlag',
      lookupCode: 'MT.YES_NO',
      label: intl.get(`${modelPrompt}.ngFlag`).d('是否不良'),
      type: FieldType.string,
    },
  ],
  fields: [
    {
      name: 'uuid',
      type: FieldType.string,
    },
    {
      name: 'parentUuid',
      type: FieldType.string,
    },
    {
      name: 'expand',
      type: FieldType.boolean,
      defaultValue: false,
    },
    {
      name: 'siteCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
    },
    {
      name: 'warehouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.warehouseCode`).d('仓库编码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料名称'),
    },
    {
      name: 'minPackageQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.minPackageQty`).d('最小单包量'),
    },
    {
      name: 'packageQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packageQty`).d('箱数'),
    },
    {
      name: 'okQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.okQty`).d('可用库存'),
    },
    {
      name: 'pendingQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.pendingQty`).d('待检库存'),
    },
    {
      name: 'freezeQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeQty`).d('冻结库存'),
    },
    {
      name: 'ngQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ngQty`).d('不良库存'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
    },
    {
      name: 'supplierCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
    },
    {
      name: 'supplierName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierName`).d('供应商名称'),
    },
    {
      name: 'specifiedLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.specifiedLevel`).d('档位'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms-inventory-details/list/get/ui`,
        method: 'GET',
        data: {
          ...data,
          siteId: data.siteId,
          materialId: data.materialIds.join(','),
          warehouseId: data.warehouseId.join(','),
          supplierId: data.supplierId.join(','),
        },
      };
    },
  },
});

export { entranceDS };
