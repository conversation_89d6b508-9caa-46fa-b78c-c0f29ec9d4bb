/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-08-22 14:11:21
 * @LastEditTime: 2023-08-22 14:17:05
 * @LastEditors: <<EMAIL>>
 */
import React, { useContext, useEffect } from 'react';
import { ThemeContext } from '@hzero-front-ui/core';
import { ConfigProvider, configure } from 'choerodon-ui';
import { getResponse ,initUserCurrentSiteInfo} from '@utils/utils';
import { loadConfig } from 'hzero-front/lib/utils/c7nUiConfig';


if (process.env.ADDITIONAL === 'true') {
  loadConfig(configure);
}

const configProps = {
  feedback: {
    loadSuccess(resp) {
      getResponse(resp);
    },
    submitSuccess() { },
  },
  dateTimePickerOkButton: true,
};

export const PageWrapper = () => {
  const PageWrapperComponent = ({ children }: any) => {

    useEffect(() => {
      initUserCurrentSiteInfo();
      const baseTheme = () => import('@utils/hmesStyle.base.less');
      baseTheme();
      if (schema === 'nightingale') {
        const overrideTheme = () => import('@utils/theme5.override.less');
        overrideTheme();
      }
      const addIconfont = () => import('@assets/iconfonts/iconfont.css');
      addIconfont();
      configure(configProps);
    }, [])

    const { schema } = useContext(ThemeContext);

    return <ConfigProvider {...configProps}>{children}</ConfigProvider>;
  };
  return PageWrapperComponent;
};
export default {};
