/**
 * @date 2023-7-5
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useMemo } from 'react';
import { DataSet, Table } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import intl from 'utils/intl';
import { assemblyDS } from '../stores/AssemblyDS';

const modelPrompt = 'tarzan.hmes.purchase.sellOrder';

const AssemblyDrawer = props => {
  const { soLineId } = props.record;
  const assemblyDs = useMemo(() => {
    return new DataSet(assemblyDS());
  }, []);

  useEffect(() => {
    assemblyDs.queryParameter = {
      soLineId,
    };
    assemblyDs.query();
  }, [soLineId]);

  // 组件息表配置
  const subColumns = [
    {
      name: 'lpNumber',
      title: intl.get(`${modelPrompt}.lpNumber`).d('计划行号'),
      width: 120,
    },
    {
      name: 'lpCategoryDesc',
      title: intl.get(`${modelPrompt}.lpCategoryDesc`).d('计划行类别'),
      width: 120,
    },
    {
      name: 'scheduleShipDate',
      title: intl.get(`${modelPrompt}.scheduleShipDate`).d('计划发运日期'),
      width: 160,
      align: 'center',
    },
    {
      name: 'scheduleArrivalDate',
      title: intl.get(`${modelPrompt}.scheduleArrivalDate`).d('计划到货日期'),
      align: 'center',
      width: 160,
    },
    {
      name: 'orderedQuantity',
      title: intl.get(`${modelPrompt}.orderedQuantity`).d('数量'),
      width: 60,
      align: 'right',
    },
    {
      name: 'shippedQuantity',
      title: intl.get(`${modelPrompt}.shippedQuantity`).d('已发运数量'),
      width: 100,
      align: 'right',
    },
    {
      name: 'reservedQuantity',
      title: intl.get(`${modelPrompt}.reservedQuantity`).d('预留数量'),
      width: 100,
      align: 'right',
    },
    {
      name: 'freezeFlag',
      title: intl.get(`${modelPrompt}.freezeFlag`).d('信用冻结标识'),
      width: 120,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'deleteFlag',
      title: intl.get(`${modelPrompt}.deleteFlag`).d('计划行删除标识'),
      width: 120,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'remark',
      title: intl.get(`${modelPrompt}.remark`).d('计划行备注'),
      width: 120,
    },
  ];
  // return props.customizeTable(
  //   {
  //     code: `${BASIC.CUSZ_CODE_BEFORE}.PO_LIST_COMPONENT.QUERY`,
  //   },
  //   <Table dataSet={assemblyDs} columns={subColumns} />,
  // )
  return <Table dataSet={assemblyDs} columns={subColumns} />;
};

export default AssemblyDrawer;
