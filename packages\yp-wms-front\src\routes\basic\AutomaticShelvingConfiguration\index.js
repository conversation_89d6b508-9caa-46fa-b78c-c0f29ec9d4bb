/* * @Author: xu xiaoyu
* @Date: 2023-06-26 15:00:24
 * @Last Modified by: xu xiaoyu
 * @Last Modified time: 2023-06-26 15:04:098
*/
import React, { useState, useEffect } from 'react';
import { DataSet, Table, Button} from 'choerodon-ui/pro';
import intl from 'utils/intl';
// import { Badge, Switch } from 'choerodon-ui';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import notification from 'utils/notification';
import { BASIC, API_HOST } from '@utils/config';
import request from 'utils/request';
import ExcelExport from 'components/ExcelExport';
import { openTab } from 'utils/menuTab';
import queryString from 'querystring';
import { headerTableDS } from './stores/ListDS';



const modelPrompt = 'tarzan.wms.AutomaticShelvingConfiguration';
const tenantId = getCurrentOrganizationId();

const Host = `${BASIC.HMES_BASIC}`

const List = props => {

  const {
    headerTableDs,
  } = props;

  const [loading, setLoading] = useState(false)
  const [delLoading, setDelLoading] = useState(false)
  const [cancelDisable, setCancelDisabled] = useState(true)
  const [selectRow, setSelectRow] = useState(false)
  const [defaultSiteLov, setDefaultSiteLov] = useState('')

  useEffect(() => {
    if (headerTableDs) {
      headerTableDs.addEventListener('query', () => {
        setSelectRow([]);
        setCancelDisabled(true)
      });
      headerTableDs.addEventListener('select', handleDataSetSelectUpdate);
      headerTableDs.addEventListener('unSelect', handleDataSetSelectUpdate);
      headerTableDs.addEventListener('selectAll', handleDataSetSelectUpdate);
      headerTableDs.addEventListener('unSelectAll', handleDataSetSelectUpdate);
    }
    return () => {
      if (headerTableDs) {
        headerTableDs.removeEventListener('select', handleDataSetSelectUpdate);
        headerTableDs.removeEventListener('unSelect', handleDataSetSelectUpdate);
        headerTableDs.removeEventListener('selectAll', handleDataSetSelectUpdate);
        headerTableDs.removeEventListener('unSelectAll', handleDataSetSelectUpdate);
      }
    };
  }, []);
  useEffect(() => {
    // 获取默认站点
    request(`${Host}/v1/${tenantId}/wms-auto-shelfs/getUser`, {
      method: 'GET',
    }).then(res => {
      setLoading(false)
      const response = getResponse(res)
      if(response){
        headerTableDs.queryDataSet?.current?.set('siteLov', {
          siteId: response.siteId,
          siteCode: response.siteCode,
          siteName: response.siteName,
        });
        setDefaultSiteLov({
          siteId: response.siteId,
          siteCode: response.siteCode,
          siteName: response.siteName,
        })
        headerTableDs.query();
      }
    })
  }, [])

  const handleDataSetSelectUpdate = () => {
    setSelectRow(headerTableDs.selected);
    setCancelDisabled(!headerTableDs.selected.length)
  };


  const headerTableColumns = [
    {
      name: 'siteLov',
      renderer: ({ record }) => record?.get('siteCode'),
      editor: record => record.getState('editing'),
    },
    {
      name: 'materialLov',
      width: 150,
      renderer: ({ record }) => record?.get('materialCode'),
      editor: record => record.getState('editing'),
    },
    {
      name: 'materialName',
      width: 250,
    },
    {
      name: 'warehouseLov',
      renderer: ({ record }) => record?.get('warehouseCode'),
      editor: record => record.getState('editing'),
    },
    {
      name: 'warehouseName',
    },
    // {
    //   name: 'enableFlag',
    //   renderer: ({ record }) =>
    //     !record.getState('editing') ? (
    //       <Badge
    //         status={record.get('enableFlag') === 'Y' ? 'success' : 'error'}
    //         text={
    //           record.get('enableFlag') === 'Y'
    //             ? intl.get('tarzan.common.label.yes').d('是')
    //             : intl.get('tarzan.common.label.no').d('否')
    //         }
    //       />
    //     ) : (
    //       <Switch onChange={(val) => {handleChangeSwitch(record,val)}} checked={record.get('enableFlag') === 'Y'} />
    //     ),
    // },
    {
      name: 'realName',
    },
    {
      name: 'lastUpdateDate',
    },
  ];

  // const handleChangeSwitch = (record,val) => {
  //   record.set('enableFlag', val?'Y':'N')
  // }
  const handleEdit = () => {
    selectRow.forEach(record => {
      record.setState('editing', true)
    })
  }
  const handleCancel = () => {
    headerTableDs.forEach(record => {
      if (record.status === 'add') {
        headerTableDs.unSelect(record)
        headerTableDs.remove(record);
      } else {
        record.reset();
        record.setState('editing', false)
      }
    })
    if(!selectRow.length){
      setCancelDisabled(true)
    }
  }
  const handleSave = () => {
    const promiss = headerTableDs.selected.map(record => record.validate())
    const canSave = headerTableDs.selected.map(record => record.getState('editing'))
    if(canSave.every(item => !item)){
      return
    }
    const temp = headerTableDs.selected.filter(record => record.getState('editing'))
    const bodyArr = temp.map(record => record.toJSONData())
    Promise.all(promiss).then(arr => {
      if(arr.every(item => item)){
        setLoading(true)
        request(`${Host}/v1/${tenantId}/wms-auto-shelfs/save`, {
          method: 'POST',
          body: bodyArr,
        }).then(res => {
          setLoading(false)
          const response = getResponse(res)
          if(response){
            notification.success({
              message: '保存成功！',
            });
            headerTableDs.forEach(record => {
              record.setState('editing', false)
            })
            headerTableDs.query(headerTableDs.currentPage)
          }
        })
      }
    })

  }

  const handleDelete = () => {
    const bodyArr = headerTableDs?.selected?.map(record => record?.toData()?.shelfId);
    request(`${Host}/v1/${tenantId}/wms-auto-shelfs/del`, {
      method: 'POST',
      body: [ ...bodyArr ],
    }).then(res => {
      setDelLoading(false)
      const response = getResponse(res)
      if(response){
        notification.success({
          message: '删除成功！',
        });
        headerTableDs.query(headerTableDs.currentPage)
      }
    })
  }
  const handleCreate = () => {
    setCancelDisabled(false)
    headerTableDs.create({}, 0);
    headerTableDs.current.init('siteLov', defaultSiteLov);
    // headerTableDs.current.set('enableFlag', 'Y');
    headerTableDs.current.setState('editing', true);
    headerTableDs.select(0)
  }

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/WMS_SYNTHESISE_DEMAND',
      title: '资材审批平台导出',
      search: queryString.stringify({
        title: '资材审批平台导出',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
      }),
    });
  };
  const getQueryParams = () => {
    return {
      siteId: headerTableDs.queryDataSet.current.get('siteId'),
      materialId: headerTableDs.queryDataSet.current.get('materialId'),
      warehouseId: headerTableDs.queryDataSet.current.get('warehouseId'),
    }
  }

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('自动上架配置维护')}>
        <Button loading={loading} disabled={headerTableDs.some(record =>record.status === 'add')} onClick={handleCreate} color='primary'>{intl.get(`tarzan.common.button.create`).d('新建')}</Button>
        <Button loading={loading} disabled={!selectRow.length} onClick={handleEdit} color='primary'>{intl.get(`tarzan.common.button.edit`).d('编辑')}</Button>
        <Button loading={loading} disabled={!selectRow.length} onClick={handleSave} color='primary'>{intl.get(`tarzan.common.button.save`).d('保存')}</Button>
        <Button loading={loading} disabled={cancelDisable} onClick={handleCancel} color='primary'>{intl.get(`tarzan.common.button.cancel`).d('取消')}</Button>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${Host}/v1/${tenantId}/wms-auto-shelfs/export`}
          queryParams={getQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
        <Button
          type="c7n-pro"
          icon="file_upload"
          onClick={goImport}
        >
          {intl.get(`tarzan.common.button.import`).d('导入')}
        </Button>
        <Button
          loading={delLoading}
          disabled={!(selectRow.length && headerTableDs.every(record =>record.status !== 'add'))}
          onClick={() => handleDelete()}
          color='primary'
        >
          {intl.get(`tarzan.common.button.delete`).d('删除')}
        </Button>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={6}
          searchCode="AutomaticShelvingConfiguration"
          customizedCode="AutomaticShelvingConfiguration"
          queryBar='filterBar'
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headerTableDs}
          columns={headerTableColumns}
        />,
      </Content>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      return {
        headerTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  formatterCollections({ code: ['tarzan.wms.AutomaticShelvingConfiguration', 'tarzan.common', 'hzero.c7nProUI'] }),
)(List);
