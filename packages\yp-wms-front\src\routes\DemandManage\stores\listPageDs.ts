import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId, getCurrentTenantId } from 'utils/utils';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const modelPrompt = 'tarzan.ass.demandManage';
const tenantId = getCurrentOrganizationId();
console.log(getCurrentTenantId());

const listPageFactory = () =>
  new DataSet({
    primaryKey: 'instructionDocId',
    selection: DataSetSelection.multiple,
    paging: true,
    autoQuery: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    queryDataSet: new DataSet({
      fields: [
        {
          name: 'instructionDocNum',
          label: intl.get(`${modelPrompt}.form.instructionDocNum`).d('需求单号'),
          type: FieldType.string,
        },
        {
          name: 'demandDateFrom',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.demandDateFrom`).d('需求日期从'),
        },
        {
          name: 'demandDateTo',
          type: FieldType.dateTime,
          label: intl.get(`${modelPrompt}.form.demandDateTo`).d('需求日期至'),
        },
        {
          name: 'instructionDocType',
          type: FieldType.string,
          lookupCode: 'WMS.DEMAND_MANAGE_TYPE_WMS',
          label: intl.get(`${modelPrompt}.form.instructionDocType`).d('需求类型'),
        },
        {
          name: 'instructionDocStatus',
          type: FieldType.string,
          lookupCode: 'ASS.INSTRUCTION_DOC_STATUS',
          label: intl.get(`${modelPrompt}.form.instructionDocStatus`).d('需求状态'),
        },
        {
          name: 'materialCode',
          label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
          type: FieldType.object,
          ignore: FieldIgnore.always,
          multiple: true,
          lovCode: 'MT.METHOD.MATERIAL',
          lovPara: {
            tenantId,
          },
        },
        {
          name: 'materialIds',
          bind: 'materialCode.materialId'
        }
      ],
    }),
    fields: [
      {
        name: 'instructionDocNum',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.instructionDocNum`).d('需求单号'),
      },
      {
        name: 'locatorCode',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.locatorCode`).d('需求仓库'),

      },
      {
        name: 'instructionDocType',
        type: FieldType.string,
        lookupCode: 'WMS.DEMAND_MANAGE_TYPE_WMS',
        label: intl.get(`${modelPrompt}.form.instructionDocType`).d('需求类型'),
      },
      {
        name: 'demandTime',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.demandTime`).d('需求时间'),
      },
      {
        name: 'instructionDocStatus',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.instructionDocStatus`).d('需求状态'),
        lookupCode: 'ASS.INSTRUCTION_DOC_STATUS',
      },
      {
        name: 'createdByName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.createdByName`).d('需求创建人'),
      },
      {
        name: 'lastUpdatedByName',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.lastUpdatedByName`).d('最后更新人'),
      },
      {
        name: 'lastUpdateDate',
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.lastUpdateDate`).d('最后更新时间'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-demand-manage-platform/list`,
        };
      },
    },
  });

export default listPageFactory;
