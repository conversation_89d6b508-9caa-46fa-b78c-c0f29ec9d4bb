import intl from 'utils/intl';
import { FieldIgnore, FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'shippingReminderDataMaintenance';
const tenantId = getCurrentOrganizationId();

const tableDS: () => DataSetProps = () => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  primaryKey: 'id',
  queryFields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      noCache: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customer`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerCode',
      bind: 'customerLov.customerCode',
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'enableFlag',
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      type: FieldType.string,
      lookupCode: 'WMS.ENABLE_FLAG',
    },
  ],
  fields: [
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteCode`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      required: true,
      ignore: FieldIgnore.always,
      noCache: true,
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customer`).d('客户'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      required: true,
    },
    {
      name: 'customerCode',
      bind: 'customerLov.customerCode',
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'stockWarningTime',
      label: intl.get(`${modelPrompt}.stockWarningTime`).d('备货预警提醒时间/min'),
      type: FieldType.number,
      step: 1,
      min: 0,
      required: true,
    },
    {
      name: 'loadingWarningTime',
      label: intl.get(`${modelPrompt}.loadingWarningTime`).d('装车预警提醒时间/min'),
      type: FieldType.number,
      step: 1,
      min: 0,
      required: true,
    },
    {
      name: 'signInWarningTime',
      label: intl.get(`${modelPrompt}.signInWarningTime`).d('签收预警提醒时间/min'),
      type: FieldType.number,
      step: 1,
      min: 0,
      required: true,
    },
    {
      name: 'enableFlag',
      label: intl.get(`${modelPrompt}.enableFlag`).d('有效性'),
      type: FieldType.string,
      lookupCode: 'WMS.ENABLE_FLAG',
      defaultValue: 'Y',
      required: true,
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-shipping-reminders/list/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDS };
