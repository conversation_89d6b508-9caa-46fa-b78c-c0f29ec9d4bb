/**
 * @Description: 称重数据报表 - 页面DS
 * @Author: <EMAIL>
 * @Date: 2024/1/11 11:30
 * @LastEditTime: 2024/1/11 15:40:05
 * @LastEditors: <EMAIL>
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { DataSetSelection, FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.wms.weightReport';
const tenantId = getCurrentOrganizationId();

const listDS = (): DataSetProps => ({
  autoQuery: true,
  autoCreate: false,
  selection: DataSetSelection.multiple,
  dataKey: 'content',
  totalKey: 'totalElements',
  primaryKey: 'weighbridgeId',
  queryFields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据号'),
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
      lookupCode: 'WMS.WEIGHT_DOC_TYPE',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料号'),
      ignore: FieldIgnore.always,
      lovCode: 'WMS.WEIGHBRIDGE_MATERIAL',
      multiple: true,
    },
    {
      name: 'materialCodes',
      bind: 'materialLov.materialCode',
      multiple: ',',
    },
    {
      name: 'numberPlate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.numberPlate`).d('车牌号'),
    },
    {
      name: 'creatDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creatDateFrom`).d('创建时间从'),
      max: 'creatDateTo',
    },
    {
      name: 'creatDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creatDateTo`).d('创建时间至'),
      min: 'creatDateFrom',
    },
    {
      name: 'lastUpdateDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDateFrom`).d('最后更新时间从'),
      max: 'lastUpdateDateTo',
    },
    {
      name: 'lastUpdateDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.lastUpdateDateTo`).d('最后更新时间至'),
      min: 'lastUpdateDateFrom',
    },
  ],
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据号'),
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
      lookupCode: 'WMS.WEIGHT_DOC_TYPE',
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料号'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'numberPlate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.numberPlate`).d('车牌号'),
    },
    {
      name: 'uom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uom`).d('单位'),
    },
    {
      name: 'gweight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.gweight`).d('毛重'),
    },
    {
      name: 'tweight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.tweight`).d('皮重'),
    },
    {
      name: 'nweight',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.nweight`).d('净重'),
    },
    {
      name: 'sumQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.sumQty`).d('制单数量'),
    },
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
    },
    {
      name: 'lastUpdatedByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedByName`).d('最后更新人'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-weighbridges/list`,
        method: 'GET',
      };
    },
  },
});

const drawerDS = (): DataSetProps => ({
  autoCreate: true,
  autoLocateFirst: true,
  fields: [
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('单据号'),
    },
    {
      name: 'instructionDocType',
      type: FieldType.string,
      required: true,
      label: intl.get(`${modelPrompt}.instructionDocType`).d('单据类型'),
      lookupCode: 'WMS.WEIGHT_DOC_TYPE',
    },
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialLov`).d('物料号'),
      ignore: FieldIgnore.always,
      required: true,
      lovCode: 'MT.METHOD.MATERIAL',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'numberPlate',
      required: true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.numberPlate`).d('车牌号'),
    },
    {
      name: 'uom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uom`).d('单位'),
      required: true,
    },
    {
      name: 'gweight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.gweight`).d('毛重'),
      required: true,
    },
    {
      name: 'tweight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.tweight`).d('皮重'),
      required: true,
    },
    {
      name: 'nweight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.nweight`).d('净重'),
      required: true,
    },
  ],
});

export { listDS, drawerDS };
