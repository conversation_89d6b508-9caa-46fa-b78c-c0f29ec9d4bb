import { DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { AxiosRequestConfig } from 'axios';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.ass.demandManage';

const detailTableFactory = () =>
  new DataSet({
    primaryKey: 'instructionDocId',
    selection: false,
    paging: true,
    autoQuery: false,
    dataKey: 'content',
    totalKey: 'totalElements',
    fields: [
      {
        name: 'lineNumber',
        type: FieldType.number,
        label: intl.get(`${modelPrompt}.form.lineNumber`).d('行号'),
      },
      {
        name: 'materialCode',
        required:true,
        label: intl.get(`${modelPrompt}.form.materialCode`).d('物料编码'),
        type: FieldType.object,
        lovCode: 'MT.METHOD.MATERIAL',
        lovPara: {
          tenantId,
        },
      },
      {
        name: 'materialId',
        dynamicProps: {
          bind: ({ record }) => {
            if (record!.get('materialCode') && record!.get('materialCode').materialId) {
              return 'materialCode.materialId'
            }
          }
        },
      },
      {
        name: 'materialName',
        disabled: true,
        label: intl.get(`${modelPrompt}.form.materialName`).d('物料描述'),
        dynamicProps: {
          bind: ({ record }) => {
            if (record!.get('materialCode') && record!.get('materialCode').materialId) {
              return 'materialCode.materialName'
            }
          },
        },
      },
    
      {
        name: 'quantity',
        type: FieldType.string,
        required:true,
        label: intl.get(`${modelPrompt}.form.quantity`).d('数量'),
      },
      {
        name: 'uomCode',
        disabled: true,
        type: FieldType.string,
        label: intl.get(`${modelPrompt}.form.uomCode`).d('单位'),
        dynamicProps: {
          bind: ({ record }) => {
            if (record!.get('materialCode') && record!.get('materialCode').uomCode) {
              return 'materialCode.uomCode'
            }
          },
        },
      },
      {
        name: 'uomId',
        type: FieldType.string,
        dynamicProps: {
          bind: ({ record }) => {
            if (record!.get('materialCode') && record!.get('materialCode').uomId) {
              return 'materialCode.uomId'
            }
          },
        },
      },
      {
        name: 'lineStatus',
        disabled: true,
        type: FieldType.string,
        defaultValue:'RELEASED',
        lookupCode: 'ASS.INSTRUCTION_DOC_STATUS',
        label: intl.get(`${modelPrompt}.form.lineStatus`).d('状态'),
      },
    ],
    transport: {
      read: (config: AxiosRequestConfig): AxiosRequestConfig => {
        return {
          ...config,
          url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/ass-demand-manage-platform/line/list`,
        };
      },
    },
  });

export default detailTableFactory;
