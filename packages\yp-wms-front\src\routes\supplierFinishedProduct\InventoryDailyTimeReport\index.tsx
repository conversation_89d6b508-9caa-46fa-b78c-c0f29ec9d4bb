/**
 * @Description: 供应商成品库存日报表
 * @Author: <<EMAIL>>
 * @Date: 2023-11-21
 * @LastEditTime: 2023-11-21
 * @LastEditors: <<EMAIL>>
 */
import React, { FC } from 'react';
import { RouteComponentProps } from 'react-router';
import { isNil } from 'lodash';
import intl from 'utils/intl';
import {Table, DataSet, Button} from 'choerodon-ui/pro';
import { Header, Content } from 'components/Page';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import formatterCollections from 'utils/intl/formatterCollections';
import withProps from 'utils/withProps';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import request from "utils/request";
import {getResponse} from "@utils/utils";
import {ButtonColor} from "choerodon-ui/pro/lib/button/enum";
import { TableDS } from './stores/index';

// const BASIC = {
//   TARZAN_REPORT: '/yp-report-wms-44212',
// }

const modelPrompt = 'tarzan.qms.inventoryDailyTimeReport.model';
const tenantId = getCurrentOrganizationId();

interface InventoryDailyTimeReportProps extends RouteComponentProps {
  tableDS: any;
}
const InventoryDailyTimeReport: FC<InventoryDailyTimeReportProps> = props => {
  const { tableDS } = props;

  const columns: ColumnProps[] = [
    {
      name: 'supplierCode',
      width: 150,
    },
    {
      name: 'supplierName',
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
    },
    {
      name: 'wareHouseMeaning',
    },
    {
      name: 'okQty',
      width: 120,
    },
    {
      name: 'ngQty',
      width: 120,
    },
    {
      name: 'supplierDate',
    },
  ];

  const getExportQueryParams = async () => {
    let queryParams = {};
    const idList = tableDS.selected.map(item => item?.toData()?.id);
    if (tableDS.selected.length > 0) {
      queryParams = {
        headerIds: idList,
      };
    } else {
      queryParams = tableDS.queryDataSet.current.toData();
      Object.keys(queryParams).forEach(i => {
        if (isNil(queryParams[i])) {
          delete queryParams[i];
        }
      })
    }
    // 请求后台
    const result = await request(`${BASIC.TARZAN_REPORT}/v1/${tenantId}/supplier-inventory-day-report/export`, {
      method: 'GET',
      responseType: 'blob',
      query: queryParams,
    });
    const res = getResponse(result);
    if (res) {
      const file = new Blob([res], { type: 'application/vnd.ms-excel' });
      const fileURL = URL.createObjectURL(file);
      const fileName = '供应商成品库存日报表.xls';
      const elink = document.createElement('a');
      elink.download = fileName;
      elink.style.display = 'none';
      elink.href = fileURL;
      document.body.appendChild(elink);
      elink.click();
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink);
    }
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('供应商成品库存日报表')}>
        <Button color={ButtonColor.primary} icon="download" onClick={() => getExportQueryParams()}>
          {intl.get(`${modelPrompt}.export`).d('导出')}
        </Button>
      </Header>
      <Content>
        <Table
          dataSet={tableDS}
          columns={columns}
          searchCode="inventoryDailyTimeReport"
          queryBar={TableQueryBarType.filterBar}
          queryFieldsLimit={3}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          customizable
          customizedCode="inventoryDailyTimeReport"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.wms.inventoryDailyTimeReport', 'tarzan.common'],
})(
  withProps(
    () => {
      const tableDS = new DataSet(TableDS());
      return {
        tableDS,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(InventoryDailyTimeReport as any),
);
