/**
 * @Description: 事件类型维护-对象类型drawer
 * @Author: <<EMAIL>>
 * @Date: 2022-11-03 09:36:07
 * @LastEditTime: 2022-11-03 13:57:56
 * @LastEditors: <<EMAIL>>
 */

import React, { FC, useMemo } from 'react';
import { RouteComponentProps } from 'react-router';
import intl from 'utils/intl';
import { DataSet, Table, TextField, Switch, Lov } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { Button as PermissionButton } from 'components/Permission';
import notification from 'utils/notification';
import { useRequest } from '@components/tarzan-hooks';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock } from 'choerodon-ui/pro/lib/table/enum';
import { tableDrawerDS } from './stories/EventTypeDs';
import { saveEventObjectTypeRelDrawerConfig } from './services';

// @ts-ignore
const ObjectTypeDrawer: FC<RouteComponentProps> = ({ eventTypeId, path }) => {
  const saveEventObjectTypeRelDrawer = useRequest(saveEventObjectTypeRelDrawerConfig(), {
    manual: true,
    needPromise: true,
  });

  const tableDrawerDs = useMemo(() => {
    return new DataSet(tableDrawerDS(eventTypeId));
  }, [eventTypeId]);

  const handleCreateEventRequestType = () => {
    const newLine = tableDrawerDs.create(
      {
        enableFlag: 'Y',
      },
      0,
    );
    newLine.setState('editing', true);
  };

  const handleDeleteObjectType = record => {
    tableDrawerDs.delete(record, false);
  };

  const handleEditMessage = record => {
    record.setState('editing', true);
  };

  const handleCleanLine = record => {
    if (record.get('eventTypeId')) {
      record.reset();
      record.setState('editing', false);
    } else {
      tableDrawerDs.delete(record, false);
    }
  };

  const handleSave = async record => {
    const recordData = record.toData();
    const validate = await record.validate();
    if (!validate) {
      return;
    }
    recordData.eventTypeId = eventTypeId;

    return saveEventObjectTypeRelDrawer.run({
      params: recordData,
      onSuccess: res => {
        record.reset();
        record.setState('editing', false);
        Object.keys(recordData).forEach(_key => {
          record.init(_key, recordData[_key]);
        });
        Object.keys(res).forEach(_key => {
          record.init(_key, res[_key]);
        });
        // @ts-ignore
        record.status = 'sync';
        // @ts-ignore
        notification.success();
      },
    });
  };

  const columns: ColumnProps[] = [
    {
      header: () => (
        <PermissionButton
          type="c7n-pro"
          icon="add"
          onClick={handleCreateEventRequestType}
          funcType="flat"
          shape="circle"
          size="small"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      align: ColumnAlign.center,
      width: 80,
      renderer: ({ record }) => (
        <PermissionButton
          type="c7n-pro"
          icon="remove"
          funcType="flat"
          shape="circle"
          size="small"
          disabled={record?.get('relId')}
          onClick={() => {
            handleDeleteObjectType(record);
          }}
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
        />
      ),
      lock: ColumnLock.left,
    },
    {
      name: 'objectTypeObject',
      width: 200,
      editor: record => record.getState('editing') && <Lov />,
    },
    {
      name: 'description',
      width: 150,
      editor: record => record.getState('editing') && <TextField />,
    },
    {
      name: 'enableFlag',
      width: 90,
      editor: record => record.getState('editing') && <Switch />,
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.enable`).d('启用')
              : intl.get(`tarzan.common.label.disable`).d('禁用')
          }
        />
      ),
    },
    {
      name: 'operator',
      width: 140,
      align: ColumnAlign.center,
      renderer: ({ record }) =>
        record?.getState('editing') ? (
          <>
            <a onClick={() => handleCleanLine(record)}>
              {intl.get('tarzan.common.button.cancel').d('取消')}
            </a>
            <a style={{ marginLeft: '10px' }} onClick={() => handleSave(record)}>
              {intl.get('tarzan.common.button.save').d('保存')}
            </a>
          </>
        ) : (
          <>
            <a onClick={() => handleEditMessage(record)}>
              {intl.get('tarzan.common.button.edit').d('编辑')}
            </a>
          </>
        ),
    },
  ];

  return <Table dataSet={tableDrawerDs} columns={columns} />;
};

export default ObjectTypeDrawer;
