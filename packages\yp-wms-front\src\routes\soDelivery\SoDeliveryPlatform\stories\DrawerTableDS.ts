/**
 * @Description: 销售发运平台 - 抽屉DS
 * @Author: <EMAIL>
 * @Date: 2022/2/10 14:53
 */
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';

const modelPrompt = 'tarzan.soDelivery.soDeliveryPlatform';
const tenantId = getCurrentOrganizationId();

const drawerTableDS = (): DataSetProps => ({
  autoQuery: true,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
    },
    {
      name: 'materialLotStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('条码状态'),
    },
    {
      name: 'carType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.carType`).d('车型'),
    },
    {
      name: 'labelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.labelCode`).d('标签号'),
    },
    {
      name: 'vinCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vinCode`).d('车架号'),
    },
    {
      name: 'seq',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.seq`).d('车序号'),
    },
    {
      name: 'paoffTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.paoffTime`).d('PAOFF时间'),
    },
    {
      name: 'identifyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifyCode`).d('简号'),
    },
    {
      name: 'containerIdentification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerIdentification`).d('所在容器'),
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
    },
    {
      name: 'prepareDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prepareData`).d('备货时间'),
    },
    {
      name: 'prepareRealName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.prepareRealName`).d('备货人'),
    },
    {
      name: 'deliveryDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.deliveryDate`).d('发货时间'),
    },
    {
      name: 'deliveryPerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.deliveryPerson`).d('发货人'),
    },
    {
      name: 'executePerson',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.executePerson`).d('执行人'),
    },
    {
      name: 'executeDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.executeDate`).d('执行时间'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-delivery-platform/instruction-doc/material-lot/detail/for/ui`,
        method: 'GET',
      };
    },
  },
});

const LabelTableDS = (): DataSetProps => ({
  autoQuery: false,
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'carType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.carType`).d('车型'),
    },
    {
      name: 'labelCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.labelCode`).d('标签号'),
    },
    {
      name: 'vinCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.vinCode`).d('车架号'),
    },
    {
      name: 'seq',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.seq`).d('车序号'),
    },
    {
      name: 'paoffTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.paoffTime`).d('PAOFF时间'),
    },
    {
      name: 'identifyCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identifyCode`).d('简号'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-product-delivery-platform/instruction-doc/material-lot/label-code/for/ui`,
        method: 'GET',
      };
    },
  },
});

export { drawerTableDS, LabelTableDS };
