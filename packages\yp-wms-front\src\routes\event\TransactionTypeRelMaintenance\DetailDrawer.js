/**
 * @Description: 新建编辑-抽屉
 * @Author: <<EMAIL>>
 * @Date: 2021-05-06 14:04:34
 * @LastEditTime: 2021-05-06 14:04:34
 * @LastEditors: <<EMAIL>>
 */

import React from 'react';
import { Lov, Form, Select, TextField, Switch } from 'choerodon-ui/pro';

const DetailDrawer = ({ ds, userRule, data = {} }) => {
  const { initialFlag = 'N' } = data;
  const changeEventTypeCode = (record = {}) => {
    ds.current.set('eventTypeId', record.eventTypeId);
    ds.current.set('description', record.description);
  };

  const changeBusinessTypeCode = (record = {}) => {
    ds.current.set('businessTypeDesc', record.description);
  };

  // 切换ERP系统名称
  const changeErpSystemType = val => {
    if (val === 'EBS') {
      ds.current.set('transCode', '');
    }
  };

  return (
    <div className="tarzan-ui-remove">
      <Form dataSet={ds} columns={1} className="customer-form">
        <Lov
          name="eventTypeCode"
          onChange={changeEventTypeCode}
          disabled={userRule === 'N' && initialFlag === 'Y'}
        />
        <TextField name="description" disabled />
        <Lov
          name="businessTypeCode"
          onChange={changeBusinessTypeCode}
          disabled={userRule === 'N' && initialFlag === 'Y'}
        />
        <TextField name="businessTypeDesc" disabled />
        <Select
          name="transCategory"
          disabled={userRule === 'N' && initialFlag === 'Y'}
        />
        <Select
          name="erpSystemType"
          onChange={changeErpSystemType}
          disabled={userRule === 'N' && initialFlag === 'Y'}
        />
        <TextField
          name="transTypeCode"
          required
          disabled={userRule === 'N' && initialFlag === 'Y'}
        />
        <Select name="transCode" disabled={userRule === 'N' && initialFlag === 'Y'} />
        <Switch name="initialFlag" disabled={userRule === 'N' && initialFlag === 'Y'} />
        <Select name="transferMethod" disabled={userRule === 'N' && initialFlag === 'Y'} />
      </Form>
    </div>
  );
};

export default DetailDrawer;
