/**
 * @feature 物料配送属性维护-详情页
 * @date 2021-3-14
 * <AUTHOR> <<EMAIL>>
 */

import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { DataSet } from 'choerodon-ui/pro';
import { targetPointOptionDs } from './EntranceDs';

const modelPrompt = 'tarzan.product.materialDistributionNew.model.materialDistributionNew';
const tenantId = getCurrentOrganizationId();

const baseInfoDS = () => ({
  autoQuery: false,
  autoCreate: true,
  autoLocateFirst: true,
  autoQueryAfterSubmit: false,
  dataKey: 'rows',
  paging: false,
  fields: [
    {
      name: 'siteCode1',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.site`).d('站点'),
      lovCode: 'MT.MODEL.SITE',
      lovPara: {
        tenantId,
      },
      required: true,
    },
    {
      name: 'siteId',
      bind: 'siteCode1.siteId',
    },
    {
      name: 'siteCode',
      bind: 'siteCode1.siteCode',
    },
    {
      name: 'materialSelect',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.patternType`).d('物料/物料类别'),
      options: new DataSet({
        data: [
          { value: 'material', key: intl.get(`${modelPrompt}.material`).d('物料') },
          {
            value: 'materialCategory',
            key: intl.get(`${modelPrompt}.materialCategory`).d('物料类别'),
          },
        ],
      }),
      textField: 'key',
      valueField: 'value',
      defaultValue: 'material',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'materialCode1',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.code`).d('编码'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            siteId: record.get('siteId'),
            tenantId,
          };
        },
        lovCode: ({ record }) => {
          switch (record.get('materialSelect')) {
            case 'material':
              return 'MT.METHOD.MATERIAL';
            case 'materialCategory':
              return 'MT.METHOD.MATERIAL_CATEGORY';
            default:
              return null;
          }
        },
      },
    },
    {
      name: 'materialId',
      bind: 'materialCode1.materialId',
    },
    {
      name: 'materialCategoryId',
      bind: 'materialCode1.materialCategoryId',
    },
    {
      name: 'materialCode',
      bind: 'materialCode1.materialCode',
    },
    {
      name: 'materialCategoryCode',
      bind: 'materialCode1.categoryCode',
    },
    {
      name: 'sourceLocatorCode1',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.sourceLocator`).d('来源库位'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')].join(','),
          };
        },
      },
    },
    {
      name: 'sourceLocatorId',
      bind: 'sourceLocatorCode1.locatorId',
    },
    {
      name: 'sourceLocatorCode',
      bind: 'sourceLocatorCode1.locatorCode',
    },
    {
      name: 'targetPlaceLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.targetPlace`).d('目标地点'),
      lovCode: 'MT_MOD_LOCATOR_CATEGORY_LOCATION',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')].join(','),
            parentLocatorId:record.get('targetLocatorId'),
          };
        },
      },
    },
    {
      name: 'targetPlaceId',
      bind: 'targetPlaceLov.locatorId',
    },
    {
      name: 'targetPlaceCode',
      bind: 'targetPlaceLov.locatorCode',
    },
    {
      name: 'distributionQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.distributionQty`).d('配送数量'),
      required: true,
      min: 0,
      computedProps: {
        disabled: ({ record }) => {
          return record.get('multiplesOfPackFlag') === 'Y';
        },
      },
    },
    {
      name: 'singleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.singleQty`).d('单包数量'),
      min: 0,
      required: true,
    },
    {
      name: 'autoCallingFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.autoCallingFlag`).d('是否自动叫料'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'DISTRIBUTION_CATEGORY',
    },
    {
      name: 'distributionRouteCode1',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.distributionLine`).d('配送路线'),
      lovCode: 'MT.METHOD.DISTRIBUTION_ROUTE',
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId') || !record.get('DISTRIBUTION_CATEGORY');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
            distributionCategory: record.get('DISTRIBUTION_CATEGORY'),
          };
        },
      },
    },
    {
      name: 'distributionRouteId',
      bind: 'distributionRouteCode1.distributionRouteId',
    },
    {
      name: 'distributionRouteCode',
      bind: 'distributionRouteCode1.distributionRouteCode',
    },
    {
      name: 'distributionCategoryDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.distributionCategory`).d('配送类别'),
      bind: 'distributionRouteCode1.distributionCategoryDesc',
    },
    {
      name: 'distributionRouteCycle',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.distributionLineCycle`).d('路线配送周期'),
      bind: 'distributionRouteCode1.distributionCycle',
    },
    {
      name: 'packQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.packQty`).d('配送包装数'),
      min: 0,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('multiplesOfPackFlag') === 'Y';
        },
      },
    },
    {
      name: 'organizationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.patternType`).d('目标点位'),
      options: targetPointOptionDs,
      textField: 'description',
      valueField: 'typeCode',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
    },
    {
      name: 'pfepDisOrganization',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.pfepDisOrganization`).d('目标点位'),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        lovPara: ({ record }) => {
          switch (record.get('organizationType')) {
            case 'LOCATOR':
              return {
                tenantId,
                siteIds: [record.get('siteId')].join(','),
              };
            case 'WORKCELL':
              return {
                tenantId,
                siteId: record.get('siteId'),
              };
            default:
              return null;
          }
        },
        lovCode: ({ record }) => {
          switch (record.get('organizationType')) {
            case 'LOCATOR':
              return 'MT.MODEL.LOCATOR_BY_ORG';
            case 'WORKCELL':
              return 'MT.MODEL.WORKCELL';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'pfepDisOrganizationId',
      bind: 'pfepDisOrganization.workcellId',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('organizationType')) {
            case 'LOCATOR':
              return 'pfepDisOrganization.locatorId';
            case 'WORKCELL':
              return 'pfepDisOrganization.workcellId';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'targetLocatorId',
      bind: 'pfepDisOrganization.locatorId',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('organizationType')) {
            case 'LOCATOR':
              return 'pfepDisOrganization.locatorId';
            case 'WORKCELL':
              return 'targetLocatorCode.locatorId';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'targetLocatorCode',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.targetLocator`).d('存储库位'),
      lovCode: 'MT.MODEL.LOCATOR_BY_ORG',
      required: true,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('siteId') || record.get('organizationType') === 'LOCATOR';
        },
        bind: ({ record }) => {
          if (record.get('organizationType') === 'LOCATOR') {
            return 'pfepDisOrganization.locatorCode';
          }
          return false;

        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')].join(','),
          };
        },
      },
    },
    {
      name: 'multiplesOfPackFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.multiplesOfPack`).d('整包配送'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用标识'),
      defaultValue: 'Y',
      trueValue: 'Y',
      falseValue: 'N',
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_METHOD}/v1/${tenantId}/mt-pfep-distribution/pfep/details/ui?customizeUnitCode=${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DISTRIBUTION_DETAIL.MATERIAL_BASIC,${BASIC.CUSZ_CODE_BEFORE}.MATERIAL_DISTRIBUTION_DETAIL.CATEGORY_BASIC`,
        method: 'get',
      };
    },
  },
});

export { baseInfoDS };
