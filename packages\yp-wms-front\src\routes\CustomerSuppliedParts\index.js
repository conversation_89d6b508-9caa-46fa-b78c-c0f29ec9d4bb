/**
 * 客供件单管理-入口文件
 * @date 2023-1-9
 * <AUTHOR> <<EMAIL>>
 */
import React, { useEffect, useState, useMemo } from 'react';
import { DataSet, Table, Dropdown, Modal, Button } from 'choerodon-ui/pro';
import { <PERSON><PERSON>, <PERSON>u, <PERSON>lapse, Spin } from 'choerodon-ui';
import intl from 'utils/intl';
import { Button as PermissionButton } from 'components/Permission';
import { Header, Content } from 'components/Page';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import formatterCollections from 'utils/intl/formatterCollections';
// import { FRPrintButton } from '@components/tarzan-ui';
import withProps from 'utils/withProps';
import { flow, isNil } from 'lodash';
import ExcelExport from 'components/ExcelExport';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import myInstance from '@utils/myAxios';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { getResponse } from '@utils/utils';
import notification from 'utils/notification';
import CreateMaterial from './CreateMaterialDrawer';
import MaterialDetailed from './MaterialDetailedDrawer';
import { headerTableDS, lineTableDS } from './stores/DeliveryListDS';
import { headDS, tableDS } from './stores/MaterialBatchDS';
import { tableDetailedDS } from './stores/MaterialDetailedDS';
import styles from './index.module.less';

const { Panel } = Collapse;
const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.customerSuppliedParts';

let modalMaterial;
let modalMaterialDetailed;
let headerResult;

// 根据文档内容，此功能用了送货单管理功能的主页面，调拨平台的新建，属于结合体
const Order = props => {
  const {
    headerTableDs,
    lineTableDs,
    match: { path },
    customizeTable,
  } = props;

  const headDs = useMemo(() => new DataSet(headDS()), []); // 物料批头ds
  const tableDs = useMemo(() => new DataSet(tableDS()), []); // 物料批行ds
  const tableDetailedDs = useMemo(() => new DataSet(tableDetailedDS()), []); // 物料批信息
  // 判断头搜索条件切换
  const [supplierId, setSupplierId] = useState();
  const [selectedStatus, setSelectedStatus] = useState([]);
  const [tableDsArr, setTableDsArr] = useState([]);
  // 头选中行instructionDocType
  const [instructionDocType, setInstructionDocType] = useState(undefined);
  // 头选中的打印列表
  const [printList, setPrintList] = useState([]);

  const [loading, setLoading] = useState(false);
  const [selectedItem, setSelectedItems] = useState([]);
  const [batchListsArr, setBatchListsArr] = useState([]);

  useEffect(() => {
    if (tableDs) {
      tableDs.addEventListener('batchSelect', handleSelect);
      tableDs.addEventListener('selectAllPage', handleSelect);
      tableDs.addEventListener('batchUnSelect', handleSelect);
      tableDs.addEventListener('unSelectAllPage', handleSelect);
    }
    if (lineTableDs) {
      lineTableDs.addEventListener('batchSelect', handleSelectLineTable);
      lineTableDs.addEventListener('selectAllPage', handleSelectLineTable);
      lineTableDs.addEventListener('batchUnSelect', handleSelectLineTable);
      lineTableDs.addEventListener('unSelectAllPage', handleSelectLineTable);
    }
    return () => {
      if (tableDs) {
        tableDs.removeEventListener('batchSelect', handleSelect);
        tableDs.removeEventListener('selectAllPage', handleSelect);
        tableDs.removeEventListener('batchUnSelect', handleSelect);
        tableDs.removeEventListener('unSelectAllPage', handleSelect);
      }
      if (lineTableDs) {
        lineTableDs.removeEventListener('batchSelect', handleSelectLineTable);
        lineTableDs.removeEventListener('selectAllPage', handleSelectLineTable);
        lineTableDs.removeEventListener('batchUnSelect', handleSelectLineTable);
        lineTableDs.removeEventListener('unSelectAllPage', handleSelectLineTable);
      }
    };
  }, []);

  // 创建多行物料批弹框内 物料批明细的选中事件监听
  useEffect(() => {
    for (let i = 0; i < selectedItem.length; i++) {
      tableDsArr[i].addEventListener('batchSelect', handleSelectLineTableArr);
      tableDsArr[i].addEventListener('selectAllPage', handleSelectLineTableArr);
      tableDsArr[i].addEventListener('batchUnSelect', handleSelectLineTableArr);
      tableDsArr[i].addEventListener('unSelectAllPage', handleSelectLineTableArr);
    }

    return () => {
      for (let i = 0; i < selectedItem.length; i++) {
        tableDsArr[i].removeEventListener('batchSelect', handleSelectLineTableArr);
        tableDsArr[i].removeEventListener('selectAllPage', handleSelectLineTableArr);
        tableDsArr[i].removeEventListener('batchUnSelect', handleSelectLineTableArr);
        tableDsArr[i].removeEventListener('unSelectAllPage', handleSelectLineTableArr);
      }
    };
  }, [tableDsArr]);

  // 创建多行物料批弹框内 物料批明细的选中事件，用来手机选中的物料批id(materialLotId)
  const handleSelectLineTableArr = () => {
    let selectedList = [];
    for (let i = 0; i < selectedItem.length; i++) {
      const selected = tableDsArr[i].selected.map(item => {
        return item.get('materialLotId');
      });
      selectedList = [...selectedList, ...selected];
    }
    updateModalTitle(selectedList);
  };

  const handleSelect = () => {
    const selected = tableDs.selected.map(item => {
      return item.get('materialLotId');
    });
    updateModalTitle(selected);
  };

  const handleSelectLineTable = () => {
    let noMaterialLot = false;
    // eslint-disable-next-line array-callback-return
    const selected = lineTableDs.selected.map(item => {
      // 非实物不可操作
      if (item.get('identifyType') !== 'MATERIAL_LOT') {
        noMaterialLot = true;
      }
      return item.get('instructionDocLineId');
    });
    if (noMaterialLot) {
      setSelectedItems([]);
    } else {
      setSelectedItems(selected);
    }
  };

  const updateModalTitle = selected => {
    modalMaterial.update({
      title: createModalTitle(selected),
    });
  };

  // 物料批弹窗上的删除按钮，用来删除与指令的关系
  const handleDelete = selected => {
    const data = {
      instructionDocLineId: headDs.toData()[0].deliverLineId,
      materialLotIdList: selected,
    };
    const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wms_customer_supplied_parts/material-lot/remove/ui`;
    return myInstance.post(url, data).then(async res => {
      if (res?.data?.success) {
        notification.success({
          message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
        });
        const arr = [];
        // eslint-disable-next-line array-callback-return
        selectedItem.map(() => {
          arr.push(new DataSet(tableDS()));
        });
        setTableDsArr([...arr]);
        modalMaterial.update({
          children: (
            <CreateMaterial
              headDs={headDs}
              tableDs={tableDs}
              batchList={batchListsArr}
              resultData={headerResult}
              tableDsArr={arr}
              selectedItem={selectedItem}
              customizeTable={customizeTable}
            />
          ),
        });
        for (let i = 0; i < selectedItem.length; i++) {
          arr[i].setQueryParameter('instructionDocLineId', selectedItem[i]);
          arr[i].query();
        }
        headDs.setQueryParameter('instructionDocLineId', selectedItem.join(','));
        setLoading(true);
        await headDs.query();
        setLoading(false);
      } else {
        notification.error({
          message: res?.data?.message,
        });
      }
    });
  };

  const handlePrintMaterial = async list => {
    const result = await request(
      `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wmsMaterialLot/print/pdf`,
      {
        method: 'POST',
        responseType: 'blob',
        body: list,
      },
    );
    const res = getResponse(result);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          // 普通对象，读取信息
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: intl.get(`${modelPrompt}.notification.print.success`).d('打印成功'),
          });
        } else {
          notification.error({
            message: intl
              .get(`${modelPrompt}.notification.browser.config`)
              .d('当前窗口已被浏览器拦截，请手动设置浏览器！'),
          });
        }
      }
    }
  };

  const createModalTitle = (selected = []) => {
    return (
      <div>
        <span style={{ fontSize: '14px' }}>
          {intl.get(`${modelPrompt}.create.materialBatch11`).d('创建箱标签')}
        </span>
        <div
          style={{
            float: 'right',
            display: 'flex',
            flexDirection: 'row-reverse',
            alignItems: 'center',
            marginRight: '0.3rem',
          }}
        >
          <PermissionButton
            style={{
              marginLeft: '0.1rem',
              marginRight: '0.2rem',
            }}
            icon="save"
            type="c7n-pro"
            color={ButtonColor.primary}
            onClick={() => handleEstablish()}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.create.establish`).d('创建')}
          </PermissionButton>
          <PermissionButton
            style={{
              marginLeft: '0.1rem',
              marginRight: '0.2rem',
            }}
            icon="delete"
            type="c7n-pro"
            color={ButtonColor.default}
            onClick={() => handleDelete(selected)}
            disabled={selected.length === 0}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.delete').d('删除')}
          </PermissionButton>
          <Button onClick={() => handlePrintMaterial(selected)} disabled={selected.length === 0}>
            {intl.get('tarzan.common.button.print').d('打印')}
          </Button>
          {/* <FRPrintButton
            kid="MATERIAL_LOT_PRINT" // 使用打印组件的功能的唯一标识，业务提供
            queryParams={selected} // 查询数据
            disabled={selected.length === 0}
            printObjectType="INSTRUCTION_DOC"
          /> */}
        </div>
      </div>
    );
  };

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 返回页面时恢复选中项和当前项状态
  useEffect(() => {
    handleLineTableChange();
    if (props?.history?.action === 'PUSH') {
      headerTableDs.query(props.headerTableDs.currentPage);
    }
  }, []);

  // 生成行列表DS查询项
  const listener = flag => {
    // 搜索条件监听
    if (headerTableDs.queryDataSet) {
      const handler = flag
        ? headerTableDs.queryDataSet.addEventListener
        : headerTableDs.queryDataSet.removeEventListener;
      handler.call(headerTableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
    // 列表交互监听
    if (headerTableDs) {
      const handler = flag ? headerTableDs.addEventListener : headerTableDs.removeEventListener;
      // 头选中和撤销选中事件
      // 列表加载事件
      handler.call(headerTableDs, 'load', resetHeaderDetail);
    }
  };

  // 头搜索条件切换清空供应商地点
  const handleQueryDataSetUpdate = ({ record }) => {
    const data = record.toData();
    if (data.supplierId !== supplierId) {
      setSupplierId(data.supplierId);
      record.set('supplierSite', {});
    }
  };

  // 头列表加载
  const resetHeaderDetail = ({ dataSet }) => {
    handleLineTableChange();
    // 数据正常时用第一条数据查询行数据否则空查
    if (dataSet?.current?.toData()) {
      headerRowClick(dataSet?.current);
    } else {
      queryLineTable();
    }
  };

  // 行列表事件, 更新选中行数量
  const handleLineTableChange = () => {
    const _selectedStatus = [];
    const _printList = [];
    headerTableDs.selected.forEach(item => {
      const instructionDocStatus = item?.data?.instructionDocStatus;
      _printList.push(item?.data?.instructionDocId);
      if (_selectedStatus.indexOf(instructionDocStatus) === -1) {
        _selectedStatus.push(instructionDocStatus);
      }
    });
    setSelectedStatus(_selectedStatus);
    setPrintList(_printList);
  };

  // 根据当前勾选的单据状态返回对应的状态变更Item的状态
  const isAllEqualWithKeyWord = (array, keyWord) => {
    // 勾选的都是下达状态，状态变更可点击，且下拉显示取消
    const cancelList = ['RELEASED'];
    // 这几种状态时，状态变更可点击，且下拉显示关闭
    const closeList = ['PROCESSING', '1_PROCESSING', '1_COMPLETED', '2_PROCESSING', 'COMPLETED'];
    // 这几种状态时，状态变更可点击，且下拉显示接收完成
    const completedList = ['PROCESSING', '1_PROCESSING'];
    if (array.length > 0) {
      if (keyWord === 'CANCEL') {
        return array.some(value => {
          return cancelList.indexOf(value) === -1;
        });
      }
      if (keyWord === 'CLOSED') {
        return array.some(value => {
          return closeList.indexOf(value) === -1;
        });
      }
      if (keyWord === 'COMPLETED') {
        return array.some(value => {
          return completedList.indexOf(value) === -1;
        });
      }
    } else {
      return false;
    }
  };

  // 行列表数据查询
  const queryLineTable = instructionDocId => {
    if (instructionDocId) {
      lineTableDs.setQueryParameter('instructionDocId', instructionDocId);
      lineTableDs.query();
    } else {
      lineTableDs.loadData([]);
    }
  };

  // 创建物料批
  const handleCreateMaterial = async () => {
    const arr = [];
    // eslint-disable-next-line array-callback-return
    selectedItem.map(() => {
      arr.push(new DataSet(tableDS()));
    });
    setTableDsArr([...arr]);
    headDs.setQueryParameter('instructionDocLineId', selectedItem.join(','));
    setLoading(true);
    const res = await headDs.query();
    setLoading(false);
    if (res.success) {
      const myDatas = res.rows.map(item => {
        return {
          ...item,
          materialLotQty: item.num ? item.num : '',
          materialSheets:
            item.num > 0
              ? Math.floor(
                ((item.orderedQuantity ? item.orderedQuantity : 0) -
                    (item.createdQuantity ? item.createdQuantity : 0)) /
                    item.num,
              )
              : '',
        };
      });
      headerResult = myDatas;
      headDs.loadData(headerResult);
      // headerResult = res.rows;
      modalMaterial = Modal.open({
        title: createModalTitle([]),
        className: 'hmes-style-modal',
        maskClosable: true,
        destroyOnClose: true,
        drawer: true,
        closable: true,
        onClose: () => {
          tableDs.batchUnSelect(tableDs.selected);
        },
        style: {
          width: 1080,
        },
        children: (
          <CreateMaterial
            headDs={headDs}
            tableDs={tableDs}
            batchList={[]}
            resultData={res.rows}
            tableDsArr={arr}
            selectedItem={selectedItem}
            customizeTable={customizeTable}
          />
        ),
        footer: (
          <Button onClick={() => modalMaterial.close()}>
            {intl.get('tarzan.common.button.back').d('返回')}
          </Button>
        ),
      });
    }
  };

  // 物料批明细
  const handleMaterialDetailed = async record => {
    const instructionDocLineId = record?.data?.instructionDocLineId;
    modalMaterialDetailed = Modal.open({
      title:
        record?.data?.identifyType === 'MATERIAL_LOT'
          ? intl.get(`${modelPrompt}.materialBatch.details`).d('物料批信息')
          : intl.get(`${modelPrompt}.materialDetail`).d('物料明细'),
      className: 'hmes-style-modal',
      maskClosable: true,
      destroyOnClose: true,
      drawer: true,
      closable: true,
      style: {
        width: 1080,
      },
      children: (
        <MaterialDetailed
          tableDs={tableDetailedDs}
          instructionDocLineId={instructionDocLineId}
          instructionDocType={instructionDocType}
          identifyType={record?.data?.identifyType}
          customizeTable={customizeTable}
        />
      ),
      footer: (
        <Button onClick={() => modalMaterialDetailed.close()}>
          {intl.get('tarzan.common.button.back').d('返回')}
        </Button>
      ),
    });
  };

  // 创建物料批
  const handleEstablish = async () => {
    const validate = await headDs.validate();
    if (validate) {
      const data = {
        deliverHeaderId: headDs.toData()[0].deliverHeaderId,
        deliverNumber: headDs.toData()[0].deliverNumber,
        lineInfoList: headDs.toData(),
      };
      const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wms_customer_supplied_parts/material-lot/create/ui`;
      return myInstance.post(url, data).then(async res => {
        if (res?.data?.success) {
          notification.success({
            message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
          });
          const batchLists = res?.data?.rows;
          setBatchListsArr(batchLists);
          const arr = [];
          // eslint-disable-next-line array-callback-return
          selectedItem.map(() => {
            arr.push(new DataSet(tableDS()));
          });
          setTableDsArr([...arr]);
          modalMaterial.update({
            children: (
              <CreateMaterial
                headDs={headDs}
                tableDs={tableDs}
                batchList={batchLists}
                resultData={headerResult}
                tableDsArr={arr}
                selectedItem={selectedItem}
                customizeTable={customizeTable}
              />
            ),
          });
          for (let i = 0; i < selectedItem.length; i++) {
            arr[i].setQueryParameter('instructionDocLineId', selectedItem[i]);
            arr[i].query();
          }
          headDs.setQueryParameter('instructionDocLineId', selectedItem.join(','));
          setLoading(true);
          await headDs.query();
          setLoading(false);
        } else {
          notification.error({
            message: res?.data?.message,
          });
        }
      });
    }
  };

  // 头列表配置
  const headerTableColumns = [
    {
      name: 'createdDate',
      width: 140,
      align: 'center',
    },
    {
      name: 'instructionDocNum',
      width: 140,
      renderer: ({ record, value }) => {
        if (record.data.instructionDocStatus === 'RELEASED') {
          return (
            <a
              onClick={() => {
                props.history.push(
                  `/hwms/customer-supplied-parts/detail/${record.data.instructionDocId}`,
                );
              }}
            >
              {value}
            </a>
          );
        }
        return value;
      },
      // lock: 'left',
    },
    {
      name: 'instructionDocStatusDesc',
      width: 100,
    },
    {
      name: 'instructionDocTypeDesc',
      width: 100,
    },
    {
      name: 'locatorCode',
      width: 180,
    },
    {
      name: 'locatorName',
      width: 180,
    },
    {
      name: 'customerCode',
      width: 180,
    },
    {
      name: 'customerName',
      width: 140,
    },
    {
      name: 'expectedArrivalTime',
      width: 140,
      align: 'center',
    },
    {
      name: 'receiveTime',
      width: 140,
      align: 'center',
    },
    {
      name: 'remark',
      width: 140,
    },
    {
      name: 'overdueFlag',
      width: 140,
      renderer: ({ value }) => {
        if (value === 'Y') {
          return '是';
        }
        if (value === 'N') {
          return '否';
        }
      },
    },
    {
      name: 'printTimes',
      width: 100,
    },
    {
      name: 'realName',
      width: 100,
    },
  ];

  // 行信息表配置
  const lineTableColumns = [
    {
      name: 'lineNumber',
      width: 100,
      lock: 'left',
    },
    {
      name: 'materialCode',
      width: 140,
      lock: 'left',
    },
    {
      name: 'materialName',
      width: 140,
      lock: 'left',
    },

    {
      name: 'revisionCode',
      width: 100,
      lock: 'left',
    },
    {
      name: 'siteCode',
      width: 140,
    },
    {
      name: 'quantity',
      width: 100,
    },
    props.permissionDetail?.approve === false && props.permissionDetail?.controllerType === 'hidden'
      ? null
      : {
        name: 'actualQuantity',
        width: 140,
      },
    {
      name: 'uomCode',
      width: 80,
    },
    {
      name: 'instructionStatusDesc',
      width: 100,
    },
    {
      name: 'actualQty',
      width: 100,
    },
    {
      name: 'storageActualQty',
      width: 100,
    },
    {
      name: 'locatorCode',
      width: 140,
    },
    {
      name: 'locatorName',
      width: 140,
    },
    {
      name: 'urgentFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get(`tarzan.common.label.yes`).d('是')
              : intl.get(`tarzan.common.label.no`).d('否')
          }
        />
      ),
    },
    {
      name: 'toleranceFlag',
      width: 100,
      align: 'center',
      renderer: ({ value }) => (
        <Badge
          status={value === 'Y' ? 'success' : 'error'}
          text={
            value === 'Y'
              ? intl.get('tarzan.common.label.enable').d('启用')
              : intl.get('tarzan.common.label.disable').d('禁用')
          }
        />
      ),
    },
    {
      name: 'toleranceType',
      width: 140,
    },
    {
      name: 'toleranceMaxValue',
      width: 140,
    },
    {
      name: 'toleranceMinValue',
      width: 140,
    },
    {
      name: 'identifyType',
      width: 120,
      // lock: 'left',
      renderer: ({ value }) => {
        if (value === 'LOT' || value === 'MAT') {
          return intl.get('tarzan.common.noPhysicalManage').d('非实物管理');
        }
        if (value === 'MATERIAL_LOT') {
          return intl.get('tarzan.common.physicalManage').d('实物管理');
        }
        return '';
      },
    },
    {
      name: 'poLineId',
      lock: 'right',
      width: 180,
      align: 'center',
      title: intl.get(`${modelPrompt}.option`).d('操作'),
      renderer: ({ record }) => (
        <>
          <a
            style={{ marginLeft: '12px' }}
            onClick={() => {
              handleMaterialDetailed(record);
            }}
          >
            {/* {intl.get(`${modelPrompt}.materialBatch.details`).d('物料批明细')} */}
            {intl.get(`${modelPrompt}.details`).d('明细')}
          </a>
        </>
      ),
    },
  ];

  const headerRowClick = record => {
    setSelectedItems([]);
    setInstructionDocType(record?.toData()?.instructionDocType);
    queryLineTable(record?.toData()?.instructionDocId);
  };

  const clickMenu = async ({ key }) => {
    const instructionDocIds = headerTableDs?.selected?.map(
      item => item?.toData()?.instructionDocId,
    );

    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/wms_customer_supplied_parts/status/change/ui`, {
      method: 'POST',
      body: {
        instructionDocIds,
        instructionDocStatus: key,
      },
    }).then(_res => {
      const res = getResponse(_res);
      if (res?.success) {
        notification.success({});
        headerTableDs.batchUnSelect(headerTableDs.selected);
        headerTableDs.clearCachedSelected();
        setSelectedStatus([]);
        headerTableDs.query(props.headerTableDs.currentPage);
      }
    });
  };

  const handlePrint = async () => {
    const result = await request(
      `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/wms_customer_supplied_parts/instruction/doc/print`,
      {
        method: 'POST',
        responseType: 'blob',
        body: printList,
      },
    );
    const res = getResponse(result);
    if (res) {
      if (res.type === 'application/json') {
        const fileReader = new FileReader();
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result);
          getResponse(jsonData);
        };
        fileReader.readAsText(res);
      } else {
        const file = new Blob([res], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);
        const newwindow = window.open(fileURL, 'newwindow');
        if (newwindow) {
          newwindow.print();
          notification.success({
            message: '打印成功',
          });
        } else {
          notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
        }
      }
    }
  };

  const menu = (
    <Menu onClick={clickMenu} className={styles['split-menu']} style={{ width: '100px' }}>
      <Menu.Item key="CANCEL" disabled={isAllEqualWithKeyWord(selectedStatus, 'CANCEL')}>
        <a target="_blank" rel="noopener noreferrer">
          {intl.get(`${modelPrompt}.button.cancel`).d('取消')}
        </a>
      </Menu.Item>
      <Menu.Item key="CLOSED" disabled={isAllEqualWithKeyWord(selectedStatus, 'CLOSED')}>
        <a target="_blank" rel="noopener noreferrer">
          {intl.get(`${modelPrompt}.button.close`).d('关闭')}
        </a>
      </Menu.Item>
      <Menu.Item key="COMPLETED" disabled={isAllEqualWithKeyWord(selectedStatus, 'COMPLETED')}>
        <a target="_blank" rel="noopener noreferrer">
          {intl.get(`${modelPrompt}.button.completed`).d('完成')}
        </a>
      </Menu.Item>
    </Menu>
  );

  const extraButton = (
    <Button onClick={handleCreateMaterial} disabled={!selectedItem.length} color="primary">
      {intl.get(`${modelPrompt}.button.createMaterialBatch11`).d('创建箱标签')}
    </Button>
  );

  const getExportQueryParams = () => {
    if (!headerTableDs.queryDataSet || !headerTableDs.queryDataSet.current) {
      return {};
    }
    const queryParams = headerTableDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    });
    return {
      ...queryParams,
      instructionDocIds: headerTableDs.selected?.map(item => {
        return item.get('instructionDocId');
      })?.join(','),
    };
  };

  return (
    <div className="hmes-style">
      <Spin spinning={loading}>
        <Header
          title={intl.get(`${modelPrompt}.title.purchaseDeliveryManagement`).d('客供件管理平台')}
        >
          <Dropdown
            overlay={menu}
            disabled={
              selectedStatus.length === 0 ||
              (isAllEqualWithKeyWord(selectedStatus, 'CLOSED') &&
                isAllEqualWithKeyWord(selectedStatus, 'CANCEL') &&
                isAllEqualWithKeyWord(selectedStatus, 'COMPLETED'))
            }
          >
            <PermissionButton
              type="c7n-pro"
              icon="cached"
              disabled={
                selectedStatus.length === 0 ||
                (isAllEqualWithKeyWord(selectedStatus, 'CLOSED') &&
                  isAllEqualWithKeyWord(selectedStatus, 'CANCEL') &&
                  isAllEqualWithKeyWord(selectedStatus, 'COMPLETED'))
              }
              permissionList={[
                {
                  code: `${path}.button.edit`,
                  type: 'button',
                  meaning: '列表页-编辑新建删除复制按钮',
                },
              ]}
            >
              {intl.get(`${modelPrompt}.button.changeStatus`).d('状态变更')}
            </PermissionButton>
          </Dropdown>
          <Button onClick={handlePrint} disabled={printList.length === 0} color="primary">
            {intl.get(`${modelPrompt}.button.print`).d('打印')}
          </Button>
          <ExcelExport
            method="GET"
            exportAsync
            requestUrl={`${BASIC.TARZAN_REPORT}/v1/${tenantId}/wms_customer_supplied_parts/export/ui`}
            queryParams={getExportQueryParams}
            buttonText={intl.get(`${modelPrompt}.button.export`).d('导出')}
          />
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={() => props.history.push(`/hwms/customer-supplied-parts/detail/create`)}
            permissionList={[
              {
                code: `${path}.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get(`${modelPrompt}.button.create`).d('新建')}
          </PermissionButton>
          {/* <FRPrintButton
            kid="DELIVERY_DOC_MANAGEMENT" // 使用打印组件的功能的唯一标识，业务提供
            queryParams={printList} // 查询数据
            disabled={printList.length === 0}
            printObjectType="INSTRUCTION_DOC"
          /> */}
        </Header>
        <Content className={styles['deliver-content']}>
          {customizeTable(
            {
              filterCode: `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST.QUERY`,
              code: `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST.HEAD`,
            },
            <Table
              searchCode="shdgl1"
              customizedCode="shdgl1"
              queryFieldsLimit={10}
              dataSet={headerTableDs}
              columns={headerTableColumns}
              highLightRow
              queryBar="filterBar"
              queryBarProps={{
                fuzzyQuery: false,
              }}
              onChange={handleLineTableChange}
              onRow={({ record }) => {
                return {
                  onClick: () => {
                    headerRowClick(record);
                  },
                };
              }}
            />,
          )}
          <Collapse bordered={false} collapsible="icon" defaultActiveKey={['basicInfo']}>
            <Panel
              header={intl.get(`${modelPrompt}.line.information`).d('行信息')}
              key="basicInfo"
              extra={extraButton}
              dataSet={lineTableDs}
            >
              {lineTableDs &&
                customizeTable(
                  {
                    code: `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST.LINE`,
                  },
                  <Table
                    customizedCode="shdgl2"
                    className={styles['expand-table']}
                    dataSet={lineTableDs}
                    highLightRow={false}
                    columns={lineTableColumns}
                  />,
                )}
            </Panel>
          </Collapse>
        </Content>
      </Spin>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      const lineTableDs = new DataSet({ ...lineTableDS() });
      return {
        headerTableDs,
        lineTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({
    unitCode: [
      // `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST.QUERY`,
      // `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST.HEAD`,
      // `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST.LINE`,
      // `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST_LINE_DETAIL.QUERY`,
      // `${BASIC.CUSZ_CODE_BEFORE}.DELIVERY_LIST_MATERIAL_LOT.QUERY`,
    ],
  }),
  formatterCollections({ code: ['tarzan.hmes.customerSuppliedParts', 'tarzan.common'] }),
)(Order);
