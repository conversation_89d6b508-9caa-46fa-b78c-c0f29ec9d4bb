/* * @Author: xu xiaoyu
 * @Date: 2023-06-20 09:50:34
 * @Last Modified by: xu xiaoyu
 * @Last Modified time: 2023-09-13 14:59:33
 * */
import React, { useState, useEffect } from 'react';
import { DataSet, Table, Button, Lov, Select, NumberField, TextField } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { Header, Content } from 'components/Page';
import formatterCollections from 'utils/intl/formatterCollections';
import { getCurrentOrganizationId, getResponse } from 'utils/utils';
import withProps from 'utils/withProps';
import { flow } from 'lodash';
import notification from 'utils/notification';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import { headerTableDS } from './stores/ListDS';

const modelPrompt = 'tarzan.wms.InoutFactoryMasterData';
const tenantId = getCurrentOrganizationId();

// const Host = `/yp-wms-33139`
const Host = `${BASIC.HMES_BASIC}`;

const Order = props => {
  const { headerTableDs } = props;

  const [loading, setLoading] = useState(false);
  const [cancelDisable, setCancelDisabled] = useState(true);
  const [selectRow, setSelectRow] = useState(false);

  useEffect(() => {
    if (headerTableDs) {
      headerTableDs.addEventListener('query', () => {
        // setSelectRow([]);
        setCancelDisabled(true);
      });
      headerTableDs.addEventListener('select', handleDataSetSelectUpdate);
      headerTableDs.addEventListener('unSelect', handleDataSetSelectUpdate);
      headerTableDs.addEventListener('selectAll', handleDataSetSelectUpdate);
      headerTableDs.addEventListener('unSelectAll', handleDataSetSelectUpdate);
    }
    return () => {
      if (headerTableDs) {
        headerTableDs.removeEventListener('select', handleDataSetSelectUpdate);
        headerTableDs.removeEventListener('unSelect', handleDataSetSelectUpdate);
        headerTableDs.removeEventListener('selectAll', handleDataSetSelectUpdate);
        headerTableDs.removeEventListener('unSelectAll', handleDataSetSelectUpdate);
      }
    };
  });

  const handleDataSetSelectUpdate = () => {
    setSelectRow(headerTableDs.selected);
    setCancelDisabled(!headerTableDs.selected.length);
  };

  const handleChangeLov = async record => {
    if (record.get('siteId') && record.get('materialId')) {
      const res = await request(`${Host}/v1/${tenantId}/wms-in-out-plant-datas/selectJIT`, {
        method: 'POST',
        body: {
          siteId: record.get('siteId'),
          materialId: record.get('materialId'),
        },
      });
      const response = getResponse(res);
      if (response) {
        record.set('jitFlag', response.jitFlag);
      }
    }
  };

  const handleChangeValue = async record => {
    record.set('fromLocatorId', null);
    record.set('fromLocatorCode', '');
    record.set('supplierId', null);
    record.set('supplierCode', '');
  };

  const headerTableColumns = [
    {
      name: 'siteLov',
      width: 130,
      renderer: ({ record }) => record?.get('siteCode'),
      editor: record =>
        record.getState('editing') && <Lov onChange={() => handleChangeLov(record)} />,
    },
    {
      name: 'materialLov',
      width: 130,
      renderer: ({ record }) => record?.get('materialCode'),
      editor: record =>
        record.getState('editing') && <Lov onChange={() => handleChangeLov(record)} />,
    },
    {
      name: 'materialName',
      width: 180,
    },
    {
      name: 'toLocatorCodeLov',
      width: 130,
      renderer: ({ record }) => record?.get('toLocatorCode'),
      editor: record => record.getState('editing'),
    },
    {
      name: 'category',
      width: 120,
      editor: record =>
        record.getState('editing') && <Select onChange={() => handleChangeValue(record)} />,
    },
    {
      name: 'pullType',
      width: 120,
      editor: record =>
        record.getState('editing') && <Select onChange={() => handleChangeValue(record)} />,
    },
    {
      name: 'fromLocatorCodeLov',
      width: 130,
      renderer: ({ record }) => record?.get('fromLocatorCode'),
      editor: record => record.getState('editing'),
    },
    {
      name: 'sourceSupplierLov',
      width: 120,
      renderer: ({ record }) => record?.get('supplierCode'),
      editor: record => record.getState('editing'),
    },
    {
      name: 'safeInventory',
      width: 100,
      editor: record => record.getState('editing'),
    },
    {
      name: 'maxInventory',
      width: 100,
      editor: record => record.getState('editing'),
    },
    {
      name: 'pullBatch',
      editor: record => record.getState('editing'),
      width: 120,
    },
    {
      name: 'deliveryCycle',
      width: 100,
      editor: record => record.getState('editing'),
    },
    {
      name: 'uomCodeLov',
      width: 150,
      renderer: ({ record }) => record?.get('uomCode'),
      editor: record => record.getState('editing'),
    },
    {
      name: 'overdueWarn',
      width: 150,
      editor: record => record.getState('editing'),
    },
    {
      name: 'overdueUomCodeLov',
      width: 160,
      renderer: ({ record }) => record?.get('overdueUomCode'),
      editor: record => record.getState('editing'),
    },
    {
      name: 'enableFlag',
      renderer: ({ record }) =>
        record.get('enableFlag') === 'Y'
          ? intl.get('tarzan.common.label.yes').d('是')
          : intl.get('tarzan.common.label.no').d('否'),
      editor: record => record.getState('editing'),
    },
    {
      name: 'createdByName',
      width: 100,
    },
    {
      name: 'creationDate',
      width: 150,
    },
    {
      name: 'lastUpdatedByName',
      width: 100,
    },
    {
      name: 'lastUpdateDate',
      width: 150,
    },
  ];
  const handleEdit = () => {
    selectRow.forEach(record => {
      record.setState('editing', true);
    });
  };
  const handleCancel = () => {
    headerTableDs.forEach(record => {
      if (record.status === 'add') {
        headerTableDs.unSelect(record);
        headerTableDs.remove(record);
      } else {
        record.reset();
        record.setState('editing', false);
      }
    });
    if (!selectRow.length) {
      setCancelDisabled(true);
    }
  };
  const handleSave = () => {
    const promiss = headerTableDs.selected.map(record => record.validate());
    const canSave = headerTableDs.selected.map(record => record.getState('editing'));
    if (canSave.every(item => !item)) {
      return;
    }
    const temp = headerTableDs.selected.filter(record => record.getState('editing'));
    const bodyArr = temp.map(record => record.toJSONData());
    Promise.all(promiss).then(arr => {
      if (arr.every(item => item)) {
        setLoading(true);
        request(`${Host}/v1/${tenantId}/wms-in-out-plant-datas/save`, {
          method: 'POST',
          body: bodyArr,
        }).then(res => {
          setLoading(false);
          const response = getResponse(res);
          if (response) {
            notification.success({
              message: intl.get(`${modelPrompt}.save.success`).d('保存成功！'),
            });
            headerTableDs.forEach(record => {
              record.setState('editing', false);
            });
            headerTableDs.query(headerTableDs.currentPage);
          }
        });
      }
    });
  };
  const handleCreate = () => {
    setCancelDisabled(false);
    headerTableDs.create({}, 0);
    headerTableDs.current.setState('editing', true);
    headerTableDs.select(0);
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('厂内/厂外拉动主数据')}>
        <Button loading={loading} onClick={handleCreate} color="primary">
          {intl.get(`tarzan.common.button.create`).d('新建')}
        </Button>
        <Button loading={loading} disabled={cancelDisable} onClick={handleEdit} color="primary">
          {intl.get(`tarzan.common.button.edit`).d('编辑')}
        </Button>
        <Button loading={loading} disabled={cancelDisable} onClick={handleSave} color="primary">
          {intl.get(`tarzan.common.button.save`).d('保存')}
        </Button>
        <Button loading={loading} disabled={cancelDisable} onClick={handleCancel} color="primary">
          {intl.get(`tarzan.common.button.cancel`).d('取消')}
        </Button>
      </Header>
      <Content>
        <Table
          queryFieldsLimit={6}
          searchCode="inoutFactoryMasterData"
          customizedCode="inoutFactoryMasterData"
          queryBar="filterBar"
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={headerTableDs}
          columns={headerTableColumns}
        />
        ,
      </Content>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const headerTableDs = new DataSet({ ...headerTableDS() });
      return {
        headerTableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  formatterCollections({ code: ['tarzan.wms.InoutFactoryMasterData', 'tarzan.common', 'hzero.c7nProUI'] }),
)(Order);
