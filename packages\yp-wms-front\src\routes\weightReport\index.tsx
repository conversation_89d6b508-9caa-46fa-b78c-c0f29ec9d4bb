/**
 * @Description: 称重数据报表
 * @Author: <EMAIL>
 * @Date: 2024/1/11 11:30
 * @LastEditTime: 2024/1/11 15:40:05
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo } from 'react';
import {
  Button,
  DataSet,
  Form,
  Lov,
  Modal,
  NumberField,
  Select,
  Table,
  TextField,
} from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import formatterCollections from 'utils/intl/formatterCollections';
import intl from 'utils/intl';
import withProps from 'utils/withProps';
import { Content, Header } from 'components/Page';
import { BASIC } from '@utils/config';
import {isNil} from 'lodash';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '@components/tarzan-hooks';
import notification from 'utils/notification';
import ExcelExport from 'components/ExcelExport';
import { ColumnAlign, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor, ButtonType } from 'choerodon-ui/pro/lib/button/enum';
import { drawerDS, listDS } from './stories';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.wms.weightReport';

const WeightList = props => {
  const {
    listDs,
  } = props;
  let _createDrawer;
  const drawerDs: DataSet = useMemo(() => new DataSet(drawerDS()), []);
  const { run: handleSave } = useRequest(
    {
      url: `${BASIC.HMES_BASIC}/v1/${tenantId}/wms-weighbridges/save`,
      method: 'POST',
    },
    {
      manual: true,
      needPromise: true,
    },
  );

  const getExportQueryParams = () => {
    const weighbridgeIds = listDs.selected.map(item => item?.toData()?.weighbridgeId);
    if (weighbridgeIds.length > 0) {
      return {
        weighbridgeIds: weighbridgeIds.join(','),
      };
    }
    if (!listDs.queryDataSet || !listDs.queryDataSet.current) {
      return {};
    }
    const queryParams = listDs.queryDataSet.current.toData();
    Object.keys(queryParams).forEach(i => {
      if (isNil(queryParams[i])) {
        delete queryParams[i];
      }
    })
    return queryParams;

  };

  // 抽屉中-保存
  const handleToSave = async () => {
    drawerDs.current!.set({ nowData: new Date() });
    const validate = await drawerDs.validate();
    if (validate) {
      _createDrawer.update({
        footer: handleFooter(true),
      });
      return handleSave({
        params: {
          ...drawerDs.toData()[0],
        },
      }).then(res => {
        if (JSON.stringify(res)==='{}') {                    
          notification.success({});
          _createDrawer.update({
            footer: handleFooter(false),
          });
          _createDrawer.close();
          listDs.query();
        } else {
          _createDrawer.update({
            footer: handleFooter(false),
          }); 
        }
      });
    }
  };

  // 控制抽屉footer的更新
  const handleFooter = (val: boolean) => {
    return (
      <>
        <div style={{ float: 'right' }}>
          <Button onClick={() => _createDrawer.close()}>
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            onClick={handleToSave}
            type={ButtonType.submit}
            color={ButtonColor.primary}
            loading={val}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </div>
      </>
    );
  };

  // 点击“新增”按钮的回调
  const openCreateDrawer = (data?) => {
    if (data) {
      drawerDs.loadData([data])
    } else {
      drawerDs.loadData([])
    }
    _createDrawer = Modal.open({
      key: Modal.key(),
      title: data ? intl.get(`${modelPrompt}.title.editDrawer`).d('编辑称重数据报表') : intl.get(`${modelPrompt}.title.newDrawer`).d('新增称重数据报表'),
      drawer: true,
      closable: true,
      style: {
        width: 360,
      },
      className: 'hmes-style-modal',
      children: (
        <>
          <Form dataSet={drawerDs} columns={1} labelWidth={112}>
            <TextField name="instructionDocNum" />
            <Select name="instructionDocType" />
            <Lov name="materialLov" />
            <TextField name="numberPlate" />
            <TextField name="uom" />
            <NumberField name="gweight" />
            <NumberField name="tweight" />
            <NumberField name="nweight" />
          </Form>
        </>
      ),
      footer: handleFooter(false),
      onClose: () => drawerDs.reset(),
    });
  };

  const tableColumns: ColumnProps[] = [
    {
      name: 'instructionDocNum',
      width: 170,
    },
    { name: 'instructionDocType', width: 150 },
    { name: 'materialCode', width: 170 },
    { name: 'materialName' },
    { name: 'numberPlate', width: 150 },
    { name: 'uom', width: 150 },
    { name: 'gweight', width: 150 },
    { name: 'tweight', width: 150 },
    { name: 'nweight', width: 150 },
    { name: 'sumQty', width: 150 },
    {
      name: 'creationDate',
      width: 150,
      align: ColumnAlign.center,
    },
    { name: 'createdByName', width: 150 },
    {
      name: 'lastUpdateDate',
      width: 150,
      align: ColumnAlign.center,
    },
    { name: 'lastUpdatedByName', width: 150 },
  ];

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title.weight.report`).d('称重数据报表')}>
        <PermissionButton
          type="c7n-pro"
          color={ButtonColor.primary}
          icon="add"
          onClick={() => openCreateDrawer()}
        >
          {intl.get(`${modelPrompt}.button.create`).d('新建')}
        </PermissionButton>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${BASIC.HMES_BASIC}/v1/${tenantId}/wms-weighbridges/export`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        <Table
          queryFieldsLimit={8}
          queryBar={TableQueryBarType.filterBar}
          queryBarProps={{
            fuzzyQuery: false,
          }}
          dataSet={listDs}
          columns={tableColumns}
          searchCode="weightReport"
          customizedCode="weightReport"
        />
      </Content>
    </div>
  );
};

export default formatterCollections({
  code: ['tarzan.wms.weight.report', 'tarzan.common'],
})(
  withProps(
    () => {
      const listDs = new DataSet({ ...listDS() });
      return {
        listDs,
      };
    },
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  )(WeightList),
);
