import intl from 'utils/intl';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import moment from 'moment';

const modelPrompt = 'tarzan.mes.sluggishReport';
const tenantId = getCurrentOrganizationId();

export const tableDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  queryFields: [
    {
      name: 'operationDateFrom',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.operationDateFrom`).d('时间从'),
      max: 'operationDateTo',
      required: true,
      dynamicProps: {
        min: ({ record }) => {
          const operationDateTo = record?.get('operationDateTo');
          return operationDateTo ? moment(operationDateTo).subtract(1, 'months') : null;
        },
      },
    },
    {
      name: 'operationDateTo',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.dateTo`).d('时间至'),
      min: 'operationDateFrom',
      required: true,
      dynamicProps: {
        max: ({ record }) => {
          const operationDateFrom = record?.get('operationDateFrom');
          return operationDateFrom ? moment(operationDateFrom).add(1, 'months') : null;
        },
      },
    },
    {
      name: 'eventId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'materialLotCodeList',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCodeList`).d('物料批编码'),
      transformRequest: (val, record) => {
        if (val?.trim().includes(' ')) {
          return record.set(
            'materialLotCodeList',
            val
              .trim()
              .split(' ')
              .join(','),
          );
        }
        return val;
      },
    },
    {
      name: 'material',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL',
      noCache: true,
      multiple: true,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'materialId',
      type: FieldType.string,
      bind: 'material.materialId',
    },
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorLov`).d('库位'),
      lovCode: 'MT.MODEL.LOCATOR',
      ignore: FieldIgnore.always,
      multiple: true,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteIds: [record.get('siteId')],
            type: 'LOCATOR',
            locatorCategoryList: ['INVENTORY', 'LOCATION'],
          };
        },
      },
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'warehouse',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.warehouse`).d('仓库'),
      lovCode: 'MT.MODEL.LOCATOR_CATEGORY',
      multiple: true,
      lovPara: {
        tenantId,
        locatorCategory: ['AREA'],
      },
      noCache: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'wareHouseId',
      type: FieldType.string,
      bind: 'warehouse.locatorId',
    },
    {
      name: 'operation',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operation`).d('类型（冻结/解冻）'),
      lookupCode: 'WMS.INVENTORY_FREEZE_ACTUAL',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'operationType',
      type: FieldType.string,
      bind: 'operation.description',
    },
    {
      name: 'operationByLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.operationBy`).d('执行人'),
      ignore: FieldIgnore.always,
      lovCode: 'HIAM.USER.ORG',
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'operationBy',
      bind: 'operationByLov.id',
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  fields: [
    {
      name: 'eventTypeDesc',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eventTypeDesc`).d('事件类型'),
    },
    {
      name: 'eventId',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eventId`).d('事件ID'),
    },
    {
      name: 'materialLotCode',
      label: intl.get(`${modelPrompt}.form.materialLotCode`).d('物料批编码'),
      type: FieldType.string,
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位'),
    },
    {
      name: 'wareHouseCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wareHouseCode`).d('仓库'),
    },
    {
      name: 'currentContainerCode',
      label: intl.get(`${modelPrompt}.currentContainerCode`).d('上层托盘'),
      type: FieldType.string,
    },
    {
      name: 'topContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerCode`).d('下层托盘'),
    },
    {
      name: 'operationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.operationDate`).d('时间'),
    },
    {
      name: 'operateByDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operateByDesc`).d('执行人'),
    },
    {
      name: 'operationTypeDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationTypeDesc`).d('类型（冻结/解冻）'),
    },
    {
      name: 'reason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reason`).d('原因'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.TARZAN_REPORT}/v1/${tenantId}/freeze-actual/report`,
        method: 'POST',
      };
    },
  },
});
