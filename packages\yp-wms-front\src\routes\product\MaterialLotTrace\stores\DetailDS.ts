/**
 * @Description: 物料批管理平台-详情页DS
 * @Author: <<EMAIL>>
 * @Date: 2022-01-18 15:09:34
 * @LastEditTime: 2022-01-29 18:17:31
 * @LastEditors: <<EMAIL>>
 */

import intl from 'utils/intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldIgnore, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import uuid from 'uuid/v4';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.product.materialLotTrance';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  fields: [
    {
      name: 'materialLotId', // 物料批主键
    },
    // 基础属性 start
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('createMultiCodeNum') >= 2
        },
      },
    },
    {
      name: 'createMultiCodeNum',
      type: FieldType.number,
    },
    {
      name: 'siteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.siteLov`).d('站点'),
      lovCode: 'MT.MODEL.SITE2',
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
        enableFlag: 'Y',
        noworthFlag: 'Y'
      },
      required: true,
    },
    {
      name: 'siteCode',
      bind: 'siteLov.siteCode',
    },
    {
      name: 'siteId',
      bind: 'siteLov.siteId',
    },
    {
      name: 'createReason',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createReason`).d('创建原因'),
      defaultValue: 'INITIALIZE',
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=CREATE_REASON`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
      defaultValue: 'PENDING',
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?statusGroup=QUALITY_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      required: true,
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('物料批标识'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('createMultiCodeNum') >= 2
        },
      },
    },
    {
      name: 'materialLotStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotStatus`).d('物料批状态'),
      textField: 'description',
      valueField: 'statusCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-status/combo-box/ui?module=ORDER&statusGroup=MATERIAL_LOT_STATUS`,
      lookupAxiosConfig: {
        transformResponse(data) {
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'enableFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.enableFlag`).d('启用状态'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'reservedFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reservedFlag`).d('预留标识'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'overOrderInterceptionFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.overOrderInterceptionFlag`).d('跨订单拦截标识'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'freezeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.freezeFlag`).d('冻结标识'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'stocktakeFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stocktakeFlag`).d('盘点停用标识'),
      defaultValue: 'N',
      trueValue: 'Y',
      falseValue: 'N',
    },
    {
      name: 'productionDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.productionDate`).d('生产日期'),
      required: true,
      max: 'expirationDate',
    },
    {
      name: 'expirationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.expirationDate`).d('到期日期'),
      min: 'productionDate',
      dynamicProps: {
        disabled: ({ record }) => {
          return !(record.get('siteId') && record.get('materialId'));
        },
      },
    },
    {
      name: 'extendedShelfLifeTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.extendedShelfLifeTimes`).d('延保次数'),
      step: 1,
      min: 0,
    },
    {
      name: 'supplierLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierLot`).d('供应商批次'),
    },
    // 基础属性 end
    // 实物信息 start
    {
      name: 'materialLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
      lovCode: 'MT.MATERIAL.MISCELLANEOUS.SITE',
      lovPara: {
        tenantId,
        enableFlag: 'Y',
      },
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            enableFlag: 'Y',
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
      },
      required: true,
      noCache: true,
      ignore: FieldIgnore.always,
    },
    {
      name: 'materialId',
      bind: 'materialLov.materialId',
    },
    {
      name: 'materialCode',
      bind: 'materialLov.materialCode',
    },
    {
      name: 'revisionFlag',
      type: FieldType.string,
      bind: 'materialLov.revisionFlag',
    },
    {
      name: 'primaryUomType',
      type: FieldType.string,
      bind: 'materialLov.primaryUomType',
    },
    {
      name: 'secondaryUomType',
      type: FieldType.string,
      bind: 'materialLov.secondaryUomType',
    },
    {
      // 物料版本
      name: 'revisionCode',
      type: FieldType.string,
      textField: 'description',
      valueField: 'description',
      noCache: true,
      lookupAxiosConfig: () => {
        return {
          transformResponse(data) {
            if (data instanceof Array) {
              return data;
            }
            const { rows } = JSON.parse(data);
            const firstlyQueryData = [] as Array<object>;
            if (rows instanceof Array) {
              rows.forEach(item => {
                firstlyQueryData.push({
                  kid: uuid(),
                  description: item,
                });
              });
            }
            return firstlyQueryData;
          },
        };
      },
      dynamicProps: {
        lookupUrl: ({ record }) => {
          if (
            !record.get('materialId') ||
            !record.get('siteId') ||
            record.get('revisionFlag') !== 'Y'
          ) {
            return;
          }
          return `${
            BASIC.TARZAN_METHOD
          }/v1/${tenantId}/mt-material/site-material/limit/lov/ui?materialId=${record.get(
            'materialId',
          )}&siteIds=${record.get('siteId')}`;
        },
        disabled: ({ record }) => {
          return (
            !record.get('materialId') || !record.get('siteId') || record.get('revisionFlag') !== 'Y'
          );
        },
        required: ({ record }) => {
          return record.get('revisionFlag') === 'Y';
        },
      },
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
      bind: 'materialLov.materialName',
      disabled: true,
    },
    {
      name: 'secondaryUomQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.secondaryUomQty`).d('辅单位数量'),
      dynamicProps: {
        // 根据物料是否带有secondaryUomType，来判断物料是否使用辅单位
        required: ({ record }) => {
          return record.get('secondaryUomType');
        },
        disabled: ({ record }) => {
          return !record.get('secondaryUomType') || record.get('createReason') === 'INVENTORY';
        },
        defaultValue: ({ record }) => {
          if (record.get('createReason') === 'INVENTORY' && record.get('secondaryUomType')) {
            return 0;
          }

        },
        min: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumberSec')).toFixed(record?.get('decimalNumberSec')),
          );
        },
        precision: ({ record }) => {
          return record?.get('decimalNumberSec');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumberSec')).toFixed(record?.get('decimalNumberSec')),
          );
        },
      },
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('主单位数量'),
      dynamicProps: {
        required: ({ record }) => {
          return record.get('materialId');
        },
        disabled: ({ record }) => {
          return !record.get('materialId') || record.get('createReason') === 'INVENTORY';
        },
        defaultValue: ({ record }) => {
          if (record.get('createReason') === 'INVENTORY') {
            return 0;
          }
        },
        min: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
        precision: ({ record }) => {
          return record?.get('decimalNumber');
        },
        step: ({ record }) => {
          return parseFloat(
            (10 ** -record?.get('decimalNumber')).toFixed(record?.get('decimalNumber')),
          );
        },
      },
    },
    {
      name: 'primaryUomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.primaryUomCode`).d('主单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        required: ({ record }) => {
          return record.get('materialId');
        },
        disabled: ({ record }) => {
          return !record.get('materialId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            uomType: record.get('primaryUomType'),
          };
        },
      },
    },
    {
      name: 'decimalNumber',
      bind: 'primaryUomLov.decimalNumber',
    },
    {
      name: 'primaryUomId',
      bind: 'primaryUomLov.uomId',
    },
    {
      name: 'primaryUomCode',
      bind: 'primaryUomLov.uomCode',
    },
    {
      name: 'secondaryUomLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.secondaryUomCode`).d('辅单位'),
      lovCode: 'MT.COMMON.UOM',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        // 根据物料是否带有secondaryUomId，来判断物料是否使用辅单位
        required: ({ record }) => {
          return record.get('secondaryUomType');
        },
        disabled: ({ record }) => {
          return !record.get('secondaryUomType');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            uomType: record.get('secondaryUomType'),
          };
        },
      },
    },
    {
      name: 'decimalNumberSec',
      bind: 'secondaryUomLov.decimalNumber',
    },
    {
      name: 'secondaryUomId',
      bind: 'secondaryUomLov.uomId',
    },
    {
      name: 'secondaryUomCode',
      bind: 'secondaryUomLov.uomCode',
    },
    // 实物信息 end
    // 位置信息 start
    {
      name: 'locatorLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.locatorCode`).d('库位编码'),
      lovCode: 'MT.MODEL.USER.SITE.LOCATOR',
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            siteId: record.get('siteId'),
          };
        },
        disabled: ({ record }) => {
          return !record.get('siteId');
        },
        required: ({ record }) => {
          return record.get('siteId') && record.get('enableFlag') === 'Y';
        },
      },
    },
    {
      name: 'locatorId',
      bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorCode',
      bind: 'locatorLov.locatorCode',
    },
    {
      name: 'wareHouse',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.wareHouse`).d('所属仓库'),
      disabled: true,
    },
    {
      name: 'inSiteTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.inSiteTime`).d('入站时间'),
    },
    {
      name: 'currentContainerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.currentContainerCode`).d('装载容器编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.CONTAINER`,
      ignore: FieldIgnore.always,
      lovPara: {
        tenantId,
      },
    },
    {
      name: 'currentContainerId',
      bind: 'currentContainerLov.containerId',
    },
    {
      name: 'currentContainerCode',
      bind: 'currentContainerLov.containerCode',
    },
    {
      name: 'topContainerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topContainerCode`).d('顶层容器编码'),
      disabled: true,
    },
    {
      name: 'inLocatorTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.inLocatorTime`).d('入库时间'),
    },
    {
      name: 'assembleToolLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.assembleToolCode`).d('装配器具编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.ASSEMBLE_TOOL`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'assembleToolId',
      bind: 'assembleToolLov.assembleToolId',
    },
    {
      name: 'assembleToolCode',
      bind: 'assembleToolLov.assembleToolCode',
    },
    {
      name: 'assemblePointLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.ASSEMBLE_POINT`,
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('assembleToolId');
        },
        lovPara: ({ record }) => {
          return {
            assembleToolId: record.get('assembleToolId'),
            tenantId,
          };
        },
      },
    },
    {
      name: 'assemblePointId',
      bind: 'assemblePointLov.assemblePointId',
    },
    {
      name: 'assemblePointCode',
      bind: 'assemblePointLov.assemblePointCode',
    },
    // {
    //   name: 'assemblePointCode',
    //   type: FieldType.string,
    //   label: intl.get(`${modelPrompt}.assemblePointCode`).d('装配点编码'),
    //   bind: 'assembleToolLov.assemblePointCode',
    // },
    {
      name: 'loadTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.loadTime`).d('装载时间'),
    },
    {
      name: 'unloadTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.unloadTime`).d('卸载时间'),
    },
    // 位置信息 end
    // 所有权及来源信息 start
    {
      name: 'ownerType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerTypeDesc`).d('所有者类型'),
      textField: 'description',
      valueField: 'typeCode',
      defaultValue: '',
      options: new DataSet({
        autoQuery: true,
        dataKey: 'rows',
        paging: false,
        transport: {
          read: () => {
            return {
              url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=OWNER_TYPE`,
              method: 'GET',
              params: { tenantId },
              transformResponse: val => {
                const data = JSON.parse(val);
                data.rows.push({
                  description: intl.get(`tarzan.common.ownerType`).d('自有'),
                  typeCode: '',
                  typeGroup: 'OWNER_TYPE',
                });
                return {
                  ...data,
                };
              },
            };
          },
        },
      }),
    },
    {
      name: 'ownerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.ownerCode`).d('所有者编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovCode({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'MT.MODEL.CUSTOMER';
          } if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'MT.MODEL.SUPPLIER';
          } if (record.get('ownerType') === 'OI') {
            return `${BASIC.LOV_CODE_BEFORE}.MES.SO_LINE`;
          }
          return '';

        },
        textField({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'customerCode';
          } if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'supplierCode';
          } if (record.get('ownerType') === 'OI') {
            return 'soNumContent';
          }
          return '';

        },
        disabled({ record }) {
          return !['CI', 'IIC', 'SI', 'IIS', 'OI'].includes(record.get('ownerType'));
        },
        required({ record }) {
          return ['CI', 'IIC', 'SI', 'IIS', 'OI'].includes(record.get('ownerType'));
        },
      },
    },
    {
      name: 'ownerId',
      type: FieldType.number,
      bind: 'ownerLov.customerId',
      dynamicProps: {
        bind({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'ownerLov.customerId';
          }
          if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'ownerLov.supplierId';
          }
          if (record.get('ownerType') === 'OI') {
            return 'ownerLov.soLineId';
          }
          return 'ownerLov.customerId';
        },
      },
    },
    {
      name: 'ownerCode',
      type: FieldType.string,
      dynamicProps: {
        bind: ({ record }) => {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'ownerLov.customerCode';
          } if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'ownerLov.supplierCode';
          } if (record.get('ownerType') === 'OI') {
            return 'ownerLov.soNumContent';
          }
          return '';

        },
      },
    },
    {
      name: 'ownerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ownerDesc`).d('所有者描述'),
      bind: 'ownerLov.customerName',
      disabled: true,
      dynamicProps: {
        bind({ record }) {
          if (record.get('ownerType') === 'CI' || record.get('ownerType') === 'IIC') {
            return 'ownerLov.customerName';
          }
          if (record.get('ownerType') === 'SI' || record.get('ownerType') === 'IIS') {
            return 'ownerLov.supplierName';
          }
          return 'ownerLov.customerName';
        },
      },
    },
    {
      name: 'supplierLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierCode`).d('供应商编码'),
      lovCode: 'MT.MODEL.SUPPLIER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'supplierId',
      bind: 'supplierLov.supplierId',
    },
    {
      name: 'supplierCode',
      bind: 'supplierLov.supplierCode',
    },
    {
      name: 'supplierDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.supplierDesc`).d('供应商描述'),
      bind: 'supplierLov.supplierName',
      disabled: true,
    },
    {
      name: 'eoLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.EO`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'eoId',
      bind: 'eoLov.eoId',
    },
    {
      name: 'eoNum',
      bind: 'eoLov.eoNum',
    },
    {
      name: 'supplierSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.supplierSiteCode`).d('供应商地点编码'),
      lovCode: 'MT.MODEL.SUPPLIER_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('supplierId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            supplierId: record.get('supplierId'),
          };
        },
      },
    },
    {
      name: 'supplierSiteId',
      bind: 'supplierSiteLov.supplierSiteId',
    },
    {
      name: 'supplierSiteCode',
      bind: 'supplierSiteLov.supplierSiteCode',
    },
    {
      name: 'supplierSiteDesc',
      label: intl.get(`${modelPrompt}.supplierSiteDesc`).d('供应商地点描述'),
      bind: 'supplierSiteLov.supplierSiteName',
      disabled: true,
    },
    {
      name: 'ovenNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ovenNumber`).d('炉号'),
    },
    {
      name: 'instructionDocLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('指令单据编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.INSTRUCTION_DOC`,
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'instructionDocId',
      bind: 'instructionDocLov.instructionDocId',
    },
    {
      name: 'instructionDocNum',
      bind: 'instructionDocLov.instructionDocNum',
    },
    {
      name: 'instructionLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.instructionNum`).d('指令编码'),
      lovCode: `${BASIC.LOV_CODE_BEFORE}.MES.INSTRUCTION`,
      ignore: FieldIgnore.always,
      dynamicProps: {
        lovPara: ({ record }) => {
          return {
            tenantId,
            instructionDocId: record.get('instructionDocId'),
          };
        },
        textField({ record }) {
          if (record.get('instructionDocId')) {
            return 'lineNumber';
          }
          return 'instructionNum';

        },
      },
    },
    {
      name: 'instructionId',
      bind: 'instructionLov.instructionId',
    },
    {
      name: 'instructionNum',
      bind: 'instructionLov.instructionNum',
    },
    {
      name: 'instructionNum',
      bind: 'instructionLov.lineNumber',
    },
    // 所有权及来源信息 end
    // 客户及预留信息 start
    {
      name: 'reservedObjectType',
      type: FieldType.string,
      textField: 'description',
      valueField: 'typeCode',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=RESERVE_OBJECT_TYPE`,
      lookupAxiosConfig: {
        transformResponse(data) {
          // data会有缓存，第一次查询得到结果为JSON字符串，再后来得到的是数组
          if (data instanceof Array) {
            return data;
          }
          const { rows } = JSON.parse(data);
          return rows;
        },
      },
      label: intl.get(`${modelPrompt}.reservedObjectTypeDesc`).d('预留对象类型'),
      dynamicProps: {
        disabled: ({ record }) => {
          return record.get('reservedFlag') !== 'Y';
        },
        required: ({ record }) => {
          return record.get('reservedFlag') === 'Y';
        },
      },
    },
    {
      name: 'reservedObjectLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.reservedObjectCode`).d('预留对象编码'),
      lovCode: 'MT.MODEL.CUSTOMER', // 必须得有默认值，不然就会变成一个Input而不是Lov了
      lovPara: {
        tenantId,
      },
      dynamicProps: {
        disabled: ({ record }) => {
          return (
            !record.get('reservedObjectType') ||
            ['OO', 'DRIVING'].includes(record.get('reservedObjectType'))
          );
        },
        required: ({ record }) => {
          return (
            record.get('reservedObjectType') &&
            !['OO', 'DRIVING'].includes(record.get('reservedObjectType'))
          );
        },
        lovCode: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'MT.MODEL.CUSTOMER';
            case 'WO':
              return `${BASIC.LOV_CODE_BEFORE}.WORK_ORDER`;
            case 'EO':
              return `${BASIC.LOV_CODE_BEFORE}.EO`;
            case 'PO_LINE':
              return `${BASIC.LOV_CODE_BEFORE}.MES.PO_LINE`;
            default:
              return '';
          }
        },
        textField: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'customerCode';
            case 'WO':
              return 'workOrderNum';
            case 'EO':
              return 'eoNum';
            case 'PO_LINE':
              return 'poNumberAndLine';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'reservedObjectId',
      bind: '',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'reservedObjectLov.customerId';
            case 'WO':
              return 'reservedObjectLov.workOrderId';
            case 'EO':
              return 'reservedObjectLov.eoId';
            case 'PO_LINE':
              return 'reservedObjectLov.poLineId';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'reservedObjectCode',
      dynamicProps: {
        bind: ({ record }) => {
          switch (record.get('reservedObjectType')) {
            case 'CUSTOMER':
              return 'reservedObjectLov.customerCode';
            case 'WO':
              return 'reservedObjectLov.workOrderNum';
            case 'EO':
              return 'reservedObjectLov.eoNum';
            case 'PO_LINE':
              return 'reservedObjectLov.poNumberAndLine';
            default:
              return '';
          }
        },
      },
    },
    {
      name: 'customerLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerCode`).d('客户编码'),
      lovCode: 'MT.MODEL.CUSTOMER',
      lovPara: {
        tenantId,
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'customerId',
      bind: 'customerLov.customerId',
    },
    {
      name: 'customerCode',
      bind: 'customerLov.customerCode',
    },
    {
      name: 'customerSiteLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.customerSiteCode`).d('客户地点编码'),
      lovCode: 'MT.MODEL.CUSTOMER_SITE',
      ignore: FieldIgnore.always,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('customerId');
        },
        lovPara: ({ record }) => {
          return {
            tenantId,
            customerId: record.get('customerId'),
          };
        },
      },
    },
    {
      name: 'customerSiteId',
      bind: 'customerSiteLov.customerSiteId',
    },
    {
      name: 'customerSiteDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerSiteDesc`).d('客户地点描述'),
      bind: 'customerSiteLov.description',
      disabled: true,
    },
    {
      name: 'customerDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.customerDesc`).d('客户描述'),
      bind: 'customerLov.customerName',
      disabled: true,
    },
    // 客户及预留信息 end
    // 操作信息 start
    {
      name: 'creationDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
      disabled: true,
    },
    {
      name: 'createdUsername',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdUsername`).d('创建人'),
      disabled: true,
    },
    {
      name: 'lastUpdateDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdateDate`).d('最后更新时间'),
      disabled: true,
    },
    {
      name: 'lastUpdatedUsername',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lastUpdatedUsername`).d('最后更新人'),
      disabled: true,
    },
    {
      name: 'printTimes',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
      disabled: true,
    },
    // 操作信息 end
  ],
});

export { detailDS };
