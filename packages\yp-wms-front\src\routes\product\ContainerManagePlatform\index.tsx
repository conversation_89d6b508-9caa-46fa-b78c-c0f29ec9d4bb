/**
 * @Description: 容器管理平台-列表页
 * @Author: <<EMAIL>>
 * @Date: 2022-04-06 14:25:18
 * @LastEditTime: 2023-05-18 16:07:59
 * @LastEditors: <<EMAIL>>
 */

import React, { useEffect, useMemo, useState, useRef } from 'react';
import {
  DataSet,
  Table,
  Modal,
  Button,
  Select,
  TextField,
  Icon,
} from 'choerodon-ui/pro';
import { Button as PermissionButton } from 'components/Permission';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ColumnAlign, ColumnLock, TableQueryBarType } from 'choerodon-ui/pro/lib/table/enum';
import { Badge } from 'choerodon-ui';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import moment from 'moment';
import queryString from 'querystring';
import { Header, Content } from 'components/Page';
import ExcelExport from 'components/ExcelExport';
import withProps from 'utils/withProps';
import { flow, isNil } from 'lodash';
import withCustomize from 'hzero-front-cusz/lib/components/Customize';
import { getCurrentOrganizationId } from 'utils/utils';
import { openTab } from 'utils/menuTab';
import { drawerPropsC7n, overrideTableBar } from '@components/tarzan-ui';
import { useRequest } from '@components/tarzan-hooks';
import { BASIC, API_HOST } from '@utils/config';
import notification from 'utils/notification';
import request from 'utils/request';
import { getResponse } from '@utils/utils';
import InputLovDS from '@/components/BatchInput/InputLovDS';
import LovModal from '@/components/BatchInput/LovModal';

import {
  tableDS,
  containerLoadDetailDS,
  containerHistoryTableDS,
  containerLoadHisDS,
  detailDS,
} from './stores/ListDS';
import ContainerDetailDrawer from './ContainerDetailDrawer';
import ContainerLoadDetailDrawer from './ContainerLoadDetailDrawer';
import ContainerHistoryDrawer from './ContainerHistoryDrawer';
import ContainerLoadHisDrawer from './ContainerLoadHisDrawer';
import { SaveContainer } from './services';
import axios from 'axios';

const { Option } = Select;
const modelPrompt = 'tarzan.hmes.product.containerManagePlatform';
const tenantId = getCurrentOrganizationId();

let TableHeight = 600;
let _modal
const SubjectMaintainListWms = (props: any) => {
  const {
    tableDs,
    match: { path },
    customizeForm,
    customizeTable,
  } = props;
  const modal = useRef<any>(null); // 当前模态框
  const inputLovDS = new DataSet(InputLovDS());
  const [inputLovFlag, setInputLovFlag] = useState('');
  const [inputLovTitle, setInputLovTitle] = useState('');
  const [inputLovVisible, setInputLovVisible] = useState(false);

  const onOpenInputModal = (inputLovVisible, inputLovFlag, inputLovTitle) => {
    setInputLovFlag(inputLovFlag);
    setInputLovTitle(inputLovTitle);
    setInputLovVisible(inputLovVisible);
    if (inputLovVisible) {
      inputLovDS.queryDataSet?.current?.getField('code')?.set('label', inputLovTitle);
    } else {
      inputLovDS.queryDataSet?.current?.set('code', '');
      inputLovDS.data = [];
      // handleSearch()
    }
  };


  useEffect(() => {
    tableDs.query();
    TableHeight = window.screen.height - 460;
  }, []);

  const containerLoadDetailDs = useMemo(() => new DataSet(containerLoadDetailDS()), []);
  const containerHistoryTableDs = useMemo(() => new DataSet(containerHistoryTableDS()), []);
  const containerLoadHisDs = useMemo(() => new DataSet(containerLoadHisDS()), []);
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const printTypeDs = useMemo(() => new DataSet({
    fields: [
      {
        name: 'type',
        required: true,
        type: FieldType.string,
        textField: 'value',
        valueField: 'meaning',
        label: intl.get(`${modelPrompt}.printType`).d('打印类型'),
      },
    ],
  }), []);

  const [selectedMaterialLotList, setSelectedMaterialLotList] = useState<any[]>([]);
  const [selectedContainerCodeList, setSelectedContainerCodeList] = useState<any[]>([]);
  const [createBtnDisplay, setBtnDisplay] = useState<boolean>(false);

  const saveContainer = useRequest(SaveContainer(), {
    manual: true,
    needPromise: true,
  });
  // 查询是否新建按钮相关配置
  const { run: queryBtnConfig } = useRequest({ lovCode: 'WMS.CONTAINER_BUTTON' }, { manual: true, needPromise: true });

  useEffect(() => {
    listener(true);
    handleInitBtnConfig();
    return function clean() {
      listener(false);
      tableDs.clearCachedRecords();
    };
  }, []);

  const handleInitBtnConfig = async () => {
    const res = await queryBtnConfig({});
    if (res?.length && res[0].value === 'Y') {
      setBtnDisplay(true);
    }
  };

  const listener = flag => {
    // 列表交互监听
    if (tableDs) {
      const handerQuery = flag
        ? tableDs.queryDataSet.addEventListener
        : tableDs.queryDataSet.removeEventListener;
      const handler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // 查询条件更新时操作
      handerQuery.call(tableDs.queryDataSet, 'update', handleQueryDataSetUpdate);
      handler.call(tableDs, 'load', handleDataSetSelectUpdate);
      // 头选中和撤销选中事件
      handler.call(tableDs, 'select', handleDataSetSelectUpdate);
      handler.call(tableDs, 'unSelect', handleDataSetSelectUpdate);
      handler.call(tableDs, 'selectAll', handleDataSetSelectUpdate);
      handler.call(tableDs, 'unSelectAll', handleDataSetSelectUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = ({ name, record }) => {
    if (name === 'ownerType') {
      record.set('ownerLov', {});
    }
    if (name === 'reservedObjectType') {
      record.set('reservedObjectLov', {});
    }
  };

  // 处理选中条
  const handleDataSetSelectUpdate = () => {
    const _containerIdList: string[] = [];
    const _containerCodeList: string[] = [];
    tableDs.selected.forEach(item => {
      const { containerId, containerCode } = item.toData();
      _containerIdList.push(containerId);
      _containerCodeList.push(containerCode);
    });
    setSelectedMaterialLotList(_containerIdList);
    setSelectedContainerCodeList(_containerCodeList);
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'identification',
        align: ColumnAlign.left,
        width: 250,
        lock: ColumnLock.left,
        renderer: ({ value, record }) => {
          return (
            <a
              onClick={() => {
                editContainer(record);
              }}
            >
              {value}
            </a>
          );
        },
      },
      {
        name: 'containerTypeCode',
        width: 250,
      },
      {
        name: 'containerClassificationDesc',
        width: 120,
        align: ColumnAlign.center,
      },
      {
        name: 'statusDesc',
        width: 120,
        align: ColumnAlign.center,
      },
      {
        name: 'siteCode',
        width: 250,
      },
      {
        name: 'locatorCode',
        width: 250,
      },
      {
        name: 'materialCode',
        width: 250,
      },
      {
        name: 'materialName',
        width: 150,
      },
      {
        name: 'primaryUomQty',
      },
      {
        name: 'specifiedLevel',
      },
      {
        name: 'lastLoadTime',
        align: ColumnAlign.center,
        width: 180,
      },
      {
        name: 'lastUnloadTime',
        align: ColumnAlign.center,
        width: 180,
      },
      {
        name: 'ownerTypeDesc',
        width: 150,
        renderer: ({ value }) => value || intl.get(`tarzan.common.ownerType`).d('自有'),
      },
      {
        name: 'ownerCode',
        width: 250,
      },
      {
        name: 'reservedFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get('tarzan.common.label.yes').d('是')
                : intl.get('tarzan.common.label.no').d('否')
            }
          >
            { }
          </Badge>
        ),
      },
      {
        name: 'reservedObjectTypeDesc',
        width: 120,
      },
      {
        name: 'reservedObjectCode',
        width: 250,
      },
      {
        name: 'currentContainerCode',
        width: 250,
      },
      {
        name: 'topContainerCode',
        width: 250,
      },
      {
        name: 'printTimes',
        width: 120,
        renderer: ({ value }) => value || 0,
      },
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'createdByName',
        width: 250,
      },
      {
        name: 'lastUpdateDate',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'lastUpdatedByName',
        width: 250,
      },
      {
        name: 'containerCode',
        width: 250,
      },
      {
        name: 'containerName',
        width: 250,
      },
      {
        name: 'description',
        width: 250,
      },
      {
        header: intl.get('tarzan.common.label.action').d('操作'),
        align: ColumnAlign.center,
        width: 240,
        lock: ColumnLock.right,
        renderer: ({ record }) => {
          return (
            <span className="action-link">
              <a
                onClick={() => {
                  openContainerLoadDetailDrawer(record);
                }}
              >
                {intl.get(`${modelPrompt}.loadDetail`).d('装载明细')}
              </a>
              <a
                onClick={() => {
                  openContainerHistoryDrawer(record);
                }}
              >
                {intl.get(`${modelPrompt}.containerHis`).d('容器历史')}
              </a>
              <a
                onClick={() => {
                  openContainerLoadHistoryDrawer(record);
                }}
              >
                {intl.get(`${modelPrompt}.loadDetailHis`).d('装卸历史')}
              </a>
            </span>
          );
        },
      },
    ];
  }, []);

  const openContainerLoadDetailDrawer = async record => {
    containerLoadDetailDs.setQueryParameter('containerId', record.get('containerId'));
    await containerLoadDetailDs.query();
    containerLoadDetailDs.loadData(
      containerLoadDetailDs.toData().map((item: any) => {
        return { ...item, uuid: `${item.loadObjectId}${item.loadObjectType}` };
      }),
    );
    Modal.open({
      ...drawerPropsC7n({
        ds: containerLoadDetailDs,
        canEdit: false,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.loadDetail`).d('装载明细'),
      style: {
        width: 1080,
      },
      children: <ContainerLoadDetailDrawer ds={containerLoadDetailDs} />,
      afterClose: () => {
        containerLoadDetailDs.loadData([]);
      },
    });
  };

  const openContainerHistoryDrawer = record => {
    containerHistoryTableDs.setQueryParameter('containerId', record.get('containerId'));
    containerHistoryTableDs.setQueryParameter('customizeUnitCode', `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.HISTORY`);
    containerHistoryTableDs.query();
    Modal.open({
      ...drawerPropsC7n({
        ds: containerHistoryTableDs,
        canEdit: false,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.containerHistoryDrawer`).d('历史查询'),
      drawer: true,
      style: {
        width: 1080,
      },
      children: <ContainerHistoryDrawer ds={containerHistoryTableDs} customizeTable={customizeTable} />,
      afterClose: () => {
        containerHistoryTableDs.loadData([]);
      },
    });
  };

  const openContainerLoadHistoryDrawer = record => {
    containerLoadHisDs.setQueryParameter('containerId', record.get('containerId'));
    containerLoadHisDs.query();
    Modal.open({
      ...drawerPropsC7n({
        ds: containerLoadHisDs,
        canEdit: false,
      }),
      key: Modal.key(),
      title: intl.get(`${modelPrompt}.containerLoadHistoryDrawer`).d('装卸历史查询'),
      drawer: true,
      style: {
        width: 1080,
      },
      children: <ContainerLoadHisDrawer ds={containerLoadHisDs} />,
      afterClose: () => {
        containerLoadHisDs.loadData([]);
      },
    });
  };

  const onHandleCorrect = () => {
    _modal.update({
      title: <div >
        <Button color={ButtonColor.primary}
          style={{
            float: 'right',
            marginRight: '10px'
          }} onClick={async()=>handleDrawerSave()}>{intl.get(`${modelPrompt}.title.save`).d('保存')}</Button>
        {intl.get(`${modelPrompt}.editContainer`).d('编辑容器')}
      </div>,
      okButton: false,
      children: <ContainerDetailDrawer correctEdit={true} canEdit={true} ds={detailDs} createFlag={false} customizeForm={customizeForm} />,
    })
  }

  const editContainer = (record?) => {
    let createFlag = true;
    if (!record) {
      detailDs.create({ dateFrom: moment(moment().format('YYYY-MM-DD HH:mm:ss')) });
    } else {
      detailDs.loadData([record.toData()]);
      createFlag = false;
    }
    detailDs.reset();
    const canEdit = true;
    _modal = Modal.open({
      ...drawerPropsC7n({ canEdit, detailDs }),
      key: Modal.key(),
      title: createFlag
        ? intl.get(`${modelPrompt}.createContainer`).d('新建容器')
        : <>
          {intl.get(`${modelPrompt}.editContainer`).d('编辑容器')}
          <PermissionButton
            type="c7n-pro"
            icon="edit"
            color={ButtonColor.primary}
            style={{
              float: 'right',
              marginRight: '36px'
            }}
            permissionList={[
              {
                code: `${path}.button.correct`,
                type: 'button',
                meaning: '容器管理平台-修正',
              },
            ]}
            onClick={() => onHandleCorrect()}
          >{intl.get(`${modelPrompt}.title.correct`).d('修正')}
          </PermissionButton>
        </>,
      closable: false,
      style: {
        width: 720,
      },
      children: <ContainerDetailDrawer correctEdit={false} canEdit={canEdit} ds={detailDs} createFlag={createFlag} customizeForm={customizeForm} />,
      afterClose: () => {
        detailDs.loadData([]);
      },
      onOk: handleDrawerConfirm,
    });
  };

  const handleDrawerSave = async () => {
    const validate = await detailDs.validate();
    if (!validate) {
      return false;
    }
    const result = detailDs!.current!.toData();
    delete result.topContainerCode
    delete result.currentContainerCode
    const url = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/mt-container/correct/ui`
    const res: any =await axios.post(url, result)
    if (res && res.success) {
      notification.success({});
      tableDs.query();
      _modal.close()
    } else {
      notification.error({message:res.message});
      return false;
    }
  };

  const handleDrawerConfirm = async () => {
    const validate = await detailDs.validate();
    if (!validate) {
      return false;
    }
    const result = detailDs!.current!.toData();
    return saveContainer
      .run({
        params: {
          ...result,
          reservedObjectType: result.reservedObjectType || '',
        },
      })
      .then(res => {
        if (res && res.success) {
          notification.success({});
          tableDs.query();
          _modal.close()
        } else {
          return Promise.resolve(false);
        }
      });
  };

  const goImport = () => {
    openTab({
      key: '/himp/commentImport/MT.WMS.CONTAINER_TEMPLATE',
      title: 'hzero.common.title.templateImport',
      search: queryString.stringify({
        title: 'hzero.common.title.templateImport',
        action: 'himp.commentImport.view.button.templateImport',
        tenantId,
        prefixPatch: '',
        templateType: 'C',
      }),
    });
  };

  const _renderContent = () => (
    <Select
      placeholder={intl.get(`${modelPrompt}.placeholder.select`).d("请选择")}
      dataSet={printTypeDs}
      name="type"
    >
      <Option value="SUPPLIER">{intl.get(`${modelPrompt}.supplierName`).d("供应商")}</Option>
      <Option value="SELF">{intl.get(`${modelPrompt}.self`).d("自有")}</Option>
      <Option value="INCOMING">{intl.get(`${modelPrompt}.incoming`).d("来料")}</Option>
    </Select>
  );

  const cancelModal = () => {
    modal.current = null;
  };

  const closeModal = () => {
    modal.current.close();
    cancelModal();
  };

  const submitData = async () => {
    if (printTypeDs.current?.toJSONData()?.type) {
      closeModal();
      const result = await request(
        `${BASIC.HMES_BASIC
        }/v1/${getCurrentOrganizationId()}/wmsContainer/print/pdf?type=${printTypeDs.current?.toJSONData()?.type}`,
        {
          method: 'POST',
          responseType: 'blob',
          body: selectedMaterialLotList,
        },
      );
      const res = getResponse(result);
      if (res) {
        if (res.type === 'application/json') {
          const fileReader: any = new FileReader();
          fileReader.onloadend = () => {
            const jsonData = JSON.parse(fileReader.result);
            // 普通对象，读取信息
            getResponse(jsonData);
          };
          fileReader.readAsText(res);
        } else {
          const file = new Blob([res], { type: 'application/pdf' });
          const fileURL = URL.createObjectURL(file);
          const newwindow = window.open(fileURL, 'newwindow');
          if (newwindow) {
            newwindow.print();
            notification.success({
              message: intl.get(`${modelPrompt}.notification.print.success`).d('打印成功'),
            });
          } else {
            notification.error({ message: intl.get(`${modelPrompt}.notification.browser.config`).d('当前窗口已被浏览器拦截，请手动设置浏览器！') });
          }
        }
      }
    }

  };

  const handlePrint = async () => {
    modal.current = Modal.open({
      title: intl.get(`${modelPrompt}.modal.title`).d('选择的打印类型'),
      style: { width: '400px' },
      destroyOnClose: false,
      closable: true,
      onClose: cancelModal,
      footer: (
        <div>
          <Button onClick={closeModal}>{intl.get(`tarzan.common.button.cancel`).d('取消')}</Button>
          <Button color={ButtonColor.primary} onClick={submitData}>
            {intl.get(`tarzan.common.button.confirm`).d('确定')}
          </Button>
        </div>
      ),
      children: _renderContent(),
    });
  }

  // 同步AGV
  const handleSyncAgv = async () => {
    const res = getResponse(await request(
      `${BASIC.HMES_BASIC
      }/v1/${getCurrentOrganizationId()}/wmsContainer/sync/Container`,
      {
        method: 'POST',
        body: selectedContainerCodeList,
      },
    ));
    if (res) {
      notification.success({
        message: intl.get(`${modelPrompt}.notification.sync.success`).d('同步成功'),
      });
      tableDs.query();
    }
  };

  const getExportQueryParams = () => {
    if (!tableDs.queryDataSet || !tableDs.queryDataSet.current) {
      return {};
    }
    const containerIdList: any = (tableDs?.selected || []).map((record) => record?.get('containerId'))
    const queryParmas = {
      ...tableDs.queryDataSet.current.toData(),
      containerIds: containerIdList.join(','),
    }
    Object.keys(queryParmas).forEach(i => {
      if (isNil(queryParmas[i])) {
        delete queryParmas[i];
      }
    })
    return queryParmas;
  };

  const lovModalProps = {
    inputLovDS,
    inputLovFlag,
    inputLovTitle,
    inputLovVisible,
    targetDS: tableDs,
    onOpenInputModal,
  };

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.containerManagePlatform`).d('容器管理平台')}>
        {createBtnDisplay && (
          <PermissionButton
            type="c7n-pro"
            color={ButtonColor.primary}
            icon="add"
            onClick={() => {
              editContainer();
            }}
            permissionList={[
              {
                code: `/hwms/product/material-lot-traceability/list.button.edit`,
                type: 'button',
                meaning: '列表页-编辑新建删除复制按钮',
              },
            ]}
          >
            {intl.get('tarzan.common.button.create').d('新建')}
          </PermissionButton>
        )}
        <PermissionButton
          type="c7n-pro"
          permissionList={[
            {
              code: `${path}.button.edit`,
              type: 'button',
              meaning: '详情页-编辑新建删除复制按钮',
            },
          ]}
          icon="file_upload"
          onClick={goImport}
        >
          {intl.get('tarzan.common.button.import').d('导入')}
        </PermissionButton>
        {/* <FRPrintButton
          kid="CONTAINER"
          queryParams={selectedMaterialLotList}
          disabled={!selectedMaterialLotList.length}
          printObjectType="CONTAINER"
        /> */}
        <Button disabled={!selectedMaterialLotList.length}
          onClick={handlePrint}>
          {intl.get('tarzan.common.button.print').d('打印')}
        </Button>
        <Button disabled={!selectedMaterialLotList.length}
          onClick={handleSyncAgv}>
          {intl.get(`${modelPrompt}.button.syncAgv`).d('同步AGV')}
        </Button>
        <ExcelExport
          method="GET"
          exportAsync
          requestUrl={`${API_HOST}${BASIC.HMES_BASIC}/v1/${tenantId}/mt-container/list/export/ui`}
          queryParams={getExportQueryParams}
          buttonText={intl.get(`${modelPrompt}.export`).d('导出')}
        />
      </Header>
      <Content>
        {customizeTable(
          {
            // filterCode: `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.QUERY`,
            code: `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.LIST`,
          },
          <Table
            queryBar={overrideTableBar}
            queryFields={{
              autoCodes: (
                <TextField
                  name="autoCodes"
                  suffix={
                    <div className="c7n-pro-select-suffix">
                      <Icon
                        type="search"
                        onClick={() =>
                          onOpenInputModal(
                            true,
                            'autoCodes',
                            intl.get(`${modelPrompt}.autoCodes`).d('具体存储库位'),
                          )
                        }
                      />
                    </div>
                  }
                />
              ),
            }}
            queryBarProps={{
              fuzzyQuery: false,
            }}
            searchCode="rqglpt"
            customizedCode="rqglpt"
            dataSet={tableDs}
            columns={columns}
            style={{ height: TableHeight }}
            headerRowHeight={30}
            rowHeight={28}
            footerRowHeight={20}
            virtual
            virtualCell
            pagination={{
              showPager: true, // 显示数字按钮
              pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
            }}
          />,
        )}
        <LovModal {...lovModalProps} />
      </Content>
    </div>
  );
};

export default flow(
  withProps(
    () => {
      const tableDs = new DataSet({
        ...tableDS(),
      });
      return {
        tableDs,
      };
    },
    // cacheState: 是否缓存数据
    // cleanWhenClose: 关闭 tabs 时,是否自动清空缓存
    // keepOriginDataSet: 是否保持原来的 DataSet 对象，不保留的话，会根据缓存的数据重查
    { cacheState: true, cleanWhenClose: true, keepOriginDataSet: false },
  ),
  withCustomize({ unitCode: [`${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.QUERY`, `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.LIST`, `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.CREATE`, `${BASIC.CUSZ_CODE_BEFORE}.CONTAINER_LIST.HISTORY`] }),
  formatterCollections({ code: ['tarzan.hmes.product.containerManagePlatform', 'tarzan.common'] }),
)(SubjectMaintainListWms);
