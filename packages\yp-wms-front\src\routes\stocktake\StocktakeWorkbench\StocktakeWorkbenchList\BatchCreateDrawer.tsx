/**
 * @Description: 盘点工作台-列表页-批量新增抽屉
 * @Author: <<EMAIL>>
 * @Date: 2022-02-10 17:32:16
 * @LastEditTime: 2022-02-16 11:32:04
 * @LastEditors: <<EMAIL>>
 */

import React, { useState, useMemo, useEffect } from 'react';
import { Form, TextField, Lov, Select, Table, Button } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ViewMode } from 'choerodon-ui/pro/lib/lov/enum';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';

const modelPrompt = 'tarzan.hmes.stocktake.stocktakeWorkbench';

export default ({ formDs, tableDs }) => {
  const [selectedSiteId, setSelectedSiteId] = useState<number | null>(null);
  const [selectedLocatorType, setSelectedLocatorType] = useState<'locator' | 'warehouse' | null>(
    null,
  );
  const [selectedTableLineIds, setSelectedTableLineIds] = useState<number[]>([]);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  // 生成行列表DS查询项
  const listener = flag => {
    // 列表交互监听
    if (tableDs) {
      const handler = flag ? tableDs.addEventListener : tableDs.removeEventListener;
      // 头选中和撤销选中事件
      handler.call(tableDs, 'batchSelect', handleLineTableChange);
      handler.call(tableDs, 'batchUnSelect', handleLineTableChange);
    }
  };

  // 行列表事件, 更新选中行数量
  const handleLineTableChange = ({ dataSet }) => {
    const _selectedTableLineIds: number[] = [];
    dataSet.selected.forEach(item => {
      _selectedTableLineIds.push(item.toData().locatorId);
    });
    setSelectedTableLineIds(_selectedTableLineIds);
  };

  const columns: ColumnProps[] = useMemo(
    () => [
      {
        name: 'locatorCode',
        title:
          selectedLocatorType === 'warehouse'
            ? intl.get(`${modelPrompt}.tableWarehouseCode`).d('仓库编码')
            : intl.get(`${modelPrompt}.tableLocatorCode`).d('库位编码'),
      },
      {
        name: 'locatorName',
        title:
          selectedLocatorType === 'warehouse'
            ? intl.get(`${modelPrompt}.tableWarehouseDesc`).d('仓库描述')
            : intl.get(`${modelPrompt}.tableLocatorDesc`).d('库位描述'),
      },
      {
        name: 'typeDesc',
        title:
          selectedLocatorType === 'warehouse'
            ? intl.get(`${modelPrompt}.tableWarehouseTypeDesc`).d('仓库类型描述')
            : intl.get(`${modelPrompt}.tableLocatorTypeDesc`).d('库位类型描述'),
      },
    ],
    [selectedLocatorType],
  );

  const handleChangeSite = values => {
    if (values) {
      setSelectedSiteId(values.siteId);
    } else {
      setSelectedSiteId(null);
    }
    formDs.current!.init('tableWarehouseRangeLov', undefined);
    formDs.current!.init('tableLocatorRangeLov', undefined);
    setSelectedLocatorType(null);
    setSelectedTableLineIds([]);
    tableDs.loadData([]);
  };

  const handleSelectedRange = (locatorType: 'warehouse' | 'locator', values) => {
    setSelectedLocatorType(locatorType);
    tableDs.batchUnSelect(tableDs.selected);
    setSelectedTableLineIds([]);
    tableDs.loadData([]);
    values.forEach(item => {
      tableDs.create(item);
    });
  };

  const handleDeleteTableLine = async () => {
    // 删除 仓库范围/库位范围 Lov中记录的已选id
    const _selectedIds = tableDs.selected.map(item => {
      return item.toData().locatorId;
    });
    if (selectedLocatorType === 'warehouse') {
      // 删除仓库范围Lov的
      const _tableWarehouseRangeLov = formDs
        .current!.toData()
        .tableWarehouseRangeLov.filter(item => {
          return !_selectedIds.includes(item.locatorId);
        });
      formDs.current!.set('tableWarehouseRangeLov', _tableWarehouseRangeLov);
    } else {
      // 删除库位范围Lov的
      const _tableLocatorRangeLov = formDs.current!.toData().tableLocatorRangeLov.filter(item => {
        return !_selectedIds.includes(item.locatorId);
      });
      formDs.current!.set('tableLocatorRangeLov', _tableLocatorRangeLov);
    }
    // 删除下方表格的记录
    await tableDs.delete(tableDs.selected, false);
    setSelectedTableLineIds([]);
    tableDs.batchUnSelect(tableDs.selected);
    if (!tableDs.toData().length) {
      // 表格数据都删完了
      setSelectedLocatorType(null);
    }
  };

  return (
    <>
      <Form dataSet={formDs} columns={3} labelWidth={112}>
        <Lov name="siteLov" onChange={handleChangeSite} />
        <Select name="openFlag" />
        <TextField name="remark" />
      </Form>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button disabled={!selectedTableLineIds.length} onClick={handleDeleteTableLine}>
          {intl.get('tarzan.common.button.delete').d('删除')}
        </Button>
        <Lov
          disabled={selectedLocatorType === 'locator'}
          color={
            (selectedSiteId && !selectedLocatorType) || selectedLocatorType === 'warehouse'
              ? ButtonColor.primary
              : ButtonColor.gray
          }
          dataSet={formDs}
          name="tableWarehouseRangeLov"
          mode={ViewMode.button}
          clearButton={false}
          onChange={values => handleSelectedRange('warehouse', values)}
        >
          {intl.get(`${modelPrompt}.tableWarehouseRange`).d('仓库范围')}
        </Lov>
        <Lov
          disabled={selectedLocatorType === 'warehouse'}
          color={
            (selectedSiteId && !selectedLocatorType) || selectedLocatorType === 'locator'
              ? ButtonColor.primary
              : ButtonColor.gray
          }
          dataSet={formDs}
          name="tableLocatorRangeLov"
          mode={ViewMode.button}
          clearButton={false}
          onChange={values => handleSelectedRange('locator', values)}
        >
          {intl.get(`${modelPrompt}.tableLocatorRange`).d('库位范围')}
        </Lov>
      </div>
      {selectedSiteId && selectedLocatorType && (
        <Table dataSet={tableDs} columns={columns} pagination={false} />
      )}
    </>
  );
};
